/**
 * TypeScript interfaces for ButToanDieuChinhGiamCongNo (Debt Reduction Adjustment) model
 *
 * This interface represents the structure of the ButToanDieuChinhGiamCongNo model from the backend.
 * It defines debt reduction adjustments and their details in the system.
 */

import {
  AccountModel,
  <PERSON><PERSON>en<PERSON>hung<PERSON>,
  ChungTu,
  Ngoai<PERSON>e,
  HanThanh<PERSON>oan,
  Khach<PERSON>,
  BoPhan,
  VuViec,
  Contract,
  DotThanhToan,
  KheUoc,
  Phi,
  ChiPhi,
  VatTu
} from '@/types/schemas';
import { ApiResponse } from '../api.type';

export interface ButToanDieuChinhGiamCongNo {
  /**
   * Unique identifier (UUID)
   */
  uuid: string;

  /**
   * Reference to the entity model
   */
  entity_model?: string;

  /**
   * Mã ngân hàng vốn
   */
  ma_ngv?: string;

  /**
   * Diễn giải
   */
  dien_giai?: string;

  /**
   * Tài khoản
   */
  tk?: string;

  /**
   * Account data (nested object)
   */
  tk_data?: AccountModel;

  /**
   * ID đơn vị
   */
  unit_id?: number;

  /**
   * <PERSON>ố chứng từ nội bộ
   */
  i_so_ct?: string;

  /**
   * Mã nghiệp vụ
   */
  ma_nk?: string;

  /**
   * Business operation data (nested object)
   */
  ma_nk_data?: QuyenChungTu;

  /**
   * Số chứng từ
   */
  so_ct?: string;

  /**
   * Document data (nested object)
   */
  so_ct_data?: ChungTu;

  /**
   * Ngày chứng từ
   */
  ngay_ct: string;

  /**
   * Ngày lập chứng từ
   */
  ngay_lct?: string;

  /**
   * Mã ngoại tệ
   */
  ma_nt?: string;

  /**
   * Foreign currency data (nested object)
   */
  ma_nt_data?: NgoaiTe;

  /**
   * Tỷ giá
   */
  ty_gia?: string | number;

  /**
   * Trạng thái
   */
  status?: string;

  /**
   * Đã chuyển
   */
  transfer_yn?: boolean;

  /**
   * Hợp đồng
   */
  hd_yn?: boolean;

  /**
   * Số chứng từ gốc
   */
  so_ct0?: string;

  /**
   * Original document data (nested object)
   */
  so_ct0_data?: ChungTu;

  /**
   * Ngày chứng từ gốc
   */
  ngay_ct0?: string;

  /**
   * Mã thanh toán
   */
  ma_tt?: string;

  /**
   * Payment term data (nested object)
   */
  ma_tt_data?: HanThanhToan;

  /**
   * Thời gian đáo hạn
   */
  tg_dd?: string;

  /**
   * Có lãi trễ
   */
  cltg_yn?: boolean;

  /**
   * Mã khách hàng
   */
  ma_kh?: string;

  /**
   * Customer data (nested object)
   */
  ma_kh_data?: KhachHang;

  /**
   * Số chứng từ gốc
   */
  so_ct_goc?: string;

  /**
   * Original document data (nested object)
   */
  so_ct_goc_data?: ChungTu;

  /**
   * Diễn giải chứng từ gốc
   */
  dien_giai_ct_goc?: string;

  /**
   * Tổng tiền ngoại tệ
   */
  t_tien_nt?: string | number;

  /**
   * Tổng tiền
   */
  t_tien?: string | number;

  /**
   * Chi tiết bút toán điều chỉnh giảm công nợ
   */
  chi_tiet_data?: ChiTietButToanDieuChinhGiamCongNo[];

  /**
   * Timestamp of creation
   */
  created?: string;

  /**
   * Timestamp of last update
   */
  updated?: string;
}

export interface ChiTietButToanDieuChinhGiamCongNo {
  /**
   * Unique identifier (UUID)
   */
  uuid: string;

  /**
   * Reference to parent ButToanDieuChinhGiamCongNo
   */
  but_toan_dieu_chinh_giam_cong_no: string;

  /**
   * Line number
   */
  line?: number;

  /**
   * Mã khách hàng
   */
  ma_kh?: string;

  /**
   * Customer data (nested object)
   */
  ma_kh_data?: KhachHang;

  /**
   * Contract ID
   */
  id_hd?: string;

  /**
   * Tài khoản có
   */
  tk_co?: string;

  /**
   * Credit account data (nested object)
   */
  tk_co_data?: AccountModel;

  /**
   * Tỷ giá hợp đồng
   */
  ty_gia_hd?: string | number;

  /**
   * Tỷ giá 2
   */
  ty_gia2?: string | number;

  /**
   * Tiền ngoại tệ
   */
  tien_nt?: string | number;

  /**
   * Tiền
   */
  tien?: string | number;

  /**
   * Diễn giải
   */
  dien_giai?: string;

  /**
   * Mã bộ phận
   */
  ma_bp?: string;

  /**
   * Department data (nested object)
   */
  ma_bp_data?: BoPhan;

  /**
   * Mã vụ việc
   */
  ma_vv?: string;

  /**
   * Case data (nested object)
   */
  ma_vv_data?: VuViec;

  /**
   * Mã hợp đồng
   */
  ma_hd?: string;

  /**
   * Contract data (nested object)
   */
  ma_hd_data?: Contract;

  /**
   * Mã đợt thanh toán
   */
  ma_dtt?: string;

  /**
   * Payment period data (nested object)
   */
  ma_dtt_data?: DotThanhToan;

  /**
   * Mã khoản ước
   */
  ma_ku?: string;

  /**
   * Loan data (nested object)
   */
  ma_ku_data?: KheUoc;

  /**
   * Mã phí
   */
  ma_phi?: string;

  /**
   * Fee data (nested object)
   */
  ma_phi_data?: Phi;

  /**
   * Mã sản phẩm
   */
  ma_sp?: string;

  /**
   * Product data (nested object)
   */
  ma_sp_data?: VatTu;

  /**
   * Mã lệnh sản xuất
   */
  ma_lsx?: string;

  /**
   * Mã chi phí
   */
  ma_cp0?: string;

  /**
   * Cost data (nested object)
   */
  ma_cp0_data?: ChiPhi;

  /**
   * ID thanh toán
   */
  id_tt?: string;

  /**
   * Timestamp of creation
   */
  created?: string;

  /**
   * Timestamp of last update
   */
  updated?: string;
}

export type ButToanDieuChinhGiamCongNoResponse = ApiResponse<ButToanDieuChinhGiamCongNo>;

export interface ButToanDieuChinhGiamCongNoInput {
  ma_ngv?: string;
  dien_giai?: string;
  tk?: string;
  unit_id?: number;
  i_so_ct?: string;
  ma_nk?: string;
  so_ct?: string;
  ngay_ct: string;
  ngay_lct?: string;
  ma_nt?: string;
  ty_gia?: string | number;
  status?: string;
  transfer_yn?: boolean;
  hd_yn?: boolean;
  so_ct0?: string;
  ngay_ct0?: string;
  ma_tt?: string;
  tg_dd?: string;
  cltg_yn?: boolean;
  ma_kh?: string;
  so_ct_goc?: string;
  dien_giai_ct_goc?: string;
  t_tien_nt?: string | number;
  t_tien?: string | number;
  chitiet_data?: ChiTietButToanDieuChinhGiamCongNoInput[];
}

export interface ChiTietButToanDieuChinhGiamCongNoInput {
  line?: number;
  ma_kh?: string;
  id_hd?: string;
  tk_co?: string;
  ty_gia_hd?: string | number;
  ty_gia2?: string | number;
  tien_nt?: string | number;
  tien?: string | number;
  dien_giai?: string;
  ma_bp?: string;
  ma_vv?: string;
  ma_hd?: string;
  ma_dtt?: string;
  ma_ku?: string;
  ma_phi?: string;
  ma_sp?: string;
  ma_lsx?: string;
  ma_cp0?: string;
  id_tt?: string;
}

export interface ButToanDieuChinhGiamCongNoSearchFormValues {
  ngay_ct1?: Date;
  ngay_ct2?: Date;
  so_ct?: string;
  ma_kh?: string;
  tk?: string;
  ma_nt?: string;
  status?: string;
  transfer_yn?: string;
  hd_yn?: string;
  [key: string]: any;
}
