/**
 * TypeScript interface for KenhBanHang (Sales Channel) model
 *
 * This interface represents the structure of the KenhBanHang model from the backend.
 * It defines sales channels used for order and sales management.
 */

import { ApiResponse } from '@/types/api.type';
import { Group } from './group.type';

/**
 * Interface for KenhBanHang (Sales Channel) model
 */
export interface KenhBanHang {
  /**
   * Unique identifier (UUID)
   */
  uuid: string;

  /**
   * Reference to the entity model
   */
  entity_model?: string;

  /**
   * Sales channel code (maps to ma_kbh in backend)
   */
  ma_kbh: string;

  /**
   * Name of the sales channel (maps to ten_kbh in backend)
   */
  ten_kbh?: string;

  /**
   * Reference to the order source UUID (maps to ma_nguondon in backend)
   */
  ma_nguondon?: string | null;

  /**
   * Order source data object (read-only)
   */
  ma_nguondon_data?: any;

  /**
   * Name of the order source (maps to ten_nguondon in backend)
   */
  ten_nguondon?: string | null;

  /**
   * Reference to the payment method UUID (maps to ma_pttt in backend)
   */
  ma_pttt?: string | null;

  /**
   * Payment method data object (read-only)
   */
  ma_pttt_data?: any;

  /**
   * Reference to the payment form UUID (maps to ma_httt in backend)
   */
  ma_httt?: string | null;

  /**
   * Payment form data object (read-only)
   */
  ma_httt_data?: any;

  /**
   * Store code (maps to ma_cuahang in backend)
   */
  ma_cuahang?: string | null;

  /**
   * Commission rate percentage (maps to tl_hoahong in backend)
   */
  tl_hoahong: number;

  /**
   * Channel type (maps to loai_kenh in backend)
   * 1 - Direct, 2 - Agency, 3 - Online, etc.
   */
  loai_kenh: number;

  /**
   * Status indicator (0=inactive, 1=active)
   */
  status: number;

  /**
   * Timestamp of creation
   */
  created: string;

  /**
   * Timestamp of last update
   */
  updated: string;
}

/**
 * Type for KenhBanHang API response
 */
export type KenhBanHangResponse = ApiResponse<KenhBanHang>;

/**
 * Type for creating or updating a KenhBanHang
 */
export interface KenhBanHangInput {
  /**
   * Sales channel code (maps to ma_kbh in backend)
   */
  ma_kbh: string;

  /**
   * Name of the sales channel (maps to ten_kbh in backend)
   */
  ten_kbh?: string;

  /**
   * Reference to the order source UUID (maps to ma_nguondon in backend)
   */
  ma_nguondon?: string | null;

  /**
   * Name of the order source (maps to ten_nguondon in backend)
   */
  ten_nguondon?: string | null;

  /**
   * Reference to the payment method UUID (maps to ma_pttt in backend)
   */
  ma_pttt?: string | null;

  /**
   * Reference to the payment form UUID (maps to ma_httt in backend)
   */
  ma_httt?: string | null;

  /**
   * Store code (maps to ma_cuahang in backend)
   */
  ma_cuahang?: string | null;

  /**
   * Commission rate percentage (maps to tl_hoahong in backend)
   */
  tl_hoahong?: number;

  /**
   * Channel type (maps to loai_kenh in backend)
   * 1 - Direct, 2 - Agency, 3 - Online, etc.
   */
  loai_kenh?: number;

  /**
   * Status indicator (0=inactive, 1=active)
   */
  status: number;
}
