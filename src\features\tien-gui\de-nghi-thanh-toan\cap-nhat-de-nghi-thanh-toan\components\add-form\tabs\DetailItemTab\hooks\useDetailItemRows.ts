import { useState, useCallback, useEffect } from 'react';

export const useDetailItemRows = (value: any[], onChange?: (newValue: any[]) => void) => {
  const [rows, setRows] = useState<any[]>([]);
  const [selectedRowUuid, setSelectedRowUuid] = useState<string | null>(null);
  const [copiedRow, setCopiedRow] = useState<any | null>(null);

  // Sync with external value
  useEffect(() => {
    const rowsWithUuid = value.map(row => ({
      ...row,
      uuid: row.uuid || String(Math.random())
    }));
    setRows(rowsWithUuid);
  }, [value]);

  // Notify parent of changes
  const updateRows = useCallback(
    (newRows: any[]) => {
      setRows(newRows);
      onChange?.(newRows);
    },
    [onChange]
  );

  const handleRowClick = useCallback((row: any) => {
    setSelectedRowUuid(row.uuid);
  }, []);

  const handleAddRow = useCallback(() => {
    const newRow = {
      uuid: String(Math.random()),
      // Budget fields
      ns_kd: '',
      ma_ns: '',
      // Entity fields
      ma_kh: '',
      ma_kh_data: null,
      ten_kh: '',
      // Amount and description
      tien: 0,
      dien_giai: '',
      // Invoice type
      ma_loai_hd: '',
      // Entity references
      ma_bp: '',
      ma_bp_data: null,
      ma_vv: '',
      ma_vv_data: null,
      ma_hd: '',
      ma_hd_data: null,
      ma_dtt: '',
      ma_dtt_data: null,
      ma_ku: '',
      ma_ku_data: null,
      ma_phi: '',
      ma_phi_data: null,
      ma_sp: '',
      ma_sp_data: null,
      ma_cp0: '',
      ma_cp0_data: null
    };
    const newRows = [...rows, newRow];
    updateRows(newRows);
    setSelectedRowUuid(newRow.uuid);
  }, [rows, updateRows]);

  const handleDeleteRow = useCallback(() => {
    if (!selectedRowUuid) return;
    const newRows = rows.filter(row => row.uuid !== selectedRowUuid);
    updateRows(newRows);
    setSelectedRowUuid(null);
  }, [rows, selectedRowUuid, updateRows]);

  const handleCopyRow = useCallback(() => {
    if (!selectedRowUuid) return;
    const rowToCopy = rows.find(row => row.uuid === selectedRowUuid);
    if (rowToCopy) {
      setCopiedRow(rowToCopy);
    }
  }, [rows, selectedRowUuid]);

  const handlePasteRow = useCallback(() => {
    if (!copiedRow) return;
    const newRow = {
      ...copiedRow,
      uuid: String(Math.random())
    };
    const newRows = [...rows, newRow];
    updateRows(newRows);
    setSelectedRowUuid(newRow.uuid);
  }, [copiedRow, rows, updateRows]);

  const handleMoveRow = useCallback(
    (direction: 'up' | 'down') => {
      if (!selectedRowUuid) return;

      const currentIndex = rows.findIndex(row => row.uuid === selectedRowUuid);
      if (currentIndex === -1) return;

      const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
      if (newIndex < 0 || newIndex >= rows.length) return;

      const newRows = [...rows];
      [newRows[currentIndex], newRows[newIndex]] = [newRows[newIndex], newRows[currentIndex]];
      updateRows(newRows);
    },
    [rows, selectedRowUuid, updateRows]
  );

  const handleCellValueChange = useCallback(
    (uuid: string, field: string, value: any) => {
      const newRows = rows.map(row => {
        if (row.uuid === uuid) {
          const updatedRow = { ...row, [field]: value };
          return updatedRow;
        }
        return row;
      });
      updateRows(newRows);
    },
    [rows, updateRows]
  );

  return {
    rows,
    selectedRowUuid,
    handleRowClick,
    handleAddRow,
    handleDeleteRow,
    handleCopyRow,
    handlePasteRow,
    handleMoveRow,
    handleCellValueChange
  };
};
