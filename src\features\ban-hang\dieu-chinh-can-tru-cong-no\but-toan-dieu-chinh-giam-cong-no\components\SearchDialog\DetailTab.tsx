import { accountSearchColumns, donViSearchColumns, khachHangSearchColumns, QUERY_KEYS } from '@/constants';
import { FormField, SearchField } from '@/components/custom/arito/';
import { DonVi, AccountModel, DoiTuong } from '@/types/schemas';
import { Label } from '@/components/ui/label';

// Define types for the props
interface DetailTabProps {
  unit?: DonVi;
  customer?: DoiTuong;
  account?: AccountModel;
  setUnit: (unit: DonVi) => void;
  setCustomer?: (customer: DoiTuong) => void;
  setAccount?: (account: AccountModel) => void;
}

const DetailTab: React.FC<DetailTabProps> = ({ unit, customer, account, setUnit, setCustomer, setAccount }) => {
  return (
    <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
      <div className='space-y-3 p-4'>
        {/* <PERSON>ã khách hàng */}
        <div className='flex items-center'>
          <Label className='flex min-w-[150px] items-center pr-2 text-left text-[13px] font-normal sm:mb-0'>
            Mã khách hàng
          </Label>
          <SearchField<DoiTuong>
            displayRelatedField='customer_name'
            columnDisplay='customer_code'
            className='w-[11.25rem]'
            searchEndpoint={`${QUERY_KEYS.KHACH_HANG}`}
            searchColumns={khachHangSearchColumns}
            dialogTitle='Danh mục khách hàng'
            value={customer?.customer_code || ''}
            onRowSelection={setCustomer}
          />
        </div>

        {/* Tài khoản có */}
        <div className='flex items-center pb-4'>
          <Label className='flex min-w-[150px] items-center pr-2 text-left text-[13px] font-normal sm:mb-0'>
            Tài khoản có
          </Label>
          <SearchField<AccountModel>
            type='text'
            displayRelatedField='name'
            columnDisplay='code'
            className='w-[11.25rem]'
            searchEndpoint={`${QUERY_KEYS.TAI_KHOAN}`}
            searchColumns={accountSearchColumns}
            dialogTitle='Danh mục tài khoản'
            value={account?.code || ''}
            onRowSelection={setAccount}
          />
        </div>

        <hr />

        {/* Đơn vị */}
        <div className='flex items-center'>
          <Label className='flex min-w-[150px] items-center pr-2 text-left text-[13px] font-normal sm:mb-0'>
            Đơn vị
          </Label>
          <SearchField<DonVi>
            type='text'
            displayRelatedField='ten_don_vi'
            columnDisplay='ma_don_vi'
            className='w-[11.25rem]'
            searchEndpoint={`${QUERY_KEYS.DON_VI}`}
            searchColumns={donViSearchColumns}
            dialogTitle='Danh mục đơn vị'
            value={unit?.ma_don_vi || ''}
            onRowSelection={setUnit}
          />
        </div>

        {/* Trạng thái */}
        <div className='flex items-center'>
          <Label className='flex min-w-[150px] items-center pr-2 text-left text-[13px] font-normal sm:mb-0'>
            Trạng thái
          </Label>
          <FormField
            name='status'
            type='select'
            className='w-[200px]'
            options={[
              { value: '', label: 'Tất cả' },
              { value: '1', label: 'Chưa ghi số' },
              { value: '2', label: 'Chờ duyệt' },
              { value: '3', label: 'Đã ghi số' },
              { value: '4', label: 'Đang thực hiện' },
              { value: '5', label: 'Hoàn thành' },
              { value: '6', label: 'Hủy' }
            ]}
          />
        </div>

        {/* Lọc theo người sd */}
        <div className='flex items-center'>
          <Label className='flex min-w-[150px] items-center pr-2 text-left text-[13px] font-normal sm:mb-0'>
            Lọc theo người sd
          </Label>
          <FormField
            name='userFilter'
            type='select'
            className='w-[200px]'
            options={[
              { value: '0', label: 'Tất cả' },
              { value: '1', label: 'Lọc theo người tạo' }
            ]}
          />
        </div>
      </div>
    </div>
  );
};

export default DetailTab;
