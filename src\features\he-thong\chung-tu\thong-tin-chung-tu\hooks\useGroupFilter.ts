import { useState } from 'react';
import { ChungTuType } from '@/types/schemas/chung-tu.type';

interface GroupType {
  label: string;
  value: ChungTuType;
}

const Group: GroupType[] = [
  { label: 'Tiền mặt', value: ChungTuType.TIEN_MAT },
  { label: 'Tiền gửi', value: ChungTuType.TIEN_GUI },
  { label: 'Bán hàng', value: ChungTuType.BAN_HANG },
  { label: 'Hóa đơn', value: ChungTuType.HOA_DON },
  { label: 'Mua hàng', value: ChungTuType.MUA_HANG },
  { label: 'Tồn kho', value: ChungTuType.TON_KHO },
  { label: 'Gi<PERSON> thành', value: ChungTuType.GIA_THANH },
  { label: 'Thuế', value: ChungTuType.THUE },
  { label: 'Tổng hợp', value: ChungTuType.TONG_HOP },
  { label: 'e-Procurement', value: ChungTuType.E_PROCUREMENT },
  { label: 'Kho', value: ChungTuType.KHO },
  { label: '<PERSON><PERSON>ân sự', value: ChungTuType.NHAN_SU },
  { label: 'Hóa đơn đầu vào', value: ChungTuType.HOA_DON_DAU_VAO }
];

export const getChungTuTypeLabel = (chungTuType: ChungTuType): string => {
  const group = Group.find(g => g.value === chungTuType);
  return group?.label || '';
};

interface UseGroupFilterReturn {
  activeGroup: ChungTuType | null;
  sidebarOpen: boolean;
  Group: GroupType[];

  handleFilter: (groupValue: ChungTuType) => void;
  toggleSidebar: () => void;
  getChungTuTypeLabel: (chungTuType: ChungTuType) => string;
}

const useGroupFilter = (): UseGroupFilterReturn => {
  // Initially no group is selected (null), which means query all data
  const [activeGroup, setActiveGroup] = useState<ChungTuType | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(true);

  const handleFilter = (groupValue: ChungTuType) => {
    setActiveGroup(groupValue);
  };

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return {
    Group,
    activeGroup,
    sidebarOpen,

    handleFilter,
    toggleSidebar,
    getChungTuTypeLabel
  };
};

export default useGroupFilter;
