import type { TaiSanCoDinh } from './tai-san-co-dinh.type';
import type { <PERSON><PERSON><PERSON><PERSON> } from './account.type';
import type { ApiResponse } from '../api.type';
import type { <PERSON><PERSON><PERSON> } from './bo-phan.type';

export interface DieuChuyenBoPhanSuDungTSCD {
  /**
   * UUID of the record
   */
  uuid: string;

  /**
   * The entity this adjustment belongs to
   */
  entity_model: string;

  /**
   * Accounting period for this adjustment
   */
  ky: number;

  /**
   * Accounting year for this adjustment
   */
  nam: number;

  /**
   * Tool information declaration this adjustment applies to
   */
  ma_ts?: string | null;

  /**
   * Full data of the tool
   */
  ma_ts_data?: TaiSanCoDinh | null;

  /**
   * Department for tool usage this adjustment applies to
   */
  ma_bp?: string | null;

  /**
   * Full data of the department
   */
  ma_bp_data?: BoPhan | null;

  /**
   * Cost center account for this adjustment
   */
  tk_ts?: string | null;
  tk_ts_data?: TaiKhoan | null;

  /**
   * Customer account for this adjustment
   */
  tk_kh?: string | null;
  tk_kh_data?: Tai<PERSON><PERSON>an | null;

  /**
   * Expense account for this adjustment
   */
  tk_cp?: string | null;
  tk_cp_data?: TaiKhoan | null;
}

export type DieuChuyenBoPhanSuDungTSCDInput = Omit<
  DieuChuyenBoPhanSuDungTSCD,
  'uuid' | 'entity_model' | 'ma_ts_data' | 'ma_bp_data' | 'tk_ts_data' | 'tk_kh_data' | 'tk_cp_data'
>;

/**
 * Type for DieuChuyenBoPhanSuDungTSCD API response
 */
export type DieuChuyenBoPhanSuDungTSCDResponse = ApiResponse<DieuChuyenBoPhanSuDungTSCD>;
