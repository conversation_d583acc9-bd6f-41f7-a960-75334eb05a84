/**
 * TypeScript interface for ChungTu (Document/Voucher) model
 *
 * This interface represents the structure of the ChungTu model from the backend.
 * It defines documents and vouchers used in the accounting system.
 */

import { ApiResponse } from '@/types/api.type';

/**
 * Enum for document status types
 */
export enum ChungTuStatusType {
  LAP_CHUNG_TU = 'lap_chung_tu',
  CHO_DUYET = 'cho_duyet',
  DANG_DUYET = 'dang_duyet',
  DA_DUYET = 'da_duyet',
  DANG_THUC_HIEN = 'dang_thuc_hien',
  HOAN_THANH = 'hoan_thanh',
  DONG = 'dong'
}

/**
 * Enum for document types
 */
export enum ChungTuType {
  TIEN_MAT = 'tien_mat',
  TIEN_GUI = 'tien_gui',
  BAN_HANG = 'ban_hang',
  HOA_DON = 'hoa_don',
  MUA_HANG = 'mua_hang',
  TON_KHO = 'ton_kho',
  GIA_THANH = 'gia_thanh',
  THUE = 'thue',
  TONG_HOP = 'tong_hop',
  E_PROCUREMENT = 'e_procurement',
  KHO = 'kho',
  NHAN_SU = 'nhan_su',
  HOA_DON_DAU_VAO = 'hoa_don_dau_vao'
}

export interface ChungTu {
  /**
   * Unique identifier (UUID)
   */
  uuid: string;

  /**
   * Reference to the entity model (ForeignKey)
   */
  entity_model: string;

  /**
   * Document code (max 10 characters)
   */
  ma_ct: string;

  /**
   * Document name (max 255 characters)
   */
  ten_ct: string;

  /**
   * Alternative document name in English (max 255 characters, optional)
   */
  ten_ct2?: string | null;

  /**
   * Alternative document name in English 2 (max 255 characters, optional)
   */
  ten_ct3?: string | null;

  /**
   * Start date for using this document type
   */
  ngay_ks: string;

  /**
   * Sort order number
   */
  stt: number;

  /**
   * Number of documents
   */
  i_so_ct: number;

  /**
   * Page count (default 0)
   */
  d_page_count: number;

  /**
   * Sort type (max 1 character, default '0')
   */
  order_type: string;

  /**
   * Default status (max 1 character, default '5')
   */
  df_status: string;

  /**
   * Document inventory check (max 1 character, default '0')
   */
  ct_kt_ton: string;

  /**
   * Inventory data type (max 1 character, default '0')
   */
  loai_dl_ton: string;

  /**
   * Whether document uses position (default False)
   */
  ct_sd_vi_tri: boolean;

  /**
   * Show creator information (default False)
   */
  user_id0_yn: boolean;

  /**
   * Show last editor information (default False)
   */
  user_id2_yn: boolean;

  /**
   * Show document creation date (default True)
   */
  ngay_lct_yn: boolean;

  /**
   * Document link (max 255 characters, optional)
   */
  vc_link?: string | null;

  /**
   * Save document log (max 1 character, default '0')
   */
  ct_save_log: string;

  /**
   * Document type category (max 255 characters, with choices)
   */
  xcode?: ChungTuType | null;

  /**
   * Nested chi tiet data (read-only from serializer)
   */
  chi_tiet_data?: ChiTietChungTu[];

  /**
   * Timestamp of creation
   */
  created: string;

  /**
   * Timestamp of last update
   */
  updated: string;
}

/**
 * Type for ChungTu API response
 */
export type ChungTuResponse = ApiResponse<ChungTu>;

/**
 * Simplified ChungTu type for use in forms and components
 */
export interface ChungTuSimple {
  /**
   * Document code
   */
  ma_ct: string;

  /**
   * Document name
   */
  ten_ct: string;
}

/**
 * Type for creating or updating a ChungTu
 */
export interface ChungTuInput {
  /**
   * Document code (max 10 characters)
   */
  ma_ct: string;

  /**
   * Document name (max 255 characters)
   */
  ten_ct: string;

  /**
   * Alternative document name in English (max 255 characters, optional)
   */
  ten_ct2?: string | null;

  /**
   * Alternative document name in English 2 (max 255 characters, optional)
   */
  ten_ct3?: string | null;

  /**
   * Start date for using this document type
   */
  ngay_ks?: string;

  /**
   * Sort order number
   */
  stt: number;

  /**
   * Number of documents
   */
  i_so_ct: number;

  /**
   * Page count (default 0)
   */
  d_page_count?: number;

  /**
   * Sort type (max 1 character, default '0')
   */
  order_type?: string;

  /**
   * Default status (max 1 character, default '5')
   */
  df_status?: string;

  /**
   * Document inventory check (max 1 character, default '0')
   */
  ct_kt_ton?: string;

  /**
   * Inventory data type (max 1 character, default '0')
   */
  loai_dl_ton?: string;

  /**
   * Whether document uses position (default False)
   */
  ct_sd_vi_tri?: boolean;

  /**
   * Show creator information (default False)
   */
  user_id0_yn?: boolean;

  /**
   * Show last editor information (default False)
   */
  user_id2_yn?: boolean;

  /**
   * Show document creation date (default True)
   */
  ngay_lct_yn?: boolean;

  /**
   * Document link (max 255 characters, optional)
   */
  vc_link?: string | null;

  /**
   * Save document log (max 1 character, default '0')
   */
  ct_save_log?: string;

  /**
   * Document type category (max 255 characters, with choices)
   */
  xcode?: ChungTuType | null;

  /**
   * Chi tiet data for creating/updating (write-only)
   */
  chi_tiet?: Partial<ChiTietChungTu>[];
}

/**
 * Type for ChungTu search parameters
 */
export interface ChungTuSearchParams {
  /**
   * Document code
   */
  ma_ct?: string;

  /**
   * Document name
   */
  ten_ct?: string;

  /**
   * Document type category
   */
  xcode?: ChungTuType;

  /**
   * Default status
   */
  df_status?: string;

  /**
   * Order type
   */
  order_type?: string;

  /**
   * Entity slug
   */
  entity_slug?: string;

  /**
   * Page number for pagination
   */
  page?: number;

  /**
   * Page size for pagination
   */
  page_size?: number;
}

/**
 * Type for ChungTu list response
 */
export type ChungTuListResponse = ApiResponse<ChungTu>;

/**
 * Interface for ChiTietChungTu (Document Detail) model
 */
export interface ChiTietChungTu {
  /**
   * Unique identifier (UUID)
   */
  uuid: string;

  /**
   * Reference to the parent ChungTu
   */
  ma_ct: string;

  /**
   * Line number
   */
  line: number;

  /**
   * Automatic data code (max 10 characters, optional)
   */
  ma_dttd?: string | null;

  /**
   * Automatic data name (max 255 characters, optional)
   */
  ten_dttd?: string | null;

  /**
   * Text content (max 255 characters, optional)
   */
  text?: string | null;

  /**
   * Secondary text content (max 255 characters, optional)
   */
  text2?: string | null;

  /**
   * Default value (max 255 characters, optional)
   */
  df_value?: string | null;

  /**
   * GL group flag (default False)
   */
  gl_group_yn: boolean;

  /**
   * Input flag (default False)
   */
  a_bb_nhap: boolean;

  /**
   * Hidden flag (default False)
   */
  a_hidden: boolean;

  /**
   * Copy flag (default False)
   */
  a_copy: boolean;

  /**
   * Active flag (default True)
   */
  a_active: boolean;

  /**
   * Default value flag (default False)
   */
  a_df_value: boolean;

  /**
   * Timestamp of creation
   */
  created: string;

  /**
   * Timestamp of last update
   */
  updated: string;
}

/**
 * Type for ChungTu with related data
 */
export interface ChungTuWithRelatedData extends ChungTu {
  /**
   * Entity model data (read-only)
   */
  entity_model_data?: any;
}
