import { Button } from '@mui/material';
import { useState } from 'react';
import { AritoDialog, AritoForm, AritoIcon, BottomBar } from '@/components/custom/arito';
import { KenhBanHang } from '@/types/schemas/kenh-ban-hang.type';
import { FormValues, formSchema } from '../schemas';
import { useSearchFieldStates } from '../hooks';
import ConfirmDialog from './ConfirmDialog';
import { FormMode } from '@/types/form';
import BasicInfo from './BasicInfo';

interface FormDialogProps {
  mode: FormMode;
  open: boolean;
  onClose: () => void;
  onSubmit: (data: FormValues) => Promise<void>;
  isSubmitting: boolean;
  error: string | null;

  onAddButtonClick?: () => void;
  onEditButtonClick?: () => void;
  onDeleteButtonClick?: () => void;
  onCopyButtonClick?: () => void;
  onWatchButtonClick?: () => void;

  initialData: FormValues;
  selectedObj?: KenhBanHang | null;
  isCopyMode?: boolean;
}

function FormDialog({
  mode,
  open,
  onClose,
  onSubmit,
  isSubmitting,
  error,
  onAddButtonClick,
  onCopyButtonClick,
  onDeleteButtonClick,
  onEditButtonClick,
  initialData,
  selectedObj,
  isCopyMode = false
}: FormDialogProps) {
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  // Initialize search field states with selectedObj for edit/view/copy modes, null for add mode
  const {
    nguonDon,
    setNguonDon,
    hinhThucThanhToan,
    setHinhThucThanhToan,
    phuongThucThanhToan,
    setPhuongThucThanhToan
  } = useSearchFieldStates(mode === 'add' && !isCopyMode ? null : selectedObj);

  const handleCloseDialog = () => {
    setShowConfirmDialog(false);
    onClose();
  };

  return (
    <>
      <AritoDialog
        open={open}
        onClose={onClose}
        title={mode === 'add' ? 'Thêm kênh bán hàng' : mode === 'edit' ? 'Sửa kênh bán hàng' : 'Xem kênh bán hàng'}
        maxWidth='lg'
        disableBackdropClose={true}
        disableEscapeKeyDown={true}
        titleIcon={<AritoIcon icon={281} />}
      >
        <AritoForm
          mode={mode}
          hasAritoActionBar={false}
          schema={formSchema}
          onSubmit={onSubmit}
          initialData={initialData}
          className='w-[90%] min-w-[43vw]'
          headerFields={
            <BasicInfo
              mode={mode}
              nguonDon={nguonDon}
              hinhThucThanhToan={hinhThucThanhToan}
              phuongThucThanhToan={phuongThucThanhToan}
              setNguonDon={setNguonDon}
              setHinhThucThanhToan={setHinhThucThanhToan}
              setPhuongThucThanhToan={setPhuongThucThanhToan}
            />
          }
          classNameBottomBar='relative w-full flex justify-end gap-2'
          bottomBar={
            <>
              {mode === 'view' && (
                <BottomBar
                  mode={mode}
                  onAdd={onAddButtonClick}
                  onEdit={onEditButtonClick}
                  onDelete={onDeleteButtonClick}
                  onCopy={onCopyButtonClick}
                  onSubmit={() => {}}
                  onClose={onClose}
                />
              )}

              {mode !== 'view' && (
                <div className='space-x-2 p-2'>
                  <Button
                    className='bg-[rgba(15,118,110,0.9)] text-white hover:bg-[rgba(15,118,110,1)]'
                    type='submit'
                    variant='contained'
                    disabled={isSubmitting}
                  >
                    <AritoIcon icon={884} marginX='4px' />
                    {isSubmitting ? 'Đang xử lý...' : 'Đồng ý'}
                  </Button>

                  <Button onClick={() => setShowConfirmDialog(true)} variant='outlined' disabled={isSubmitting}>
                    <AritoIcon icon={885} marginX='4px' />
                    Huỷ
                  </Button>
                </div>
              )}
            </>
          }
        />
        {error && <div className='mx-4 mb-4 rounded bg-red-100 p-2 text-red-700'>{error}</div>}
      </AritoDialog>

      <ConfirmDialog
        onClose={handleCloseDialog}
        open={showConfirmDialog}
        onCloseConfirmDialog={() => setShowConfirmDialog(false)}
      />
    </>
  );
}

export default FormDialog;
