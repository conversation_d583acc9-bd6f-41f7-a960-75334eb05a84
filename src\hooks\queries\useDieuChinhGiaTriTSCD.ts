import { useState, useEffect } from 'react';
import {
  DieuChinhGiaTriTSCD,
  DieuChinhGiaTriTSCDInput,
  DieuChinhGiaTriTSCDResponse
} from '@/types/schemas/dieu-chinh-gia-tri-tscd.type';
import { useAuth } from '@/contexts/auth-context';
import { QUERY_KEYS } from '@/constants';
import api from '@/lib/api';

interface UseDieuChinhGiaTriTSCDReturn {
  dieuChinhGiaTriTSCDs: DieuChinhGiaTriTSCD[];
  isLoading: boolean;
  error: string | null;
  addDieuChinhGiaTriTSCD: (newDieuChinh: DieuChinhGiaTriTSCDInput) => Promise<void>;
  updateDieuChinhGiaTriTSCD: (uuid: string, updatedDieuChinh: DieuChinhGiaTriTSCDInput) => Promise<void>;
  deleteDieuChinhGiaTriTSCD: (uuid: string) => Promise<void>;
  refreshDieuChinhGiaTriTSCDs: () => Promise<void>;
  getDieuChinhGiaTriTSCDsByTaiSan: (maTaiSan: string) => Promise<DieuChinhGiaTriTSCD[]>;
  getDieuChinhGiaTriTSCDsByKyNam: (ky: number, nam: number) => Promise<DieuChinhGiaTriTSCD[]>;
}

/**
 * Hook for managing DieuChinhGiaTriTSCD (Fixed Asset Value Adjustment) data
 *
 * This hook provides functions to fetch, create, update, and delete fixed asset value adjustments.
 * It follows the established pattern from other query hooks in the codebase.
 */
export const useDieuChinhGiaTriTSCD = (initialDieuChinhs: DieuChinhGiaTriTSCD[] = []): UseDieuChinhGiaTriTSCDReturn => {
  const [dieuChinhGiaTriTSCDs, setDieuChinhGiaTriTSCDs] = useState<DieuChinhGiaTriTSCD[]>(initialDieuChinhs);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const { entity } = useAuth();

  const fetchDieuChinhGiaTriTSCDs = async () => {
    if (!entity?.slug) return;

    setIsLoading(true);
    setError(null);
    try {
      const response = await api.get<DieuChinhGiaTriTSCDResponse>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.DIEU_CHINH_GIA_TRI_TSCD}/`
      );

      setDieuChinhGiaTriTSCDs(response.data.results);
    } catch (error: any) {
      const errorMessage = error.message || 'Error fetching dieu chinh gia tri TSCD';
      setError(errorMessage);
      console.error('Error fetching dieu chinh gia tri TSCD:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const addDieuChinhGiaTriTSCD = async (newDieuChinh: DieuChinhGiaTriTSCDInput): Promise<void> => {
    if (!entity?.slug) return;

    try {
      const response = await api.post<DieuChinhGiaTriTSCD>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.DIEU_CHINH_GIA_TRI_TSCD}/`,
        newDieuChinh
      );

      setDieuChinhGiaTriTSCDs(prev => [...prev, response.data]);
    } catch (error: any) {
      const errorMessage = error.message || 'Error adding dieu chinh gia tri TSCD';
      setError(errorMessage);
      console.error('Error adding dieu chinh gia tri TSCD:', error);
      throw error;
    }
  };

  const updateDieuChinhGiaTriTSCD = async (
    uuid: string,
    updatedAdjustment: DieuChinhGiaTriTSCDInput
  ): Promise<void> => {
    if (!entity?.slug) return;

    try {
      const response = await api.put<DieuChinhGiaTriTSCD>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.DIEU_CHINH_GIA_TRI_TSCD}/${uuid}/`,
        updatedAdjustment
      );

      setDieuChinhGiaTriTSCDs(prev => prev.map(dieuChinh => (dieuChinh.uuid === uuid ? response.data : dieuChinh)));
    } catch (error: any) {
      const errorMessage = error.message || 'Error updating dieu chinh gia tri TSCD';
      setError(errorMessage);
      console.error('Error updating dieu chinh gia tri TSCD:', error);
      throw error;
    }
  };

  const deleteDieuChinhGiaTriTSCD = async (uuid: string): Promise<void> => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      await api.delete(`/entities/${entity.slug}/erp/${QUERY_KEYS.DIEU_CHINH_GIA_TRI_TSCD}/${uuid}/`);

      setDieuChinhGiaTriTSCDs(prev => prev.filter(dieuChinh => dieuChinh.uuid !== uuid));
    } catch (error: any) {
      const errorMessage = error.message || 'Error deleting dieu chinh gia tri TSCD';
      setError(errorMessage);
      console.error('Error deleting dieu chinh gia tri TSCD:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const refreshDieuChinhGiaTriTSCDs = async (): Promise<void> => {
    await fetchDieuChinhGiaTriTSCDs();
  };

  const getDieuChinhGiaTriTSCDsByTaiSan = async (assetUuid: string): Promise<DieuChinhGiaTriTSCD[]> => {
    if (!entity?.slug) return [];

    try {
      const response = await api.get<DieuChinhGiaTriTSCDResponse>(
        `/entities/${entity.slug}/erp/tai-san/khai-bao-tang-giam-tscd/dieu-chinh-gia-tri-tscd/`,
        {
          params: { ma_ts: assetUuid }
        }
      );

      return response.data.results;
    } catch (error: any) {
      const errorMessage = error.message || 'Error fetching asset value adjustments by asset';
      setError(errorMessage);
      console.error('Error fetching asset value adjustments by asset:', error);
      return [];
    }
  };

  const getDieuChinhGiaTriTSCDsByKyNam = async (ky: number, nam: number): Promise<DieuChinhGiaTriTSCD[]> => {
    if (!entity?.slug) return [];

    try {
      const response = await api.get<DieuChinhGiaTriTSCDResponse>(
        `/entities/${entity.slug}/erp/tai-san/khai-bao-tang-giam-tscd/dieu-chinh-gia-tri-tscd/`,
        {
          params: { ky, nam }
        }
      );

      return response.data.results;
    } catch (error: any) {
      const errorMessage = error.message || 'Error fetching asset value adjustments by period';
      setError(errorMessage);
      console.error('Error fetching asset value adjustments by period:', error);
      return [];
    }
  };

  useEffect(() => {
    fetchDieuChinhGiaTriTSCDs();
  }, [entity?.slug]);

  return {
    dieuChinhGiaTriTSCDs,
    isLoading,
    error,
    addDieuChinhGiaTriTSCD,
    updateDieuChinhGiaTriTSCD,
    deleteDieuChinhGiaTriTSCD,
    refreshDieuChinhGiaTriTSCDs,
    getDieuChinhGiaTriTSCDsByTaiSan,
    getDieuChinhGiaTriTSCDsByKyNam
  };
};
