import { QUERY_KEYS, accountSearchColumns, quyenChungTuSearchColumns } from '@/constants';
import { SearchField, FormField, UnitDropdown } from '@/components/custom/arito';
import { FormFieldState, FormFieldActions } from '../../hooks';
import { AccountModel, QuyenChungTu } from '@/types/schemas';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';
import { useNgoaiTe } from '@/hooks';

interface BasicInfoTabProps {
  formMode: FormMode;
  formState: {
    state: FormFieldState;
    actions: FormFieldActions;
  };
}

export function BasicInfoTab({ formMode, formState: { state, actions } }: BasicInfoTabProps) {
  const { currencies } = useNgoaiTe();

  const currencyOptions = currencies.map(currency => ({
    value: currency.uuid,
    label: currency.ma_nt
  }));

  // Find VND currency for default value
  const vndCurrency = currencies.find(
    currency => currency.ma_nt === 'VND' || currency.ma_nt === 'VNĐ' || currency.ten_nt?.includes('VND')
  );
  const defaultCurrencyValue = vndCurrency?.uuid || (currencies.length > 0 ? currencies[0].uuid : '');

  return (
    <div className='space-y-2 p-3'>
      <div className='space-y-1'>
        <div className='grid grid-cols-5 gap-4'>
          <div className='col-span-4 flex flex-col items-baseline gap-2'>
            <div className='flex items-center'>
              <Label className='w-32 min-w-32'>Loại phiếu chi</Label>
              <div className='w-[250px]'>
                <FormField
                  name='ma_ngv'
                  type='select'
                  options={[
                    { value: '1', label: '1. Chi theo hóa đơn' },
                    { value: '2', label: '2. Chi theo đối tượng' },
                    { value: '3', label: '3. Chi khác' },
                    { value: '4', label: '4. Gửi tiền vào ngân hàng' }
                  ]}
                  value={state.loaiPhieuChi}
                  onValueChange={actions.setLoaiPhieuChi}
                  disabled={formMode === 'view'}
                />
              </div>
            </div>

            <div className='flex w-full items-center'>
              <Label className='w-32 min-w-32'>Địa chỉ</Label>
              <div className='flex-1'>
                <FormField name='dia_chi' type='text' disabled={formMode === 'view'} />
              </div>
            </div>

            <div className='flex w-full items-center'>
              <Label className='w-32 min-w-32'>Người nhận tiền</Label>
              <div className='flex-1'>
                <FormField name='ong_ba' type='text' disabled={formMode === 'view'} className='w-full' />
              </div>
            </div>

            <div className='flex w-full items-center'>
              <Label className='w-32 min-w-32'>Diễn giải</Label>
              <div className='flex-1'>
                <FormField name='dien_giai' type='text' disabled={formMode === 'view'} />
              </div>
            </div>

            <div className='flex items-center'>
              <Label className='w-32 min-w-32'>Tài khoản có</Label>
              <SearchField<AccountModel>
                name='tk'
                type='text'
                disabled={formMode === 'view'}
                columnDisplay='code'
                displayRelatedField='name'
                searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
                searchColumns={accountSearchColumns}
                value={state.taiKhoan?.code || ''}
                onRowSelection={actions.setTaiKhoan}
                dialogTitle='Danh mục tài khoản'
              />
            </div>
          </div>

          <div className='col-start-5 flex flex-col items-start gap-2'>
            <div className='flex items-center'>
              <Label className='w-32 min-w-32'>Số chứng từ</Label>
              <SearchField<QuyenChungTu>
                name='so_ct'
                type='text'
                disabled={formMode === 'view'}
                columnDisplay='ma_nk'
                searchEndpoint={`/${QUERY_KEYS.QUYEN_CHUNG_TU}/`}
                searchColumns={quyenChungTuSearchColumns}
                value={state.chungTu?.ma_nk || ''}
                onRowSelection={actions.setChungTu}
                dialogTitle='Danh mục quyển chứng từ'
              />
            </div>

            <div className='flex items-center'>
              <Label className='w-32 min-w-32'>Ngày chứng từ</Label>
              <FormField name='ngay_ct' type='date' disabled={formMode === 'view'} />
            </div>

            <div className='flex items-center'>
              <Label className='w-32 min-w-32'>Ngày lập chứng từ</Label>
              <FormField name='ngay_lct' type='date' disabled={formMode === 'view'} />
            </div>

            <UnitDropdown
              formMode={formMode}
              className='mb-2'
              labelClassName='w-32 min-w-32'
              selectClassName='w-[240px]'
            />

            <div className='flex items-center'>
              <Label className='w-32 min-w-32'>Ngoại tệ</Label>
              <div className='w-[70px]'>
                <FormField
                  name='ma_nt'
                  type='select'
                  disabled={formMode === 'view'}
                  options={currencyOptions}
                  defaultValue={defaultCurrencyValue}
                  className='pt-1'
                />
              </div>
              <div className='flex-1'>
                <FormField name='ty_gia' type='number' disabled={formMode === 'view'} className='ml-2' />
              </div>
            </div>

            <div className='flex w-full items-center'>
              <Label className='w-32 min-w-32'>Trạng thái</Label>
              <div className='flex-1'>
                <FormField
                  name='status'
                  type='select'
                  disabled={formMode === 'view'}
                  options={[
                    { value: '0', label: 'Chưa ghi sổ' },
                    { value: '3', label: 'Chờ duyệt' },
                    { value: '5', label: 'Đã ghi sổ' },
                    { value: '6', label: 'Đang thực hiện' },
                    { value: '7', label: 'Hoàn thành' }
                  ]}
                />
              </div>
            </div>

            <div className='flex items-center'>
              <Label className='w-32 min-w-32'></Label>
              <FormField name='transfer_yn' type='checkbox' disabled={true} label='Dữ liệu được nhận' />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
