import { useState, useEffect } from 'react';
import {
  ButToanDieuChinhGiamCongNo,
  ButToanDieuChinhGiamCongNoInput,
  ButToanDieuChinhGiamCongNoResponse,
  ChiTietButToanDieuChinhGiamCongNo,
  ChiTietButToanDieuChinhGiamCongNoInput
} from '@/types/schemas/but-toan-dieu-chinh-giam-cong-no.type';
import { QUERY_KEYS } from '@/constants/query-keys';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/api';

interface UseButToanDieuChinhGiamCongNoReturn {
  butToanDieuChinhGiamCongNos: ButToanDieuChinhGiamCongNo[];
  isLoading: boolean;
  isLoadingDetail: boolean;
  addButToanDieuChinhGiamCongNo: (
    newButToanDieuChinhGiamCongNo: ButToanDieuChinhGiamCongNoInput
  ) => Promise<ButToanDieuChinhGiamCongNo>;
  updateButToanDieuChinhGiamCongNo: (
    uuid: string,
    updatedButToanDieuChinhGiamCongNo: ButToanDieuChinhGiamCongNoInput
  ) => Promise<ButToanDieuChinhGiamCongNo>;
  deleteButToanDieuChinhGiamCongNo: (uuid: string) => Promise<void>;
  refreshButToanDieuChinhGiamCongNos: () => Promise<void>;
  getButToanDieuChinhGiamCongNoByCustomer: (customerId: string) => Promise<ButToanDieuChinhGiamCongNo[]>;
  getButToanDieuChinhGiamCongNoDetail: (uuid: string) => Promise<ChiTietButToanDieuChinhGiamCongNo[]>;
  addChiTietButToanDieuChinhGiamCongNo: (
    parentUuid: string,
    newChiTiet: ChiTietButToanDieuChinhGiamCongNoInput
  ) => Promise<ChiTietButToanDieuChinhGiamCongNo>;
  updateChiTietButToanDieuChinhGiamCongNo: (
    parentUuid: string,
    chiTietUuid: string,
    updatedChiTiet: ChiTietButToanDieuChinhGiamCongNoInput
  ) => Promise<ChiTietButToanDieuChinhGiamCongNo>;
  deleteChiTietButToanDieuChinhGiamCongNo: (parentUuid: string, chiTietUuid: string) => Promise<void>;
}

/**
 * Hook for managing ButToanDieuChinhGiamCongNo (Debt Reduction Adjustment) data
 *
 * This hook provides functions to fetch, create, update, and delete debt reduction adjustments and their details.
 */
export const useButToanDieuChinhGiamCongNo = (
  initialButToanDieuChinhGiamCongNos: ButToanDieuChinhGiamCongNo[] = []
): UseButToanDieuChinhGiamCongNoReturn => {
  const [butToanDieuChinhGiamCongNos, setButToanDieuChinhGiamCongNos] = useState<ButToanDieuChinhGiamCongNo[]>(
    initialButToanDieuChinhGiamCongNos
  );
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isLoadingDetail, setIsLoadingDetail] = useState<boolean>(true);

  const { entity } = useAuth();

  const fetchButToanDieuChinhGiamCongNos = async () => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.get<ButToanDieuChinhGiamCongNoResponse>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.BUT_TOAN_DIEU_CHINH_GIAM_CONG_NO}/`
      );
      setButToanDieuChinhGiamCongNos(response.data.results);
    } catch (error) {
      console.error('Error fetching debt reduction adjustments:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getButToanDieuChinhGiamCongNoByCustomer = async (customerId: string): Promise<ButToanDieuChinhGiamCongNo[]> => {
    if (!entity?.slug) return [];

    setIsLoading(true);
    try {
      const response = await api.get<ButToanDieuChinhGiamCongNoResponse>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.BUT_TOAN_DIEU_CHINH_GIAM_CONG_NO}/?ma_kh=${customerId}`
      );
      return response.data.results;
    } catch (error) {
      console.error('Error fetching debt reduction adjustments by customer:', error);
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  const addButToanDieuChinhGiamCongNo = async (
    newButToanDieuChinhGiamCongNo: ButToanDieuChinhGiamCongNoInput
  ): Promise<ButToanDieuChinhGiamCongNo> => {
    if (!entity?.slug) throw new Error('Entity not found');

    setIsLoading(true);
    try {
      const response = await api.post<ButToanDieuChinhGiamCongNo>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.BUT_TOAN_DIEU_CHINH_GIAM_CONG_NO}/`,
        newButToanDieuChinhGiamCongNo
      );

      const addedButToanDieuChinhGiamCongNo = response.data;
      setButToanDieuChinhGiamCongNos(prev => [...prev, addedButToanDieuChinhGiamCongNo]);
      return addedButToanDieuChinhGiamCongNo;
    } catch (error) {
      console.error('Error adding debt reduction adjustment:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateButToanDieuChinhGiamCongNo = async (
    uuid: string,
    updatedButToanDieuChinhGiamCongNo: ButToanDieuChinhGiamCongNoInput
  ): Promise<ButToanDieuChinhGiamCongNo> => {
    if (!entity?.slug) throw new Error('Entity not found');

    setIsLoading(true);
    try {
      const response = await api.patch<ButToanDieuChinhGiamCongNo>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.BUT_TOAN_DIEU_CHINH_GIAM_CONG_NO}/${uuid}/`,
        updatedButToanDieuChinhGiamCongNo
      );

      const updatedData = response.data;
      setButToanDieuChinhGiamCongNos(prev => prev.map(item => (item.uuid === uuid ? updatedData : item)));
      return updatedData;
    } catch (error) {
      console.error('Error updating debt reduction adjustment:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteButToanDieuChinhGiamCongNo = async (uuid: string) => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      await api.delete(`/entities/${entity.slug}/erp/${QUERY_KEYS.BUT_TOAN_DIEU_CHINH_GIAM_CONG_NO}/${uuid}/`);
      setButToanDieuChinhGiamCongNos(prev => prev.filter(item => item.uuid !== uuid));
    } catch (error) {
      console.error('Error deleting debt reduction adjustment:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const getButToanDieuChinhGiamCongNoDetail = async (uuid: string): Promise<ChiTietButToanDieuChinhGiamCongNo[]> => {
    if (!entity?.slug) throw new Error('Entity not found');

    setIsLoadingDetail(true);
    try {
      const response = await api.get<ChiTietButToanDieuChinhGiamCongNo[]>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.BUT_TOAN_DIEU_CHINH_GIAM_CONG_NO}/${uuid}/chi-tiet/`
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching debt reduction adjustment detail:', error);
      throw error;
    } finally {
      setIsLoadingDetail(false);
    }
  };

  const addChiTietButToanDieuChinhGiamCongNo = async (
    parentUuid: string,
    newChiTiet: ChiTietButToanDieuChinhGiamCongNoInput
  ): Promise<ChiTietButToanDieuChinhGiamCongNo> => {
    if (!entity?.slug) throw new Error('Entity not found');

    setIsLoadingDetail(true);
    try {
      const response = await api.post<ChiTietButToanDieuChinhGiamCongNo>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.BUT_TOAN_DIEU_CHINH_GIAM_CONG_NO}/${parentUuid}/chi-tiet/`,
        newChiTiet
      );
      return response.data;
    } catch (error) {
      console.error('Error adding debt reduction adjustment detail:', error);
      throw error;
    } finally {
      setIsLoadingDetail(false);
    }
  };

  const updateChiTietButToanDieuChinhGiamCongNo = async (
    parentUuid: string,
    chiTietUuid: string,
    updatedChiTiet: ChiTietButToanDieuChinhGiamCongNoInput
  ): Promise<ChiTietButToanDieuChinhGiamCongNo> => {
    if (!entity?.slug) throw new Error('Entity not found');

    setIsLoadingDetail(true);
    try {
      const response = await api.patch<ChiTietButToanDieuChinhGiamCongNo>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.BUT_TOAN_DIEU_CHINH_GIAM_CONG_NO}/${parentUuid}/chi-tiet/${chiTietUuid}/`,
        updatedChiTiet
      );
      return response.data;
    } catch (error) {
      console.error('Error updating debt reduction adjustment detail:', error);
      throw error;
    } finally {
      setIsLoadingDetail(false);
    }
  };

  const deleteChiTietButToanDieuChinhGiamCongNo = async (parentUuid: string, chiTietUuid: string) => {
    if (!entity?.slug) return;

    setIsLoadingDetail(true);
    try {
      await api.delete(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.BUT_TOAN_DIEU_CHINH_GIAM_CONG_NO}/${parentUuid}/chi-tiet/${chiTietUuid}/`
      );
    } catch (error) {
      console.error('Error deleting debt reduction adjustment detail:', error);
      throw error;
    } finally {
      setIsLoadingDetail(false);
    }
  };

  useEffect(() => {
    fetchButToanDieuChinhGiamCongNos();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [entity?.slug]);

  return {
    butToanDieuChinhGiamCongNos,
    isLoading,
    isLoadingDetail,
    addButToanDieuChinhGiamCongNo,
    updateButToanDieuChinhGiamCongNo,
    deleteButToanDieuChinhGiamCongNo,
    refreshButToanDieuChinhGiamCongNos: fetchButToanDieuChinhGiamCongNos,
    getButToanDieuChinhGiamCongNoByCustomer,
    getButToanDieuChinhGiamCongNoDetail,
    addChiTietButToanDieuChinhGiamCongNo,
    updateChiTietButToanDieuChinhGiamCongNo,
    deleteChiTietButToanDieuChinhGiamCongNo
  };
};
