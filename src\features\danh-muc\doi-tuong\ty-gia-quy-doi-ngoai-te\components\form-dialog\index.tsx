'use client';

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { AritoDialog, AritoIcon, AritoForm, BottomBar } from '@/components/custom/arito';
import { FormSchema, initialFormValues } from '../../schemas';
import { BasicInfoTab } from './BasicInfoTab';
import ConfirmDialog from '../ConfirmDialog';
import { NgoaiTe } from '@/types/schemas';
import { FormMode } from '@/types/form';

interface FormDialogProps {
  formMode: FormMode;
  open: boolean;
  initialData?: any;
  onSubmit?: (data: any) => void;
  onClose: () => void;
  onAdd?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onCopy?: () => void;
}

const FormDialog = ({
  open,
  onClose,
  onSubmit,
  formMode,
  initialData,
  onAdd,
  onEdit,
  onDelete,
  onCopy
}: FormDialogProps) => {
  const [isConfirm, setIsConfirm] = useState<boolean>(false);
  const [isFormDirty, setIsFormDirty] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [ngoaiTe, setNgoaiTe] = useState<NgoaiTe | null>(initialData?.ma_nt_data);

  useEffect(() => {
    if (!open) {
      setIsFormDirty(false);
      setError('');
    }
  }, [open]);

  const handleSubmit = (data: any) => {
    // Validate required SearchField
    if (!ngoaiTe?.uuid) {
      setError('Ngoại tệ là bắt buộc');
      return;
    }

    // Clear error if validation passes
    setError('');

    onSubmit?.({ ...data, ma_nt: ngoaiTe?.uuid, ngay_hl: format(data.ngay_hl, 'yyyy-MM-dd') });
    setIsFormDirty(false);
    onClose();
  };

  const handleClose = () => {
    if (isFormDirty && formMode !== 'view') {
      setIsConfirm(true);
    } else {
      onClose();
    }
  };

  return (
    <>
      <AritoDialog
        open={open}
        onClose={handleClose}
        title={formMode === 'add' ? 'Mới' : formMode === 'edit' ? 'Sửa' : 'Tỷ giá quy đổi ngoại tệ'}
        maxWidth='xl'
        disableBackdropClose={false}
        disableEscapeKeyDown={true}
        titleIcon={<AritoIcon icon={281} />}
      >
        <AritoForm
          mode={formMode}
          hasAritoActionBar={false}
          schema={FormSchema}
          onSubmit={handleSubmit}
          onClose={handleClose}
          initialData={initialData || initialFormValues}
          className='w-[800px] overflow-y-auto'
          headerFields={
            <div onChange={() => setIsFormDirty(true)}>
              <BasicInfoTab formMode={formMode} ngoaiTe={ngoaiTe} setNgoaiTe={setNgoaiTe} />
            </div>
          }
          classNameBottomBar='relative w-full flex justify-end gap-2 '
          bottomBar={
            <>
              <div className='ml-2 mt-2 w-full'>
                {error && <div className='mb-4 rounded bg-red-100 p-2 text-red-700'>{error}</div>}
              </div>
              <BottomBar
                mode={formMode}
                onClose={onClose}
                onAdd={onAdd}
                onEdit={onEdit}
                onDelete={onDelete}
                onCopy={onCopy}
              />
            </>
          }
        />
      </AritoDialog>

      {isConfirm && (
        <ConfirmDialog
          open={isConfirm}
          onClose={() => setIsConfirm(false)}
          onConfirm={() => {
            setIsConfirm(false);
            setIsFormDirty(false);
            onClose();
          }}
          title='Cảnh báo'
          content='Bạn muốn kết thúc?'
        />
      )}
    </>
  );
};

export default FormDialog;
