import { useState } from 'react';
import { useTaxRateIntegration } from './useTaxRateIntegration';
import { TaxRateFormattedData } from '../schemas';

/**
 * Hook for managing row selection state and data
 */
export default function useRows() {
  const [selectedObj, setSelectedObj] = useState<TaxRateFormattedData | null>(null);
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(null);
  const { taxRates, isLoading, refreshTaxRates } = useTaxRateIntegration();

  // Transform API data to the format expected by the data table
  const tableData = taxRates.map(item => {
    return {
      uuid: item.uuid,
      ma_thue: item.ma_thue,
      ten_thue: item.ten_thue,
      nhom_thue: item.nhom_thue,
      nhom_thue_data: item.nhom_thue_data,
      tk_thue_dau_ra: item.tk_thue_dau_ra,
      tk_thue_dau_ra_data: item.tk_thue_dau_ra_data,
      tk_thue_dau_ra_duoc_gia: item.tk_thue_dau_ra_duoc_gia,
      tk_thue_dau_ra_duoc_gia_data: item.tk_thue_dau_ra_duoc_gia_data,
      tk_thue_dau_vao: item.tk_thue_dau_vao,
      tk_thue_dau_vao_data: item.tk_thue_dau_vao_data,
      tk_thue_dau_vao_duoc_gia: item.tk_thue_dau_vao_duoc_gia,
      tk_thue_dau_vao_duoc_gia_data: item.tk_thue_dau_vao_duoc_gia_data
    };
  });

  const handleRowClick = (params: any) => {
    setSelectedRowIndex(params.id);
    setSelectedObj(params.row);
  };

  const clearSelection = () => {
    setSelectedRowIndex(null);
    setSelectedObj(null);
  };

  return {
    selectedObj,
    selectedRowIndex,
    tableData,
    isLoading,
    refreshTaxRates,
    handleRowClick,
    clearSelection
  };
}
