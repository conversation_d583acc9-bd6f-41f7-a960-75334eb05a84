'use client';

import Split from 'react-split';
import { AritoDataTables, InputTable, LoadingOverlay, DeleteDialog } from '@/components/custom/arito';
import { SearchDialog, ActionBar, FormDialog, InputTableActionBar } from './components';
import { useFormState, useHoaDonBanDichVu, useTaxRate, useRows } from '@/hooks';
import { ChiTietHoaDonBanHangDichVu } from '@/types/schemas';
import { getInputTableColumns } from './cols-definition';
import { useSearchDialog, useTableData } from './hooks';

export default function HoaDonBanDichVuPage() {
  const { taxRates } = useTaxRate();
  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRows();
  const {
    showForm,
    showDelete,
    formMode,
    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick
  } = useFormState();

  const { showSearchDialog, handleSearch, handleSearchSubmit, closeSearchDialog } = useSearchDialog();
  const {
    hoaDonBanDichVus,
    isLoading,
    addHoaDonBanDichVu,
    updateHoaDonBanDichVu,
    deleteHoaDonBanDichVu,
    refreshHoaDonBanDichVus
  } = useHoaDonBanDichVu();
  const { detailRows, totalAmount, totalDiscount, totalTax, tables, setDetailRows } = useTableData(
    hoaDonBanDichVus,
    handleViewClick
  );

  return (
    <div className='flex size-full min-h-[calc(100vh-120px)] flex-col overflow-hidden'>
      {!showForm && (
        <>
          <ActionBar
            onAdd={handleAddClick}
            onEdit={handleEditClick}
            onDelete={handleDeleteClick}
            onCopy={handleCopyClick}
            onSearch={handleSearch}
            onRefresh={refreshHoaDonBanDichVus}
            isEditDisabled={!selectedObj}
          />

          <Split
            className='flex flex-1 flex-col overflow-hidden'
            direction='vertical'
            sizes={[50, 50]}
            minSize={200}
            gutterSize={4}
            gutterAlign='center'
            snapOffset={30}
            dragInterval={1}
            cursor='row-resize'
          >
            <div className='w-full overflow-hidden'>
              {isLoading && <LoadingOverlay />}
              {!isLoading && (
                <AritoDataTables
                  tables={tables}
                  onRowClick={handleRowClick}
                  selectedRowId={selectedRowIndex || undefined}
                />
              )}
            </div>

            <div className='h-[250px] overflow-hidden'>
              <InputTable<ChiTietHoaDonBanHangDichVu>
                rows={detailRows}
                columns={getInputTableColumns()}
                mode={formMode}
                actionButtons={<InputTableActionBar mode={formMode} />}
              />
            </div>
          </Split>
        </>
      )}

      {showForm && (
        <FormDialog
          formMode={formMode}
          detailRows={detailRows}
          setDetailRows={setDetailRows}
          totalAmount={totalAmount}
          totalDiscount={totalDiscount}
          totalTax={totalTax}
          onClose={handleCloseForm}
          onSubmit={addHoaDonBanDichVu}
        />
      )}

      {showDelete && (
        <DeleteDialog
          open={showDelete}
          onClose={handleCloseDelete}
          deleteObj={deleteHoaDonBanDichVu}
          selectedObj={selectedObj}
          clearSelection={clearSelection}
        />
      )}

      {showSearchDialog && (
        <SearchDialog open={showSearchDialog} onClose={closeSearchDialog} onSearch={handleSearchSubmit} />
      )}
    </div>
  );
}
