import { FormField } from '@/components/custom/arito';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface ExchangeRateTabProps {
  formMode: FormMode;
}

const ExchangeRateTab: React.FC<ExchangeRateTabProps> = ({ formMode }) => {
  const isViewMode = formMode === 'view';

  return (
    <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
      <div className='p-4 md:p-6'>
        <div className='flex gap-y-8'>
          <Label className='w-40'>Sửa tỷ giá ghi sổ</Label>
          <FormField name='tg_dd' type='checkbox' disabled={isViewMode} />
        </div>
      </div>
    </div>
  );
};

export default ExchangeRateTab;
