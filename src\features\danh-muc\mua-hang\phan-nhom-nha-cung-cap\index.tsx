'use client';
import { ChevronRight } from 'lucide-react';
import AritoDataTables from '@/components/custom/arito/data-tables';
import SupplierGroupDialog from './components/suppliergroup-dialog';
import { useSidebarAndFilter } from './hooks/useSidebarAndFilter';
import { exportMainColumns, groupTypes } from './cols-definition';
import { useDialogState, useRowSelection } from './hooks';
import DeleteDialog from './components/DeleteDialog';
import { useGroup } from '@/hooks/queries/useGroup';
import { ActionBar } from './components/ActionBar';
import { Sidebar } from './components/SideBar';
import { GroupType } from '@/types/schemas';

export default function SupplierGroupPage() {
  const { selectedObj, handleRowClick, clearSelection } = useRowSelection();
  const {
    showAddDialog,
    showEditDialog,
    showDeleteDialog,
    showWatchDialog,
    isCopyMode,

    openAddDialog,
    closeAddDialog,
    openEditDialog,
    closeEditDialog,
    openDeleteDialog,
    closeDeleteDialog,
    openWatchDialog,
    closeWatchDialog,

    handleAddButtonClick,
    handleEditButtonClick,
    handleDeleteButtonClick,
    handleCopyButtonClick
  } = useDialogState(clearSelection);

  const { showSidebar, activeGroup, toggleSidebar, handleFilterChange } = useSidebarAndFilter();
  const { groups, isLoading, refreshGroups, addGroup, updateGroup, deleteGroup, fetchGroupsByType } = useGroup(
    activeGroup || GroupType.SUPPLIER1
  );

  const tables = [
    {
      name: 'Tất cả',
      rows: groups,
      columns: exportMainColumns
    }
  ];
  return (
    <div className='flex h-screen w-screen flex-col overflow-hidden'>
      {(showAddDialog || showEditDialog) && (
        <SupplierGroupDialog
          open={showAddDialog || showEditDialog}
          mode={showEditDialog ? 'edit' : 'add'}
          initialData={
            selectedObj && showEditDialog
              ? {
                  ma_nhom: selectedObj.ma_nhom,
                  ten_phan_nhom: selectedObj.ten_phan_nhom,
                  ten2: selectedObj.ten2,
                  trang_thai: parseInt(selectedObj.trang_thai)
                }
              : selectedObj && showAddDialog && isCopyMode
                ? {
                    ma_nhom: selectedObj.ma_nhom,
                    ten_phan_nhom: selectedObj.ten_phan_nhom,
                    ten2: selectedObj.ten2,
                    trang_thai: parseInt(selectedObj.trang_thai)
                  }
                : undefined
          }
          onClose={() => {
            if (showEditDialog) {
              closeEditDialog();
              clearSelection(); // Xóa lựa chọn sau khi đóng dialog sửa
            } else {
              closeAddDialog();
            }
          }}
          selectedObj={showEditDialog ? selectedObj : null}
          addSupplierGroup={addGroup}
          updateSupplierGroup={updateGroup}
          activeGroupType={activeGroup}
          groups={groups} // Truyền danh sách nhóm để kiểm tra trùng mã
        />
      )}

      {showDeleteDialog && (
        <DeleteDialog
          open={showDeleteDialog}
          onClose={closeDeleteDialog}
          selectedObj={selectedObj}
          deleteSupplierGroup={deleteGroup}
          clearSelection={clearSelection}
        />
      )}

      {showWatchDialog && selectedObj && (
        <SupplierGroupDialog
          open={showWatchDialog}
          mode='view'
          initialData={{
            ma_nhom: selectedObj.ma_nhom,
            ten_phan_nhom: selectedObj.ten_phan_nhom,
            ten2: selectedObj.ten2,
            trang_thai: parseInt(selectedObj.trang_thai)
          }}
          onClose={closeWatchDialog}
          selectedObj={selectedObj}
          addSupplierGroup={addGroup}
          updateSupplierGroup={updateGroup}
          onAddButtonClick={handleAddButtonClick}
          onEditButtonClick={handleEditButtonClick}
          onDeleteButtonClick={handleDeleteButtonClick}
          onCopyButtonClick={handleCopyButtonClick}
          activeGroupType={activeGroup}
          groups={groups} // Truyền danh sách nhóm để kiểm tra trùng mã
        />
      )}

      <div className='flex h-full overflow-hidden'>
        <Sidebar
          activeGroup={activeGroup}
          onFilterChange={handleFilterChange}
          groupTypes={groupTypes}
          isOpen={showSidebar}
          toggleSidebar={toggleSidebar}
        />

        <div className='flex flex-1 flex-col overflow-hidden'>
          <div className='flex shrink-0 items-center border-b bg-white p-2'>
            {!showSidebar && (
              <button
                onClick={toggleSidebar}
                className='flex-shrink-0 rounded-full bg-gray-200 p-1 text-gray-500 hover:text-gray-700'
              >
                <ChevronRight className='h-5 w-5' />
              </button>
            )}
            <div className='flex w-full'>
              <ActionBar
                onAddClick={openAddDialog}
                onEditClick={() => selectedObj && openEditDialog()}
                onDeleteClick={() => selectedObj && openDeleteDialog()}
                onCopyClick={() => selectedObj && handleCopyButtonClick()}
                onRefreshClick={refreshGroups}
                onShowSidebar={toggleSidebar}
                onViewClick={() => selectedObj && openWatchDialog()}
              />
            </div>
          </div>

          <div className='w-full flex-1 overflow-auto'>
            {isLoading && (
              <div className='flex h-64 items-center justify-center'>
                <div className='size-12 animate-spin rounded-full border-b-2 border-t-2 border-blue-500'></div>
              </div>
            )}

            {!isLoading && (
              <AritoDataTables
                tables={tables}
                onRowClick={handleRowClick}
                selectedRowId={selectedObj?.uuid || undefined}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
