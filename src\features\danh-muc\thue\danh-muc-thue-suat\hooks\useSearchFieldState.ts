import { useState } from 'react';
import { AccountModel, Group } from '@/types/schemas';

export const useSearchFieldStates = (initialData?: any) => {
  const [nhomThue, setNhomThue] = useState<Group | null>(initialData?.nhom_thue_data || null);

  const [tkThueDauRa, setTkThueDauRa] = useState<AccountModel | null>(initialData?.tk_thue_dau_ra_data || null);

  const [tkThueDauRaDuocGiam, setTkThueDauRaDuocGiam] = useState<AccountModel | null>(
    initialData?.tk_thue_dau_ra_duoc_gia_data || null
  );

  const [tkThueDauVao, setTkThueDauVao] = useState<AccountModel | null>(initialData?.tk_thue_dau_vao_data || null);

  const [tkThueDauVaoDuocGiam, setTkThueDauVaoDuocGiam] = useState<AccountModel | null>(
    initialData?.tk_thue_dau_vao_duoc_gia_data || null
  );

  return {
    nhomThue,
    setNhomThue,
    tkThueDauRa,
    setTkThueDauRa,
    tkThueDauRaDuocGiam,
    setTkThueDauRaDuocGiam,
    tkThueDauVao,
    setTkThueDauVao,
    tkThueDauVaoDuocGiam,
    setTkThueDauVaoDuocGiam
  };
};
