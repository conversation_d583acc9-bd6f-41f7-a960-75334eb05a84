import { ChevronLeft, ChevronRight } from 'lucide-react';
import SidebarButton from '@/components/custom/arito/sidebar-button';
import { ChungTuType } from '@/types/schemas/chung-tu.type';
import { AritoIcon } from '@/components/custom/arito';

interface GroupType {
  label: string;
  value: ChungTuType;
}

interface SidebarProps {
  activeGroup: ChungTuType | null;
  onFilterChange: (groupType: ChungTuType) => void;
  group: GroupType[];
  isOpen: boolean;
  toggleSidebar: () => void;
}

const Sidebar = ({ activeGroup, onFilterChange, group, isOpen, toggleSidebar }: SidebarProps) => {
  if (!isOpen) {
    return (
      <div className='relative h-full w-7 border-r border-gray-300'>
        <div className='absolute left-0.5 top-4 translate-x-1/2 transform'>
          <button
            onClick={toggleSidebar}
            className='flex h-6 w-6 items-center justify-center rounded-full border border-gray-300 bg-gray-200 shadow-sm hover:bg-gray-100'
          >
            <ChevronRight className='h-4 w-4' />
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className='relative flex h-full w-64 flex-col border-r bg-white'>
      <div className='flex-1 overflow-y-auto p-4'>
        <div className='mb-4 flex items-center gap-x-2'>
          <AritoIcon icon={538} />
          <h2 className='text-lg font-semibold'>Phân hệ</h2>
        </div>
        <hr className='mb-4' />
        <ul>
          {/* ChungTu type options */}
          {group.map(g => (
            <li key={g.value} className='mb-2 text-sm'>
              <SidebarButton isActive={activeGroup === g.value} onClick={() => onFilterChange(g.value)}>
                {g.label}
              </SidebarButton>
            </li>
          ))}
        </ul>
      </div>
      <div className='absolute right-0 top-4 z-10 translate-x-1/2 transform'>
        <button
          onClick={toggleSidebar}
          className='flex h-6 w-6 items-center justify-center rounded-full border border-gray-300 bg-gray-200 shadow-sm hover:bg-gray-100'
        >
          <ChevronLeft className='h-4 w-4' />
        </button>
      </div>
    </div>
  );
};

export default Sidebar;
