'use client';

import { AritoDataTables, DeleteDialog } from '@/components/custom/arito';
import { getDataTableColumns } from './cols-definition';
import { ActionBar, FormDialog } from './components';
import { useFormState, useNhanVien } from './hooks';
import { NhanVienProvider } from './context';

export default function DanhMucNhanVienPage() {
  const { nhanViens, isLoading, addNhanVien, updateNhan<PERSON>ien, deleteNhanVien, refreshNhanViens } = useNhanVien();

  const {
    showForm,
    showDelete,
    formMode,
    selectedObj,
    selectedRowIndex,
    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick,
    handleRowClick,
    clearSelection
  } = useFormState();

  const handleSubmit = async (data: any) => {
    try {
      if (formMode === 'add') {
        await add<PERSON>han<PERSON>ien(data);
        handleCloseForm();
        clearSelection();
      } else if (formMode === 'edit' && selectedObj) {
        await updateNhanVien(selectedObj.uuid, data);
        handleCloseForm();
        clearSelection();
      }
    } catch (error) {
      console.error('Error submitting form:', error);
    }
    await refreshNhanViens();
  };

  const tables = [
    {
      name: '',
      rows: nhanViens,
      columns: getDataTableColumns(handleViewClick)
    }
  ];

  return (
    <NhanVienProvider>
      <div className='flex h-full min-h-[calc(100vh-120px)] w-full flex-col overflow-hidden'>
        {!showForm && (
          <>
            <ActionBar
              onAdd={handleAddClick}
              onEdit={() => selectedObj && handleEditClick()}
              onDelete={() => selectedObj && handleDeleteClick()}
              onCopy={handleCopyClick}
              isEditDisabled={!selectedObj}
            />

            {!isLoading && (
              <AritoDataTables
                tables={tables}
                onRowClick={handleRowClick}
                selectedRowId={selectedRowIndex || undefined}
              />
            )}
          </>
        )}

        {showForm && (
          <FormDialog
            formMode={formMode}
            onSubmit={handleSubmit}
            open={showForm}
            onClose={handleCloseForm}
            initialData={
              selectedObj && (formMode === 'edit' || formMode === 'view' || (formMode === 'add' && isCopyMode))
                ? selectedObj
                : undefined
            }
            onAddButtonClick={() => {
              handleCloseForm();
              clearSelection();
              setTimeout(() => {
                handleAddClick();
              }, 100);
            }}
            onEditButtonClick={() => {
              if (selectedObj) {
                handleCloseForm();
                handleEditClick();
              }
            }}
            onDeleteButtonClick={() => {
              if (selectedObj) {
                handleCloseForm();
                handleDeleteClick();
              }
            }}
            onCopyButtonClick={() => {
              if (selectedObj) {
                handleCloseForm();
                handleCopyClick();
              }
            }}
          />
        )}

        {showDelete && (
          <DeleteDialog
            open={showDelete}
            onClose={handleCloseDelete}
            selectedObj={selectedObj}
            deleteObj={deleteNhanVien}
            clearSelection={clearSelection}
          />
        )}
      </div>
    </NhanVienProvider>
  );
}
