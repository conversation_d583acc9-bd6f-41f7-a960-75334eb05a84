import { AritoForm, BottomBar } from '@/components/custom/arito';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { TaxGroupInput } from '@/types/schemas/nhom-thue.type';
import { initialValues } from '../schemas';
import BasicInfo from './BasicInfo';

interface FormDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: TaxGroupInput) => void;
  formMode: 'add' | 'edit' | 'view';
  initialData?: any;
  onAddButtonClick?: () => void;
  onEditButtonClick?: () => void;
  onDeleteButtonClick?: () => void;
  onCopyButtonClick?: () => void;
}

const FormDialog = ({
  open,
  onClose,
  onSubmit,
  formMode,
  initialData,
  onAddButtonClick,
  onEditButtonClick,
  onDeleteButtonClick,
  onCopyButtonClick
}: FormDialogProps) => {
  const handleSubmit = async (data: TaxGroupInput) => {
    await onSubmit(data);
  };

  const title = formMode === 'add' ? 'Thêm nhóm thuế' : formMode === 'edit' ? 'Sửa nhóm thuế' : 'Xem nhóm thuế';

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title={title}
      maxWidth='md'
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
    >
      <AritoForm
        mode={formMode}
        hasAritoActionBar={false}
        onSubmit={handleSubmit}
        initialData={initialData || initialValues}
        className='w-full md:min-w-[500px] lg:min-w-[600px]'
        headerFields={<BasicInfo formMode={formMode} />}
        classNameBottomBar='relative w-full flex justify-end gap-2'
        bottomBar={
          <BottomBar
            mode={formMode}
            onAdd={onAddButtonClick}
            onEdit={onEditButtonClick}
            onDelete={onDeleteButtonClick}
            onCopy={onCopyButtonClick}
            onClose={onClose}
          />
        }
      />
    </AritoDialog>
  );
};

export default FormDialog;
