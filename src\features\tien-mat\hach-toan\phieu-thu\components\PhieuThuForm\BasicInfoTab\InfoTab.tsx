import { useFormContext } from 'react-hook-form';
import { useEffect, useState } from 'react';
import { accountSearchColumns, quyenChungTuSearchColumns } from '@/constants/search-columns';
import { InfoTabSearchFieldStates } from '../../../hooks/useSearchFieldStates';
import { SearchField, FormField } from '@/components/custom/arito';
import { useNgoaiTe, useQuyenChungTuByChungTu } from '@/hooks';
import { generateSoChungTuHienTai } from '@/lib/stringUtil';
import { Tai<PERSON><PERSON>an, QuyenChungTu } from '@/types/schemas';
import { MA_CHUNG_TU, QUERY_KEYS } from '@/constants';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface InfoTabProps {
  formMode: FormMode;
  searchFieldStates: InfoTabSearchFieldStates;
}

export const InfoTab = ({ formMode, searchFieldStates }: InfoTabProps) => {
  const { currencies } = useNgoaiTe();
  const { quyenChungTu, setQuyenChungTu } = searchFieldStates;
  const { watch } = useFormContext();

  const [soChungTuHienTai, setSoChungTuHienTai] = useState('');
  const ngayCt = watch('ngay_ct');

  const { quyenChungTus, isLoading: isLoadingQuyenChungTu } = useQuyenChungTuByChungTu({
    ma_ct: MA_CHUNG_TU.TIEN_MAT.PHIEU_THU,
    ngay_hl: ngayCt || new Date().toISOString().split('T')[0]
  });

  useEffect(() => {
    if (quyenChungTu) {
      setSoChungTuHienTai(generateSoChungTuHienTai(quyenChungTu.i_so_ct_ht, quyenChungTu.so_ct_mau, new Date()));
    }
  }, [quyenChungTu]);

  return (
    <div className='p-4'>
      <div className='flex flex-col space-y-2 lg:flex-row lg:space-x-1 lg:space-y-0'>
        {/* Left column - takes remaining width */}
        <div className='flex-1 space-y-2'>
          <div className='grid grid-cols-[120px,1fr] items-center lg:grid-cols-[120px,1fr]'>
            <Label className='text-sm font-medium'>Loại phiếu thu</Label>
            <FormField
              name='ma_ngv'
              type='select'
              options={[
                { value: '1', label: '1. Thu theo hóa đơn' },
                { value: '2', label: '2. Thu theo đối tượng' },
                { value: '3', label: '3. Thu khác' }
              ]}
              disabled={formMode === 'view'}
            />
          </div>
          <div className='grid grid-cols-[120px,1fr] items-center'>
            <Label className='text-sm font-medium'>Địa chỉ</Label>
            <FormField type='text' name='dia_chi' disabled={formMode === 'view'} />
          </div>
          <div className='grid grid-cols-[120px,1fr] items-center'>
            <Label className='text-sm font-medium'>Người nộp tiền</Label>
            <FormField type='text' name='ong_ba' disabled={formMode === 'view'} />
          </div>
          <div className='grid grid-cols-[120px,1fr] items-center'>
            <Label className='text-sm font-medium'>Diễn giải</Label>
            <FormField type='text' name='dien_giai' disabled={formMode === 'view'} />
          </div>
          <div className='grid grid-cols-[120px,1fr] items-center'>
            <Label className='text-sm font-medium'>Tài khoản nợ</Label>
            <SearchField<TaiKhoan>
              type='text'
              disabled={formMode === 'view'}
              dialogTitle='Danh mục tài khoản'
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
              searchColumns={accountSearchColumns}
              displayRelatedField='name'
              columnDisplay='code'
              value={searchFieldStates.account?.code || ''}
              relatedFieldValue={searchFieldStates.account?.name || ''}
              onRowSelection={searchFieldStates.setAccount}
            />
          </div>
        </div>

        {/* Right column - fixed width of 15rem */}
        <div className='w-full space-y-2 lg:w-72'>
          <div className='grid grid-cols-[120px,1fr] items-center lg:grid-cols-[120px,1fr]'>
            <Label className='text-sm font-medium'>Số chứng từ</Label>
            <div className='flex flex-col space-y-1'>
              <SearchField<QuyenChungTu>
                type='text'
                disabled={formMode === 'view'}
                dialogTitle='Danh mục chứng từ'
                searchColumns={quyenChungTuSearchColumns}
                value={soChungTuHienTai}
                onValueChange={e => setSoChungTuHienTai(e.target.value)}
                relatedFieldValue={quyenChungTu?.ten_nk || ''}
                rows={quyenChungTus}
                onRowSelection={setQuyenChungTu}
              />
            </div>
          </div>
          <div className='grid grid-cols-[120px,1fr] items-center'>
            <Label className='text-sm font-medium'>Ngày chứng từ</Label>
            <FormField type='date' name='ngay_ct' disabled={formMode === 'view'} />
          </div>
          <div className='grid grid-cols-[120px,1fr] items-center'>
            <Label className='text-left text-sm font-medium'>Ngày lập chứng từ</Label>
            <FormField type='date' name='ngay_lct' disabled={formMode === 'view'} />
          </div>
          <div className='grid grid-cols-[120px,1fr] items-center'>
            <Label className='text-sm font-normal'>Ngoại tệ</Label>
            <div className='flex items-center gap-2'>
              <FormField
                type='select'
                name='ma_nt'
                disabled={formMode === 'view'}
                options={currencies.map(item => ({ value: item.uuid, label: item.ten_nt }))}
                className='w-20'
              />

              <FormField type='number' name='ty_gia' disabled={formMode === 'view'} />
            </div>
          </div>
          <div className='grid grid-cols-[120px,1fr] items-center'>
            <Label className='text-sm font-medium'>Trạng thái</Label>
            <FormField
              type='select'
              name='status'
              disabled={formMode === 'view'}
              options={[
                { value: 'unposted', label: 'Chưa ghi sổ' },
                { value: 'pending', label: 'Chờ duyệt' },
                { value: 'posted', label: 'Đã ghi sổ' }
              ]}
            />
          </div>
          <div className='grid grid-cols-[120px,1fr] items-center'>
            <div></div>
            <div className='flex items-center gap-2'>
              <FormField type='checkbox' name='transfer_yn' disabled={formMode === 'view'} />
              <Label className='text-sm font-medium'>Dữ liệu được nhận</Label>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
