import { useFormContext } from 'react-hook-form';
import { useState, useEffect } from 'react';
import { accountSearchColumns, thueSearchColumns, coQuanThueSearchColumns } from '@/constants/search-columns';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { AccountModel, Tax, CoQuanThue } from '@/types/schemas';
import { QUERY_KEYS } from '@/constants/query-keys';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface FormProps {
  mode: FormMode;
}

function BankFeeForm({ mode }: FormProps) {
  const isDisabled = mode === 'view';
  const {
    setValue,
    getValues,
    formState: { errors }
  } = useFormContext();

  const [selectionData, setSelectionData] = useState<{
    tkPhi?: any;
    maThue?: any;
    tkThue?: any;
    cqt?: any;
  }>({});

  useEffect(() => {
    const formValues = getValues();
    if (formValues) {
      if (formValues.tk_cpnh && formValues.tk_cpnh_data) {
        setSelectionData(prev => ({
          ...prev,
          tkPhi: {
            uuid: formValues.tk_cpnh,
            code: formValues.tk_cpnh_data.code,
            name: formValues.tk_cpnh_data.name
          }
        }));
      }

      if (formValues.ma_thue && formValues.ma_thue_data) {
        setSelectionData(prev => ({
          ...prev,
          maThue: {
            uuid: formValues.ma_thue,
            ma_thue: formValues.ma_thue_data.ma_thue,
            ten_thue: formValues.ma_thue_data.ten_thue
          }
        }));
      }

      if (formValues.tk_thue && formValues.tk_thue_data) {
        setSelectionData(prev => ({
          ...prev,
          tkThue: {
            uuid: formValues.tk_thue,
            code: formValues.tk_thue_data.code,
            name: formValues.tk_thue_data.name
          }
        }));
      }
    }
  }, [mode, getValues]);

  const handleTkPhiSelection = (row: any) => {
    setSelectionData(prev => ({ ...prev, tkPhi: row }));
    setValue('tk_cpnh', row.uuid || row.code);
  };

  const handleCqtSelection = (row: any) => {
    setSelectionData(prev => ({ ...prev, cqt: row }));
    setValue('cuc_thue', row.uuid || row.code);
  };

  const handleMaThueSelection = (row: any) => {
    setSelectionData(prev => ({ ...prev, maThue: row }));
    setValue('ma_thue', row?.uuid || row?.ma_thue || null);
  };

  const handleTkThueSelection = (row: any) => {
    setSelectionData(prev => ({ ...prev, tkThue: row }));
    setValue('tk_thue', row.uuid || row.code);
  };

  return (
    <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
      <div className='p-4 md:p-6'>
        <div className='flex flex-col gap-y-4 md:gap-y-2'>
          <div className='space-y-4 md:space-y-2'>
            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Mã phí</Label>
              <FormField type='text' name='ma_cpnh' disabled={isDisabled || mode === 'edit'} />
            </div>

            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tên phí</Label>
              <div className='w-full'>
                <FormField type='text' name='ten_cpnh' disabled={isDisabled} />
              </div>
            </div>

            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tên khác</Label>
              <div className='w-full'>
                <FormField type='text' name='ten_cpnh2' disabled={isDisabled} />
              </div>
            </div>

            <div className='flex items-center'>
              <Label className='w-40 min-w-40'>Tài khoản phí</Label>
              <div className='w-full'>
                <SearchField<AccountModel>
                  type='text'
                  columnDisplay='code'
                  className='w-[11.25rem]'
                  searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
                  searchColumns={accountSearchColumns}
                  dialogTitle='Danh mục tài khoản'
                  value={selectionData.tkPhi?.code || ''}
                  onRowSelection={handleTkPhiSelection}
                  disabled={isDisabled}
                />
                {errors.tk_cpnh && <p className='mt-1 text-xs text-red-500'>{errors.tk_cpnh.message as string}</p>}
                {selectionData.tkPhi?.name && (
                  <span className='ml-2 text-sm text-gray-700'>{selectionData.tkPhi.name}</span>
                )}
              </div>
            </div>

            <div className='flex items-center'>
              <Label className='w-40 min-w-40'>Mã thuế</Label>
              <div className='w-full'>
                <SearchField<Tax>
                  disabled={mode === 'view'}
                  type='text'
                  searchEndpoint={`/${QUERY_KEYS.THUE}/`}
                  searchColumns={thueSearchColumns}
                  dialogTitle='Danh mục thuế'
                  value={selectionData.maThue?.ma_thue || ''}
                  onRowSelection={handleMaThueSelection}
                  className='w-[11.25rem]'
                />
                {selectionData.maThue?.ten_thue && (
                  <span className='ml-2 text-sm text-gray-700'>{selectionData.maThue.ten_thue}</span>
                )}
              </div>
            </div>

            <div className='flex items-center'>
              <Label className='w-40 min-w-40'>Tài khoản thuế</Label>
              <div className='w-full'>
                <SearchField<AccountModel>
                  type='text'
                  columnDisplay='code'
                  className='w-[11.25rem]'
                  searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
                  searchColumns={accountSearchColumns}
                  dialogTitle='Danh mục tài khoản'
                  value={selectionData.tkThue?.code || ''}
                  onRowSelection={handleTkThueSelection}
                  disabled={isDisabled}
                />
                {errors.tk_thue && <p className='mt-1 text-xs text-red-500'>{errors.tk_thue.message as string}</p>}
                {selectionData.tkThue?.name && (
                  <span className='ml-2 text-sm text-gray-700'>{selectionData.tkThue.name}</span>
                )}
              </div>
            </div>

            <div className='flex items-center'>
              <Label className='w-40 min-w-40'>Cục thuế</Label>
              <div className='w-full'>
                <SearchField<CoQuanThue>
                  disabled={mode === 'view'}
                  type='text'
                  searchEndpoint={`/${QUERY_KEYS.CO_QUAN_THUE}/`}
                  searchColumns={coQuanThueSearchColumns}
                  dialogTitle='Danh mục thuế'
                  value={selectionData.cqt?.ma_doi_tuong || ''}
                  onRowSelection={handleCqtSelection}
                  className='w-[11.25rem]'
                />
                {selectionData.cqt?.ten_doi_tuong && (
                  <span className='ml-2 text-sm text-gray-700'>{selectionData.cqt.ten_doi_tuong}</span>
                )}
              </div>
            </div>

            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Mẫu báo cáo</Label>
              <div className='w-2/3'>
                <FormField
                  type='select'
                  name='ma_mau_bc'
                  disabled={isDisabled}
                  defaultValue='3'
                  options={[
                    { value: '3', label: '3. Hóa đơn giá trị gia tăng' },
                    { value: '4', label: '4. Hàng hóa, dịch vụ mua vào không có hóa đơn' },
                    { value: '5', label: '5. Hóa đơn bán hàng thông thường' }
                  ]}
                />
              </div>
            </div>

            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Mã tính chất</Label>
              <div className='w-2/3'>
                <FormField
                  type='select'
                  name='ma_tc_thue'
                  disabled={isDisabled}
                  defaultValue='3'
                  options={[
                    {
                      value: '1',
                      label:
                        '1. Hàng hoá, dịch vụ dùng riêng cho SXKD chịu thuế GTGT và sử dụng cho các hoạt động cung cấp hàng hoá, dịch vụ không kê khai, nộp thuế GTGT đủ điều kiện khấu trừ thuế'
                    },
                    {
                      value: '2',
                      label: '2. Hàng hoá, dịch vụ dùng chung cho SXKD chịu thuế GTGT và không chịu thuế GTGT'
                    },
                    { value: '3', label: '3. Hàng hoá, dịch vụ dùng cho dự án đầu tư đủ điều kiện khấu trừ thuế' },
                    { value: '4', label: '4. Hàng hoá, dịch vụ không đủ điều kiện khấu trừ' },
                    { value: '5', label: '5. Hàng hoá, dịch vụ không phải tổng hợp trên tờ khai 01/GTGT' }
                  ]}
                />
              </div>
            </div>

            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label htmlFor='status' className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>
                Trạng thái
              </Label>
              <div className='w-[11.25rem]'>
                <FormField
                  id='status'
                  type='select'
                  name='status'
                  defaultValue='1'
                  options={[
                    { label: '1. Còn sử dụng', value: '1' },
                    { label: '0. Không sử dụng', value: '0' }
                  ]}
                  disabled={isDisabled}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default BankFeeForm;
