import { GridColDef } from '@mui/x-data-grid';
import { Eye, Pencil } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito';

// Main table columns for payment requests
export const getChangingValuePaymentRequestColumns = (
  handleOpenViewForm: (obj: any) => void,
  handleOpenEditForm: (obj: any) => void
): GridColDef[] => [
  {
    field: 'status',
    headerName: 'Trạng thái',
    width: 120,
    align: 'center',
    headerAlign: 'center',
    renderCell: params => {
      const status = params.value;
      switch (status) {
        case '0':
          return 'Lập chứng từ';
        case '1':
          return 'Chờ duyệt';
        case '5':
          return 'Đã duyệt';
        default:
          return 'Khác';
      }
    }
  },
  {
    field: 'doc_number',
    headerName: 'Số chứng từ',
    width: 120,
    align: 'left',
    headerAlign: 'left'
  },
  {
    field: 'doc_date',
    headerName: '<PERSON><PERSON><PERSON> chứng từ',
    width: 120,
    align: 'center',
    headerAlign: 'center'
  },
  {
    field: 'supplier_code',
    headerName: 'Mã nhân viên',
    width: 130,
    align: 'left',
    headerAlign: 'left'
  },
  {
    field: 'supplier_name',
    headerName: 'Tên người đề nghị',
    width: 200,
    align: 'left',
    headerAlign: 'left'
  },
  {
    field: 'description',
    headerName: 'Diễn giải',
    width: 200,
    align: 'left',
    headerAlign: 'left'
  },
  {
    field: 'total_amount',
    headerName: 'Tổng tiền',
    width: 120,
    align: 'right',
    headerAlign: 'right',
    type: 'number'
  },
  {
    field: 'currency',
    headerName: 'Ngoại tệ',
    width: 80,
    align: 'center',
    headerAlign: 'center'
  },
  {
    field: 'payment_type',
    headerName: 'Loại phiếu chi',
    width: 130,
    align: 'left',
    headerAlign: 'left'
  },
  {
    field: 'department',
    headerName: 'Bộ phận',
    width: 120,
    align: 'left',
    headerAlign: 'left'
  },
  {
    field: 'requester',
    headerName: 'Người đề nghị',
    width: 120,
    align: 'left',
    headerAlign: 'left'
  }
];

// Detail table columns for payment request items
export const exportPaymentRequestDetailColumns: GridColDef[] = [
  {
    field: 'ns_kd',
    headerName: 'Chỉ tiêu ngân sách',
    width: 150
  },
  {
    field: 'ma_kh',
    headerName: 'Mã đối tượng',
    width: 120
  },
  {
    field: 'ten_kh',
    headerName: 'Tên đối tượng',
    width: 200
  },
  {
    field: 'tien',
    headerName: 'Tiền VND',
    width: 130
  },
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 200
  },
  {
    field: 'ma_loai_hd',
    headerName: 'Loại hóa đơn',
    width: 120
  },
  {
    field: 'ma_mau_bc',
    headerName: 'Mẫu báo cáo',
    width: 120
  },
  {
    field: 'ma_tc_thue',
    headerName: 'Mã tính chất',
    width: 120
  },
  {
    field: 'ma_kh_thue',
    headerName: 'Mã ncc',
    width: 100
  },
  {
    field: 'ten_kh_thue',
    headerName: 'Tên nhà cung cấp',
    width: 200
  },
  {
    field: 'dia_chi',
    headerName: 'Địa chỉ',
    width: 200
  },
  {
    field: 'ma_so_thue',
    headerName: 'Mã số thuế',
    width: 120
  },
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 120
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 120
  },
  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 120
  },
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 130
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 120
  },
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 100
  },
  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 120
  },
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 130
  }
];
