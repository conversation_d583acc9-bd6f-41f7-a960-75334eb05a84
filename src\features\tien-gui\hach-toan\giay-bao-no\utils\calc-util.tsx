/**
 * Calculate totals from detail rows
 */
export const calculateTotals = (detailRows: any[] = []) => {
  const amount = detailRows.reduce((sum: number, row: { total: number }) => sum + (row.total || 0), 0);
  const quantity = detailRows.reduce((sum: number, row: { quantity: number }) => sum + (row.quantity || 0), 0);
  const bankFees = detailRows.reduce((sum: number, row: { bankFees: number }) => sum + (row.bankFees || 0), 0);
  const tax = detailRows.reduce((sum: number, row: { tax: number }) => sum + (row.tax || 0), 0);
  const payment = amount + tax + bankFees;

  return {
    totalAmount: amount,
    totalQuantity: quantity,
    totalBankFees: bankFees,
    totalTax: tax,
    totalPayment: payment
  };
};
