import { BasicInfo, DetailTab, ExchangeRateTab, OtherTab, BottomBar } from './tabs';
import { useCalculations, useFormFieldStates } from '../../hooks';
import { FormSchema, initialFormValues } from '../../schema';
import { AritoForm } from '@/components/custom/arito';
import { useAuth } from '@/contexts/auth-context';
import { transformFormData } from '../../utils';
import { useInputTableRows } from '@/hooks';
import { FormMode } from '@/types/form';

interface FormDialogProps {
  formMode: FormMode;
  initialData: any;
  detailRows: any[];
  setDetailRows: (rows: any[]) => void;
  onClose: () => void;
  onSubmit: (data: any) => Promise<void>;
}

export default function FormDialog({
  formMode,
  initialData,
  detailRows,
  setDetailRows,
  onClose,
  onSubmit
}: FormDialogProps) {
  const { entityUnit } = useAuth();
  const {
    rows,
    selectedRowUuid,
    handleRowClick,
    handleAddRow,
    handleDeleteRow,
    handleCopyRow,
    handlePasteRow,
    handleMoveRow,
    handleCellValueChange
  } = useInputTableRows<any>(detailRows, setDetailRows);
  const { totalAmount } = useCalculations(rows);
  const formFieldStates = useFormFieldStates(initialData);

  const handleSubmit = (data: any) => {
    const formData = transformFormData(data, formFieldStates, rows, totalAmount, entityUnit);
    console.log(formData);
    onSubmit(formData);
  };

  return (
    <AritoForm
      schema={FormSchema}
      initialData={initialData || initialFormValues}
      onSubmit={handleSubmit}
      mode={formMode}
      title={formMode === 'add' ? 'Mới' : undefined}
      subTitle='Bút toán điều chỉnh giảm công nợ'
      onClose={onClose}
      headerFields={<BasicInfo formMode={formMode} formFieldStates={formFieldStates} />}
      bottomBar={<BottomBar totalAmount={totalAmount} />}
      tabs={[
        {
          id: '1',
          label: 'Chi tiết',
          component: (
            <DetailTab
              formMode={formMode}
              rows={rows}
              selectedRowUuid={selectedRowUuid}
              handleRowClick={handleRowClick}
              handleAddRow={handleAddRow}
              handleDeleteRow={handleDeleteRow}
              handleCopyRow={handleCopyRow}
              handlePasteRow={handlePasteRow}
              handleMoveRow={handleMoveRow}
              handleCellValueChange={handleCellValueChange}
            />
          )
        },
        {
          id: '2',
          label: 'Tỷ giá',
          component: <ExchangeRateTab formMode={formMode} />
        },
        {
          id: '3',
          label: 'Khác',
          component: <OtherTab formMode={formMode} />
        }
      ]}
    />
  );
}
