import { useMemo, useState } from 'react';
import { HoaDonBanHangDichVu, ChiTietHoaDonBanHangDichVu } from '@/types/schemas';
import { getDataTableColumns } from '../cols-definition';

export const useTableData = (hoaDonBanDichVus: HoaDonBanHangDichVu[], handleViewClick: () => void) => {
  const [detailRows, setDetailRows] = useState<ChiTietHoaDonBanHangDichVu[]>([]);

  const { totalAmount, totalDiscount, totalTax } = useMemo(() => {
    const amount = detailRows.reduce((sum, row) => sum + (row.tien2 || 0), 0);
    const discount = detailRows.reduce((sum, row) => sum + (row.ck || 0), 0);
    const tax = detailRows.reduce((sum, row) => sum + (row.thue || 0), 0);
    return { totalAmount: amount, totalDiscount: discount, totalTax: tax };
  }, [detailRows]);

  const tables = [
    {
      name: 'Tất cả',
      rows: hoaDonBanDichVus,
      columns: getDataTableColumns(handleViewClick)
    },
    {
      name: 'Chưa ghi sổ',
      rows: hoaDonBanDichVus.filter(row => row.status === 'chua-ghi-so'),
      columns: getDataTableColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-[#EAD1DC]' />
    },
    {
      name: 'Đã ghi sổ',
      rows: hoaDonBanDichVus.filter(row => row.status === 'da-ghi-so'),
      columns: getDataTableColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-[#3D85C6]' />
    },
    {
      name: 'Chờ duyệt',
      rows: hoaDonBanDichVus.filter(row => row.status === 'cho-duyet'),
      columns: getDataTableColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-red-500' />
    },
    {
      name: 'Khác',
      rows: hoaDonBanDichVus.filter(
        row => row.status !== 'chua-ghi-so' && row.status !== 'da-ghi-so' && row.status !== 'cho-duyet'
      ),
      columns: getDataTableColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-black' />
    }
  ];

  return {
    hoaDonBanDichVus,
    detailRows,
    totalAmount,
    totalDiscount,
    totalTax,
    tables,
    setDetailRows
  };
};
