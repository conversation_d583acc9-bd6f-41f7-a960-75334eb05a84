import React from 'react';
import { FormField } from '@/components/custom/arito';
import { usePhuongThucThanhToan } from '@/hooks';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface EInvoiceTabProps {
  formMode: FormMode;
}

export default function EInvoiceTab({ formMode }: EInvoiceTabProps) {
  const { paymentMethods } = usePhuongThucThanhToan();

  return (
    <div className='p-4'>
      <div className='flex flex-col'>
        <div className='grid grid-cols-[150px,1fr] items-center'>
          <Label className='text-sm font-medium'>Trạng thái HĐĐT</Label>
          <FormField
            name='ma_tthddt'
            type='select'
            options={[
              { value: '0', label: 'Không sử dụng' },
              { value: '1', label: 'Chờ phát hành' }
            ]}
            disabled={formMode === 'view'}
          />
        </div>

        <div className='grid grid-cols-[150px,1fr] items-center'>
          <Label className='text-sm font-medium'>Thanh toán</Label>
          <FormField
            name='ma_pttt'
            type='select'
            options={paymentMethods.map(method => ({ value: method.uuid, label: method.ten_pttt }))}
            disabled={formMode === 'view'}
          />
        </div>

        <div className='grid grid-cols-[150px,1fr] items-center'>
          <Label className='text-sm font-medium'>Số hóa đơn</Label>
          <FormField name='so_ct_hddt' type='text' disabled={formMode === 'view'} />
        </div>

        <div className='grid grid-cols-[150px,1fr] items-center'>
          <Label className='text-sm font-medium'>Ngày hóa đơn</Label>
          <FormField name='ngay_ct_hddt' type='date' disabled={formMode === 'view'} />
        </div>

        <div className='grid grid-cols-[150px,1fr] items-center'>
          <Label className='text-sm font-medium'>Ký hiệu</Label>
          <FormField name='so_ct2_hddt' type='text' disabled={formMode === 'view'} />
        </div>

        <div className='grid grid-cols-[150px,1fr] items-center'>
          <Label className='text-sm font-medium'>Mẫu hóa đơn</Label>
          <FormField name='ma_mau_ct_hddt' type='text' disabled={formMode === 'view'} />
        </div>
      </div>
    </div>
  );
}
