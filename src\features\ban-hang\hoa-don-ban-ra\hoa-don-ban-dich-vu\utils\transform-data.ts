import { ChiTietHoaDonBanHangDichVu, ChiTietHoaDonBanHangDichVuInput } from '@/types/schemas';
import { FormFieldState } from '../hooks';

/**
 * Transform service detail rows for API submission
 * @param detailRows - Array of service detail row data from the form
 * @returns Transformed detail rows ready for API submission
 */
export const transformDetailRows = (detailRows: ChiTietHoaDonBanHangDichVu[]): ChiTietHoaDonBanHangDichVuInput[] => {
  return detailRows.map((row: any, index: number) => ({
    hoa_don: '', // Will be set by the API when creating the invoice
    line: index + 1,
    ma_dv: row.ma_dv_data?.uuid || '',
    tk_dt: row.tk_dt_data?.uuid || '',
    dvt: row.ma_dv_data?.dvt_data?.dvt || row.dvt || '',
    so_luong: row.so_luong || 0,
    gia_nt2: row.gia_vnd || 0, // Price in foreign currency (VND in this case)
    tien_nt2: row.thanh_tien_vnd || 0, // Amount in foreign currency
    dien_giai: row.dien_giai || '',
    tl_ck: row.tl_ck || 0,
    ck_nt: row.ck_vnd || 0, // Discount in foreign currency
    tk_ck: row.tk_ck_data?.uuid || '',
    ten_tk_ck: row.tk_ck_data?.name || row.ma_dv_data?.tk_ck_data?.name || '',
    ma_thue: row.thue_suat || '', // Tax code
    thue_suat: parseFloat(row.thue_suat) || 0, // Tax rate as number
    tk_thue_co: row.tk_thue_co_data?.uuid || '',
    ten_tk_thue_co: row.tk_thue_co_data?.name || '',
    thue_nt: row.thue_vnd || 0, // Tax in foreign currency
    gia2: row.gia_ban || 0, // Price in VND
    tien2: row.thanh_tien || 0, // Amount in VND
    ck: row.tong_ck || 0, // Discount in VND
    thue: row.tong_thue || 0, // Tax in VND

    // Additional fields
    ma_bp: row.ma_bp_data?.uuid || '',
    ma_vv: row.ma_vv_data?.uuid || '',
    ma_hd: row.ma_hd_data?.uuid || '',
    ma_dtt: row.ma_dtt_data?.uuid || '',
    ma_ku: row.ma_ku_data?.uuid || '',
    ma_phi: row.ma_phi_data?.uuid || '',
    ma_sp: row.ma_sp_data?.uuid || '',
    ma_lsx: row.lenh_sx || '', // Production order
    ma_cp0: row.ma_cp0_data?.uuid || ''
  }));
};

/**
 * Transform all service invoice form data for submission
 * @param data - Form data from the form submission
 * @param state - Form field state containing references to selected entities
 * @param detailRows - Array of service detail row data
 * @param totalAmount - Total amount calculated
 * @param totalDiscount - Total discount calculated
 * @param totalTax - Total tax calculated
 * @param entity - Current entity information
 * @returns Transformed data ready for API submission
 */
export const transformFormData = (
  data: any,
  state: FormFieldState,
  detailRows: ChiTietHoaDonBanHangDichVu[],
  totalAmount: number = 0,
  totalDiscount: number = 0,
  totalTax: number = 0,
  entity: any = null
) => {
  // Transform detail and tax rows
  const chi_tiet = transformDetailRows(detailRows);

  // Calculate final totals
  const finalAmount = totalAmount - totalDiscount + totalTax;

  // Build the final form data object
  return {
    // Basic entity and document info
    unit_id: entity?.uuid || '',
    ma_tt: data.ma_tt || '',
    ma_kh: state.khachHang?.uuid || '',
    tk: state.taiKhoan?.uuid || state.khachHang?.account_data?.uuid || '',
    ma_nk: state.quyenChungTu?.uuid || '',
    ma_nvbh: state.nhanVien?.uuid || state.khachHang?.sales_rep_data?.uuid || '',
    ma_tthddt: data.ma_tthddt || '',

    // Customer information from state and form
    ma_so_thue: state.khachHang?.tax_code || data.ma_so_thue || '',
    ten_kh_thue: state.khachHang?.customer_name || data.ten_kh_thue || '',
    dia_chi: state.khachHang?.address || data.dia_chi || '',
    ong_ba: data.ong_ba || state.khachHang?.contact_person || '',
    e_mail: data.e_mail || state.khachHang?.email || '',
    dien_giai: data.dien_giai || state.khachHang?.description || '',

    // Document dates and references
    ngay_ct: data.ngay_ct || '',
    ngay_lct: data.ngay_ct || '',
    so_ct: state.soChungTuHienTai || state.quyenChungTu?.so_ct_mau || '',
    so_ct2: '',
    i_so_ct: state.quyenChungTu?.i_so_ct_ht,

    // Currency and exchange rate
    ma_nt: data.ma_nt || '',
    ty_gia: 1,

    // Status and other fields
    status: data.status || '',
    ly_do_huy: data.ly_do_huy || '',
    ly_do: data.ly_do || '',
    ten_vt_thue: data.ten_vt_thue || '',
    ghi_chu: data.ghi_chu || '',

    // E-invoice information
    so_ct_hddt: data.so_ct_hddt || '',
    ngay_ct_hddt: data.ngay_ct_hddt || '',
    so_ct2_hddt: data.so_ct2_hddt || '',
    ma_mau_ct_hddt: data.ma_mau_ct_hddt || '',

    // Payment and processing flags
    pt_tao_yn: state.pt_tao_yn,
    transfer_yn: state.transfer_yn,
    ma_httt: data.ma_httt || 'TMB',
    ma_pttt: data.ma_pttt || '',

    // Additional references
    ma_ngv: '1',
    ma_kh9: state.coQuanThue?.uuid || '',

    // Financial totals
    t_tien_nt2: totalAmount,
    t_tien2: totalAmount,
    t_thue_nt: totalTax,
    t_thue: totalTax,
    t_ck_nt: totalDiscount,
    t_ck: totalDiscount,
    t_tt_nt: finalAmount,
    t_tt: finalAmount,

    // Detail arrays
    chi_tiet
  };
};
