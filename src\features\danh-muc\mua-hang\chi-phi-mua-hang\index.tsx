'use client';
import { useState } from 'react';
import { ActionBar, FormDialog, DeleteDialog, ConfirmDialog } from './components';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { ChiPhiMuaHang, ChiPhiMuaHangInput } from '@/types/schemas';
import { useChiPhiMuaHang, useFormState, useRows } from '@/hooks';
import { LoadingOverlay } from '@/components/custom/arito';
import { exportMainColumns } from './cols-definition';

export default function ChiPhiMuaHangPage() {
  const {
    chiPhiMuaHangs,
    isLoading,
    addChiPhiMuaHang,
    updateChiPhiMuaHang,
    deleteChiPhiMuaHang,
    refreshChiPhiMuaHangs
  } = useChiPhiMuaHang();

  const {
    showForm,
    showDelete,
    formMode,
    isCopyMode,
    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick
  } = useFormState<ChiPhiMuaHang>();

  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRows<ChiPhiMuaHang>();
  const [showDuplicateWarning, setShowDuplicateWarning] = useState(false);

  const handleSubmit = async (data: ChiPhiMuaHangInput) => {
    try {
      if (formMode === 'add') {
        await addChiPhiMuaHang(data);
      } else if (formMode === 'edit' && selectedObj) {
        await updateChiPhiMuaHang(selectedObj.uuid, data);
      }

      handleCloseForm();
      clearSelection();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  const tables = [
    {
      name: '',
      rows: chiPhiMuaHangs,
      columns: exportMainColumns()
    }
  ];

  return (
    <div className='flex h-[calc(100vh-10%)] w-screen flex-col lg:overflow-hidden'>
      <ActionBar
        onAddClick={handleAddClick}
        onEditClick={() => selectedObj && handleEditClick()}
        onCopyClick={() => selectedObj && handleCopyClick()}
        onViewClick={() => selectedObj && handleViewClick()}
        onDeleteClick={() => selectedObj && handleDeleteClick()}
        onRefreshClick={refreshChiPhiMuaHangs}
      />
      {isLoading && <LoadingOverlay />}

      {!isLoading && (
        <AritoDataTables tables={tables} onRowClick={handleRowClick} selectedRowId={selectedRowIndex || undefined} />
      )}

      {showForm && (
        <FormDialog
          open={showForm}
          onSubmit={handleSubmit}
          formMode={formMode}
          initialData={formMode === 'add' && !isCopyMode ? undefined : selectedObj}
          onClose={handleCloseForm}
          onAdd={() => {
            handleCloseForm();
            clearSelection();
            setTimeout(() => {
              handleAddClick();
            }, 100);
          }}
          onEdit={() => {
            if (selectedObj) {
              handleCloseForm();
              handleEditClick();
            }
          }}
          onDelete={() => {
            if (selectedObj) {
              handleCloseForm();
              handleDeleteClick();
            }
          }}
          onCopy={() => {
            if (selectedObj) {
              handleCloseForm();
              handleCopyClick();
            }
          }}
        />
      )}

      {showDelete && (
        <DeleteDialog
          open={showDelete}
          onClose={handleCloseDelete}
          selectedObj={selectedObj}
          deleteObj={deleteChiPhiMuaHang}
          clearSelection={clearSelection}
        />
      )}

      <ConfirmDialog
        open={showDuplicateWarning}
        onClose={() => setShowDuplicateWarning(false)}
        onConfirm={() => setShowDuplicateWarning(false)}
        title='Cảnh báo'
        content='Mã chi phí đã có trong danh mục chi phí'
        confirmText='Đồng ý'
        showCancel={false}
      />
    </div>
  );
}
