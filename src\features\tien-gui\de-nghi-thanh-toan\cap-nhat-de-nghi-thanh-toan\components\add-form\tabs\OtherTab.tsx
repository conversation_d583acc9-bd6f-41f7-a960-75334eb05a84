import { ChangeEvent, useState, useEffect } from 'react';
import { useFormContext } from 'react-hook-form';
import { UploadCloud } from 'lucide-react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface Props {
  value: any[];
  onChange?: (newValue: any[]) => void;
  onFileChange?: (file: File | null) => void;
  formMode: FormMode;
}

export const OtherTab = ({ value, onChange, onFileChange, formMode }: Props) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const { watch } = useFormContext();

  // Debug form values from OtherTab
  useEffect(() => {
    const subscription = watch((value, { name, type }) => {
      if (name && ['dien_giai_ct_goc', 'i_so_ct', 'so_ct_goc', 'email_tq', 'attachedFileName'].includes(name)) {
        console.log(`📝 OtherTab field changed - ${name}:`, value[name]);
      }
    });
    return () => subscription.unsubscribe();
  }, [watch]);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setSelectedFile(file);
    onFileChange?.(file);

    // Log file change for debugging
    console.log('📎 File selected in OtherTab:', file?.name || 'No file');
  };

  return (
    <div className='space-y-4 p-6'>
      <div className='flex items-center gap-4'>
        <Label className='w-40 min-w-40 text-left text-sm font-medium'>Mã đối tượng</Label>
        <div className='w-80'>
          <FormField type='text' name='dien_giai_ct_goc' disabled={formMode === 'view'} className='h-9' />
        </div>
      </div>

      <div className='flex items-center gap-4'>
        <Label className='w-40 min-w-40 text-left text-sm font-medium'>Kèm theo</Label>
        <div className='w-80'>
          <FormField type='number' name='i_so_ct' disabled={formMode === 'view'} className='h-9' />
        </div>
      </div>

      <div className='flex items-center gap-4'>
        <Label className='w-40 min-w-40 text-left text-sm font-medium'>Chứng từ gốc</Label>
        <div className='w-80'>
          <FormField name='so_ct_goc' type='text' disabled={formMode === 'view'} className='h-9' />
        </div>
      </div>

      <div className='flex items-center gap-4'>
        <Label className='w-40 min-w-40 text-left text-sm font-medium'>Email thủ quỹ</Label>
        <div className='w-80'>
          <FormField name='email_tq' disabled={formMode === 'view'} className='h-9' />
        </div>
      </div>

      <div className='flex items-center gap-4'>
        <Label className='w-40 min-w-40 text-left text-sm font-medium'>Tệp đính kèm</Label>
        <div className='flex items-center gap-3'>
          <Label
            htmlFor='file-upload'
            className={`flex cursor-pointer items-center gap-2 rounded-md border px-4 py-2 text-sm font-medium transition-colors ${
              formMode === 'view'
                ? 'cursor-not-allowed border-gray-300 bg-gray-100 text-gray-500'
                : 'border-gray-300 bg-white text-gray-700 hover:border-gray-400 hover:bg-gray-50'
            }`}
          >
            <UploadCloud className='h-4 w-4' />
            <span>{selectedFile ? 'Thay đổi file' : 'Chọn file'}</span>
          </Label>
          <Input
            id='file-upload'
            type='file'
            className='hidden'
            onChange={handleFileChange}
            disabled={formMode === 'view'}
            accept='.pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png'
          />
          {selectedFile && (
            <div className='flex items-center gap-2 rounded-md border bg-gray-50 px-3 py-2'>
              <span className='text-sm text-gray-700'>{selectedFile.name}</span>
              <span className='text-xs text-gray-500'>({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)</span>
            </div>
          )}
        </div>
      </div>

      {/* Hidden field to capture file name in form */}
      <div style={{ display: 'none' }}>
        <FormField name='attachedFileName' type='text' value={selectedFile?.name || ''} />
      </div>
    </div>
  );
};
