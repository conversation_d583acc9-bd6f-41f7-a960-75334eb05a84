import { useState, useEffect } from 'react';
import {
  DieuChuyenBoPhanSuDungTSCD,
  DieuChuyenBoPhanSuDungTSCDInput,
  DieuChuyenBoPhanSuDungTSCDResponse
} from '@/types/schemas/dieu-chuyen-bo-phan-su-dung-tscd.type';
import { useAuth } from '@/contexts/auth-context';
import { QUERY_KEYS } from '@/constants';
import api from '@/lib/api';

interface UseDieuChuyenBoPhanSuDungTSCDReturn {
  dieuChuyenBoPhanSuDungTSCDs: DieuChuyenBoPhanSuDungTSCD[];
  isLoading: boolean;
  error: string | null;
  addDieuChuyenBoPhanSuDungTSCD: (newDieuChinh: DieuChuyenBoPhanSuDungTSCDInput) => Promise<void>;
  updateDieuChuyenBoPhanSuDungTSCD: (uuid: string, updatedDieuChinh: DieuChuyenBoPhanSuDungTSCDInput) => Promise<void>;
  deleteDieuChuyenBoPhanSuDungTSCD: (uuid: string) => Promise<void>;
  refreshDieuChuyenBoPhanSuDungTSCDs: () => Promise<void>;
  getDieuChuyenBoPhanSuDungTSCDsByTaiSan: (ma_ts: string) => Promise<DieuChuyenBoPhanSuDungTSCD[]>;
  getDieuChuyenBoPhanSuDungTSCDsByKyNam: (ky: number, nam: number) => Promise<DieuChuyenBoPhanSuDungTSCD[]>;
}

/**
 * Hook for managing DieuChuyenBoPhanSuDungTSCD (Fixed Asset Value Adjustment) data
 *
 * This hook provides functions to fetch, create, update, and delete fixed asset value adjustments.
 * It follows the established pattern from other query hooks in the codebase.
 */
export const useDieuChuyenBoPhanSuDungTSCD = (
  initialDieuChuyenBoPhanSuDungTSCDs: DieuChuyenBoPhanSuDungTSCD[] = []
): UseDieuChuyenBoPhanSuDungTSCDReturn => {
  const [dieuChuyenBoPhanSuDungTSCDs, setDieuChuyenBoPhanSuDungTSCDs] = useState<DieuChuyenBoPhanSuDungTSCD[]>(
    initialDieuChuyenBoPhanSuDungTSCDs
  );
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const { entity } = useAuth();

  const fetchDieuChuyenBoPhanSuDungTSCDs = async () => {
    if (!entity?.slug) return;

    setIsLoading(true);
    setError(null);
    try {
      const response = await api.get<DieuChuyenBoPhanSuDungTSCDResponse>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.DIEU_CHUYEN_BO_PHAN_SU_DUNG_TSCD}/`
      );

      setDieuChuyenBoPhanSuDungTSCDs(response.data.results);
    } catch (error: any) {
      const errorMessage = error.message || 'Error fetching dieu chuyen bo phan su dung TSCD';
      setError(errorMessage);
      console.error('Error fetching dieu chuyen bo phan su dung TSCD:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const addDieuChuyenBoPhanSuDungTSCD = async (newDieuChinh: DieuChuyenBoPhanSuDungTSCDInput): Promise<void> => {
    if (!entity?.slug) return;

    try {
      const response = await api.post<DieuChuyenBoPhanSuDungTSCD>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.DIEU_CHUYEN_BO_PHAN_SU_DUNG_TSCD}/`,
        newDieuChinh
      );

      setDieuChuyenBoPhanSuDungTSCDs(prev => [...prev, response.data]);
    } catch (error: any) {
      const errorMessage = error.message || 'Error adding dieu chuyen bo phan su dung TSCD';
      setError(errorMessage);
      console.error('Error adding dieu chuyen bo phan su dung TSCD:', error);
      throw error;
    }
  };

  const updateDieuChuyenBoPhanSuDungTSCD = async (
    uuid: string,
    updatedAdjustment: DieuChuyenBoPhanSuDungTSCDInput
  ): Promise<void> => {
    if (!entity?.slug) return;

    try {
      const response = await api.put<DieuChuyenBoPhanSuDungTSCD>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.DIEU_CHUYEN_BO_PHAN_SU_DUNG_TSCD}/${uuid}/`,
        updatedAdjustment
      );

      setDieuChuyenBoPhanSuDungTSCDs(prev =>
        prev.map(dieuChinh => (dieuChinh.uuid === uuid ? response.data : dieuChinh))
      );
    } catch (error: any) {
      const errorMessage = error.message || 'Error updating dieu chuyen bo phan su dung TSCD';
      setError(errorMessage);
      console.error('Error updating dieu chuyen bo phan su dung TSCD:', error);
      throw error;
    }
  };

  const deleteDieuChuyenBoPhanSuDungTSCD = async (uuid: string): Promise<void> => {
    if (!entity?.slug) return;

    try {
      await api.delete(`/entities/${entity.slug}/erp/${QUERY_KEYS.DIEU_CHUYEN_BO_PHAN_SU_DUNG_TSCD}/${uuid}/`);

      setDieuChuyenBoPhanSuDungTSCDs(prev => prev.filter(dieuChinh => dieuChinh.uuid !== uuid));
    } catch (error: any) {
      const errorMessage = error.message || 'Error deleting dieu chuyen bo phan su dung TSCD';
      setError(errorMessage);
      console.error('Error deleting dieu chuyen bo phan su dung TSCD:', error);
      throw error;
    }
  };

  const refreshDieuChuyenBoPhanSuDungTSCDs = async (): Promise<void> => {
    await fetchDieuChuyenBoPhanSuDungTSCDs();
  };

  const getDieuChuyenBoPhanSuDungTSCDsByTaiSan = async (assetUuid: string): Promise<DieuChuyenBoPhanSuDungTSCD[]> => {
    if (!entity?.slug) return [];

    try {
      const response = await api.get<DieuChuyenBoPhanSuDungTSCDResponse>(
        `/entities/${entity.slug}/erp/tai-san/dieu-chuyen-tscd/dieu-chuyen-bo-phan-su-dung-tscd/`,
        {
          params: { ma_ts: assetUuid }
        }
      );

      return response.data.results;
    } catch (error: any) {
      const errorMessage = error.message || 'Error fetching dieu chuyen bo phan su dung TSCD by asset';
      setError(errorMessage);
      console.error('Error fetching dieu chuyen bo phan su dung TSCD by asset:', error);
      return [];
    }
  };

  const getDieuChuyenBoPhanSuDungTSCDsByKyNam = async (
    ky: number,
    nam: number
  ): Promise<DieuChuyenBoPhanSuDungTSCD[]> => {
    if (!entity?.slug) return [];

    try {
      const response = await api.get<DieuChuyenBoPhanSuDungTSCDResponse>(
        `/entities/${entity.slug}/erp/tai-san/dieu-chuyen-tscd/dieu-chuyen-bo-phan-su-dung-tscd/`,
        {
          params: { ky, nam }
        }
      );

      return response.data.results;
    } catch (error: any) {
      const errorMessage = error.message || 'Error fetching dieu chuyen bo phan su dung TSCD by period';
      setError(errorMessage);
      console.error('Error fetching dieu chuyen bo phan su dung TSCD by period:', error);
      return [];
    }
  };

  useEffect(() => {
    fetchDieuChuyenBoPhanSuDungTSCDs();
  }, [entity?.slug]);

  return {
    dieuChuyenBoPhanSuDungTSCDs,
    isLoading,
    error,
    addDieuChuyenBoPhanSuDungTSCD,
    updateDieuChuyenBoPhanSuDungTSCD,
    deleteDieuChuyenBoPhanSuDungTSCD,
    refreshDieuChuyenBoPhanSuDungTSCDs,
    getDieuChuyenBoPhanSuDungTSCDsByTaiSan,
    getDieuChuyenBoPhanSuDungTSCDsByKyNam
  };
};
