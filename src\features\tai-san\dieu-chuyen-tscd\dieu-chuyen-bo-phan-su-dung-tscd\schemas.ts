import { z } from 'zod';

export const formSchema = z.object({
  ma_ts: z.string().optional(),
  ky: z.coerce.number().optional(),
  nam: z.coerce.number().optional(),
  ma_bp: z.string().nonempty('Bộ phận là bắt buộc'),
  tk_ts: z.string().nonempty('Tài sản là bắt buộc'),
  tk_kh: z.string().nonempty('Khấu hao là bắt buộc'),
  tk_cp: z.string().nonempty('Chi phí là bắt buộc')
});

export type FormValues = z.infer<typeof formSchema>;

export const initialValues: FormValues = {
  ma_ts: '',
  ky: 6,
  nam: new Date().getFullYear(),
  ma_bp: '',
  tk_ts: '',
  tk_kh: '',
  tk_cp: ''
};
