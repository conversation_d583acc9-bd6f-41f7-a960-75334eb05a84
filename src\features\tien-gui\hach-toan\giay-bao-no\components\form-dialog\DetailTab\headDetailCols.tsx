import {
  boPhanSearchColumns,
  chiPhiSearchColumns,
  dotThanhToanSearchColumns,
  hopDongSearchColumns,
  khachHangSearchColumns,
  kheUocSearchColumns,
  lenhSanXuatSearchColumns,
  phiSearchColumns,
  QUERY_KEYS,
  vatTuSearchColumns,
  vuViecSearchColumns
} from '@/constants';
import type { BoPhan, ChiPhi, DotThanhToan, HopDong, KhachHang, KheUoc, Phi, VatTu, VuViec } from '@/types/schemas';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { SearchField } from '@/components/custom/arito';

export const headColumns = (onCellValueChange: (rowUuid: string, field: string, newValue: any) => void) => [
  {
    field: 'dien_giai',
    headerName: '<PERSON>ễn giải',
    width: 200,
    renderCell: (params: any) => (
      <CellField
        name='dien_giai'
        type='text'
        value={params.row.dien_giai || ''}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'dien_giai', newValue)}
      />
    )
  },
  {
    field: 'ma_kh',
    headerName: 'Mã đối tượng',
    width: 120,
    renderCell: (params: any) => (
      <SearchField<KhachHang>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
        searchColumns={khachHangSearchColumns}
        columnDisplay='customer_code'
        dialogTitle='Danh mục đối tượng'
        value={params.row.ma_kh_data?.customer_code}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_kh_data', row)}
      />
    )
  },
  {
    field: 'ten_kh',
    headerName: 'Tên đối tượng',
    width: 200,
    renderCell: (params: any) => (
      <CellField name='ten_kh' type='text' value={params.row.ma_kh_data?.customer_name || ''} />
    )
  }
];
export const tailColumns = (onCellValueChange: (rowUuid: string, field: string, newValue: any) => void) => [
  {
    field: 'so_ct_dn',
    headerName: 'Số đề nghị',
    width: 120,
    renderCell: (params: any) => (
      <CellField
        name='so_ct_dn'
        type='text'
        value={params.row.so_ct_dn || ''}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'so_ct_dn', newValue)}
      />
    )
  },
  {
    field: 'line_dn',
    headerName: 'Dòng DN',
    width: 100,
    renderCell: (params: any) => (
      <CellField
        name='line_dn'
        type='number'
        value={params.row.line_dn || 0}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'line_dn', newValue)}
      />
    )
  },
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 120,
    renderCell: (params: any) => (
      <SearchField<BoPhan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
        searchColumns={boPhanSearchColumns}
        columnDisplay='ma_bp'
        dialogTitle='Danh mục bộ phận'
        value={params.row.ma_bp_data?.ma_bp || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_bp_data', row)}
      />
    )
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 120,
    renderCell: (params: any) => (
      <SearchField<VuViec>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VU_VIEC}/`}
        searchColumns={vuViecSearchColumns}
        columnDisplay='ma_vu_viec'
        dialogTitle='Danh mục vụ việc'
        value={params.row.ma_vv_data?.ma_vu_viec || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_vv_data', row)}
      />
    )
  },
  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 120,
    renderCell: (params: any) => (
      <SearchField<HopDong>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.HOP_DONG}/`}
        searchColumns={hopDongSearchColumns}
        columnDisplay='ma_hd'
        dialogTitle='Danh mục hợp đồng'
        value={params.row.ma_hd_data?.ma_hd || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_hd_data', row)}
      />
    )
  },
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 120,
    renderCell: (params: any) => (
      <SearchField<DotThanhToan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}/`}
        searchColumns={dotThanhToanSearchColumns}
        columnDisplay='ma_dtt'
        dialogTitle='Danh mục đợt thanh toán'
        value={params.row.ma_dtt_data?.ma_dtt || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_dtt_data', row)}
      />
    )
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 120,
    renderCell: (params: any) => (
      <SearchField<KheUoc>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHE_UOC}/`}
        searchColumns={kheUocSearchColumns}
        columnDisplay='ma_ku'
        dialogTitle='Danh mục khế ước'
        value={params.row.ma_ku_data?.ma_ku || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_ku_data', row)}
      />
    )
  },
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 120,
    renderCell: (params: any) => (
      <SearchField<Phi>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.PHI}/`}
        searchColumns={phiSearchColumns}
        columnDisplay='ma_phi'
        dialogTitle='Danh mục phí'
        value={params.row.ma_phi_data?.ma_phi || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_phi_data', row)}
      />
    )
  },
  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 120,
    renderCell: (params: any) => (
      <SearchField<VatTu>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
        searchColumns={vatTuSearchColumns}
        columnDisplay='ma_vt'
        dialogTitle='Danh mục sản phẩm'
        value={params.row.ma_sp_data?.ma_vt || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_sp_data', row)}
      />
    )
  },
  {
    field: 'ma_lsx',
    headerName: 'Lệnh sản xuất',
    width: 120,
    renderCell: (params: any) => (
      <SearchField<any>
        type='text'
        searchEndpoint={'/'}
        searchColumns={lenhSanXuatSearchColumns}
        columnDisplay='ma_lsx'
        dialogTitle='Danh mục lệnh sản xuất'
        value={params.row.ma_lsx_data?.ma_lsx || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_lsx_data', row)}
      />
    )
  },
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 120,
    renderCell: (params: any) => (
      <SearchField<ChiPhi>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.CHI_PHI}/`}
        searchColumns={chiPhiSearchColumns}
        columnDisplay='ma_cp'
        dialogTitle='Danh mục chi phí không hợp lệ'
        value={params.row.ma_cp0_data?.ma_cp || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_cp0_data', row)}
      />
    )
  }
];
