import { Button } from '@mui/material';
import { useState } from 'react';
import { AritoDialog, AritoIcon } from '@/components/custom/arito';

interface ConfirmDialogProps {
  open: boolean;
  selectedObj?: any | null;
  onClose: () => void;
  onDelete?: (uuid: string) => Promise<any>;
  clearSelection?: () => void;
  title?: string;
  content?: string;
}

const ConfirmDialog = ({
  open,
  selectedObj,
  onClose,
  onDelete,
  clearSelection,
  title = 'Xóa dữ liệu',
  content = 'Bạn có chắc chắn muốn xóa không?'
}: ConfirmDialogProps) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleConfirm = async () => {
    console.log('🗑️ ConfirmDialog handleConfirm called');
    console.log('🔍 selectedObj:', selectedObj);
    console.log('🔍 selectedObj.uuid:', selectedObj?.uuid);
    console.log('🔍 onDelete function:', onDelete);

    setIsDeleting(true);
    setError(null);

    try {
      if (!selectedObj?.uuid) {
        throw new Error('Không tìm thấy UUID của đối tượng cần xóa');
      }

      console.log('📞 Calling onDelete with UUID:', selectedObj.uuid);
      await onDelete?.(selectedObj.uuid);
      console.log('✅ Delete completed successfully');
      onClose();
      clearSelection?.();
    } catch (error: any) {
      console.error('❌ Error deleting payment request:', error);
      setError(error.message || 'Có lỗi xảy ra khi xóa dữ liệu');
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title={title}
      maxWidth='sm'
      disableBackdropClose={isDeleting}
      disableEscapeKeyDown={isDeleting}
      showFullscreenToggle={false}
      titleIcon={<AritoIcon icon={260} />}
      actions={
        <>
          <Button
            onClick={handleConfirm}
            type='submit'
            variant='contained'
            className='bg-[rgba(15,118,110,0.9)] normal-case hover:bg-[rgba(15,118,110,1)]'
            disabled={isDeleting}
          >
            <AritoIcon icon={884} className='mr-2' />
            {isDeleting ? 'Đang xóa...' : 'Đồng ý'}
          </Button>
          <Button onClick={onClose} variant='outlined' disabled={isDeleting}>
            <AritoIcon icon={885} className='mr-2' />
            Huỷ
          </Button>
        </>
      }
    >
      <div className='min-w-[40vw] p-4'>
        <p className='text-base font-medium'>{content}</p>
        {selectedObj && (
          <div className='mt-4 rounded-md bg-gray-50 p-3'>
            <h4 className='mb-2 text-sm font-semibold text-gray-700'>Thông tin đề nghị thanh toán:</h4>
            <div className='space-y-1 text-sm text-gray-600'>
              <p>
                <strong>UUID:</strong>{' '}
                <code className='rounded bg-gray-200 px-1 text-xs'>{selectedObj.uuid || 'N/A'}</code>
              </p>
              <p>
                <strong>Số chứng từ:</strong> {selectedObj.doc_number || selectedObj.so_ct || 'N/A'}
              </p>
              <p>
                <strong>Diễn giải:</strong> {selectedObj.dien_giai || selectedObj.description || 'N/A'}
              </p>
              <p>
                <strong>Người đề nghị:</strong> {selectedObj.supplier_name || selectedObj.ong_ba || 'N/A'}
              </p>
              <p>
                <strong>Ngày chứng từ:</strong> {selectedObj.doc_date || selectedObj.ngay_ct || 'N/A'}
              </p>
              <p>
                <strong>Trạng thái:</strong> {selectedObj.status || 'N/A'}
              </p>
            </div>
          </div>
        )}
        {error && (
          <div className='mt-3 rounded-md bg-red-50 p-3'>
            <p className='text-sm text-red-800'>{error}</p>
          </div>
        )}
      </div>
    </AritoDialog>
  );
};

export default ConfirmDialog;
