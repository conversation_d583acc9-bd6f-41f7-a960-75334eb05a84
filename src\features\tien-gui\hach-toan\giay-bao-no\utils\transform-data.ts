const transformDetailRows = (detailRows: any[]) => {
  return detailRows.map((row: any, index: number) => ({
    line: index + 1,

    dien_giai: row.dien_giai || '',
    ma_kh: row.ma_kh_data?.uuid || '',
    ten_kh: row.ma_kh_data?.customer_name || '',

    so_ct_dn: row.so_ct_dn || '',
    line_dn: row.line_dn || 0,
    ma_bp: row.ma_bp_data?.uuid || '',
    ma_vv: row.ma_vv_data?.uuid || null,
    ma_hd: row.ma_hd_data?.uuid || null,
    ma_dtt: row.ma_dtt_data?.uuid || null,
    ma_ku: row.ma_ku_data?.uuid || null,
    ma_phi: row.ma_phi_data?.uuid || null,
    ma_sp: row.ma_sp_data?.uuid || null,
    ma_lsx: row.ma_lsx_data?.uuid || null,
    ma_cp0: row.ma_cp0_data?.uuid || null,

    du_cn: row.du_cn || 0.0,
    id_hd: row.id_hd || '',
    so_ct0_hd: row.so_ct0_hd || '',
    ngay_ct_hd: row.ngay_ct_hd || '',
    tk_no: row.tk_no_data?.uuid || '',
    ma_nt_hd: row.ma_nt_hd_data?.uuid || '',
    ty_gia_hd: row.ty_gia_hd || 0.0,
    tien_hd_nt: row.tien_hd_nt || 0.0,
    da_pb_nt: row.da_pb_nt || 0.0,
    cl_nt: row.cl_nt || 0.0,
    tien_nt: row.tien_nt || 0.0,

    tknh2: row.tk_ngan_hang?.uuid || '',

    ma_loai_hd: row.ma_loai_hd || '0',
    ma_thue: row.ma_thue_data?.uuid || '',
    ten_thue: row.ma_thue_data?.ten_thue || '',
    thue_suat: row.ma_thue_data?.thue_suat || 0.0,
    tk_thue: row.tk_thue_data?.uuid || '',
    ten_tk_thue: row.tk_thue_data?.name || '',
    so_ct0: row.so_ct0 || '',
    so_ct2: row.so_ct2 || '',
    ngay_ct0: row.ngay_ct0 || '',
    ma_mau_ct: row.ma_mau_ct_data?.uuid || '',
    ten_mau_ct: row.ma_mau_ct_data?.ten_mau_ct || '',
    ma_mau_bc: row.ma_mau_bc || '',
    ma_tc_thue: row.ma_tc_thue || '',
    ma_kh_thue: row.ma_kh_thue_data?.uuid || '',
    ten_kh_thue: row.ma_kh_thue_data?.customer_name || row.ten_kh_thue || '',
    dia_chi: row.dia_chi || row.ma_kh_thue_data?.address || '',
    ma_so_thue: row.ma_so_thue || row.ma_kh_thue_data?.tax_code || '',
    ten_vt_thue: row.ten_vt_thue || '',
    thue_nt: row.thue_nt || 0.0,
    thue: row.thue || 0.0,
    ma_kh9: row.ma_kh9_data?.uuid || '',
    ten_kh9: row.ma_kh9_data?.customer_name || '',

    ma_vt: row.ma_vt_data?.uuid || '',
    dvt: row.ma_vt_data?.dvt || '',
    ma_kho: row.ma_kho_data?.uuid || '',
    ct_km: row.ct_km || '0',
    so_luong: row.so_luong,
    gia_nt2: row.gia_nt2,
    tien_nt2: row.tien_nt2,
    px_dd: row.px_dd,
    gia_nt: row.gia_nt,
    thanh_tien: row.thanh_tien,
    don_gia: row.don_gia,
    giam_gia: row.giam_gia,
    tk_thue_co: row.tk_thue_co_data?.uuid || '',
    tk_dt: row.tk_dt_data?.uuid || '',
    tk_gv: row.tk_gv_data?.uuid || '',
    tk_vt: row.tk_vt_data?.uuid || '',
    tk_ck: row.tk_ck_data?.uuid || '',
    ghi_chu: row.ghi_chu || '',
    sl_px: row.sl_px || null,
    line_dh: row.line_dh || null,
    line_hd: row.line_hd || null
  }));
};

/**
 * Transform tax rows for API submission
 * @param thueRows - Array of tax row data from the ThueTab form
 * @returns Transformed tax rows ready for API submission
 */
const transformThueRows = (thueRows: any[]) => {
  return thueRows.map((row: any, index: number) => ({
    line: index + 1,
    // Invoice information
    so_ct0: row.so_hoa_don || '',
    so_ct2: row.ky_hieu || '',
    ngay_ct0: row.ngay_hoa_don || '',

    // Tax information
    ma_thue: row.ma_thue || '',
    ma_mau_ct: row.ma_mau_ct_data?.uuid || '',
    ma_mau_bc: row.mau_bao_cao || '',
    ma_tc_thue: row.ma_tc_thue?.uuid || '',

    // Supplier information
    ma_kh: row.ma_ncc_data?.uuid || '',
    ten_kh_thue: row.ma_ncc_data?.customer_name || '',
    dia_chi: row.ma_ncc_data?.address || '',
    ma_so_thue: row.ma_ncc_data?.tax_code || '',

    // Product/Service information
    ten_vt_thue: row.ten_hang_hoa_dv || '',

    // Financial amounts
    t_tien_nt: row.tien_hang_vnd || 0.0,
    t_thue_nt: row.thue_vnd || 0.0,

    // Account information
    tk_thue_no: row.tk_thue_data?.uuid || '',
    tk_du: row.tk_du_data?.uuid || '',

    // Tax authority
    ma_kh9: row.customer_code_data?.uuid || '',

    // Payment information
    ma_tt: row.ma_thanh_toan_data?.uuid || '',

    // Additional information
    ghi_chu: row.ghi_chu || '',

    // Department and project information
    ma_bp: row.ma_bp_data?.uuid || '',
    ma_vv: row.ma_vv_data?.uuid || '',
    ma_hd: row.ma_hop_dong || '',
    ma_dtt: row.dot_thanh_toan || '',
    ma_ku: row.ma_ku_data?.uuid || '',
    ma_phi: row.ma_phi_data?.uuid || '',
    ma_sp: row.ma_vt_data?.uuid || '',
    ma_lsx: row.ma_lsx_data?.uuid || '',
    ma_cp0: row.ma_cp_data?.uuid || ''
  }));
};

/**
 * Transform bank fee rows for API submission
 * @param phiNganHangRows - Array of bank fee row data from the PhiNganHangTab form
 * @returns Transformed bank fee rows ready for API submission
 */
export const transformPhiNganHangRows = (phiNganHangRows: any[]) => {
  return phiNganHangRows.map((row: any, index: number) => ({
    line: index + 1,
    // Supplier information
    ma_kh: row.ma_kh_data?.uuid || '',
    ten_kh: row.ma_kh_data?.ten_kh || '',

    // Bank fee information
    ma_cpnh: row.ma_cpnh_data?.uuid || '',
    ten_cpnh: row.ma_cpnh_data?.ten_cpnh || '',
    tien_cp_nt: row.tien_cp_nt || 0.0,

    // Invoice information
    so_ct0: row.so_ct0 || '',
    so_ct2: row.so_ct2 || '',
    ngay_ct0: row.ngay_ct0 || '',

    // Description
    dien_giai: row.dien_giai || '',

    // Account information
    tk_cpnh: row.tk_cpnh_data?.uuid || '',
    tk_du: row.tk_du_data?.uuid || '',
    tk_thue: row.tk_thue_data?.uuid || '',

    // Tax information
    ma_thue: row.ma_thue_data?.uuid || '',
    t_thue_nt: row.t_thue_nt || 0.0,

    // Department and project information
    ma_bp: row.ma_bp_data?.uuid || '',
    ma_vv: row.ma_vv_data?.uuid || '',
    ma_hd: row.ma_hd_data?.uuid || '',
    ma_dtt: row.ma_dtt_data?.uuid || '',
    ma_ku: row.ma_ku_data?.uuid || '',
    ma_phi: row.ma_phi_data?.uuid || '',
    ma_sp: row.ma_sp_data?.uuid || '',
    ma_lsx: row.ma_lsx_data?.uuid || '',
    ma_cp0: row.ma_cp0_data?.uuid || ''
  }));
};

export const transformFormData = (data: any, state: any, ...rest: any[]) => {
  // Transform detail and account rows
  const detail = transformDetailRows(rest[0]);

  // Build the final form data object
  return {
    ...data,

    // Search field
    ma_ngv: state.loaiChungTu || '0',
    tknh: state.bank?.uuid || '',
    tk: state.account?.uuid || '',
    ma_nk: state.quyenChungTu?.uuid || '',
    loai_hd: state.loai_hd || '1',
    ma_kh0: state.donViNhanTien?.uuid || '',
    ma_ngan_hang: state.nganHangNhanTien?.uuid || '',
    ma_tt: state.hanTT?.uuid || '',

    // Detail arrays
    chi_tiet: detail
  };
};
