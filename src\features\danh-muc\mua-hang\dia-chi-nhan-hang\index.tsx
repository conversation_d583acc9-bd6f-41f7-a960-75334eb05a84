'use client';
import ConfirmationDialog from '@/components/custom/arito/dialog/confirm-dialog';
import type { DiaChiNhanHang, DiaChiNhanHangInput } from '@/types/schemas';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { LoadingOverlay } from '@/components/custom/arito';
import { getDataTableColumns } from './cols-definition';
import { ActionBar } from './components/ActionBar';
import { useCRUD, useFormState } from '@/hooks';
import { FormDialog } from './components';
import { QUERY_KEYS } from '@/constants';

export default function DiaChiNhanHangPage() {
  const { addItem, updateItem, deleteItem, isLoading, data } = useCRUD<DiaChiNhanHang, DiaChiNhanHangInput>({
    endpoint: QUERY_KEYS.DIA_CHI_GIAO_NHAN_HANG
  });

  const {
    showForm,
    showDelete,
    selectedObj,
    selectedRowIndex,
    formMode: mode,
    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick,
    handleRowClick,
    clearSelection
  } = useFormState<DiaChiNhanHang>();

  const handleSubmit = async (data: DiaChiNhanHangInput) => {
    if (mode === 'add') {
      await addItem(data);
    } else if (mode === 'edit' && selectedObj) {
      await updateItem(selectedObj.uuid, data);
    }
    handleCloseForm();
    clearSelection();
  };

  const tables = [
    {
      name: '',
      rows: data,
      columns: getDataTableColumns()
    }
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {showForm && (
        <FormDialog
          mode={mode}
          open={showForm}
          onClose={handleCloseForm}
          onAddButtonClick={handleAddClick}
          onEditButtonClick={handleEditClick}
          onCopyButtonClick={handleCopyClick}
          onWatchButtonClick={handleViewClick}
          onDeleteButtonClick={handleDeleteClick}
          onSubmit={handleSubmit}
          initialData={mode === 'add' && !isCopyMode ? undefined : selectedObj}
        />
      )}

      <div className='w-full'>
        <ActionBar
          onAddIconClick={handleAddClick}
          onEditIconClick={handleEditClick}
          onDeleteIconClick={handleDeleteClick}
          onCopyIconClick={handleCopyClick}
          onWatchIconClick={handleViewClick}
        />
        {isLoading && <LoadingOverlay />}
        {!isLoading && (
          <AritoDataTables tables={tables} selectedRowId={selectedRowIndex || undefined} onRowClick={handleRowClick} />
        )}
      </div>

      {showDelete && (
        <ConfirmationDialog
          onClose={handleCloseDelete}
          onConfirm={() => {
            deleteItem(selectedObj!.uuid);
            handleCloseDelete();
            clearSelection();
            handleCloseForm();
          }}
        />
      )}
    </div>
  );
}
