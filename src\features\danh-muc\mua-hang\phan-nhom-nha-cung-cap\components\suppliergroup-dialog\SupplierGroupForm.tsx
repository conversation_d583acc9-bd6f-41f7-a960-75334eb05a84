import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';
const SupplierGroupForm = ({ mode }: { mode: 'add' | 'edit' | 'view' | 'search' }) => {
  const isDisabled = mode === 'view';
  return (
    <div>
      <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
        <div className='p-4 md:p-6'>
          <div className='flex flex-col gap-y-4 md:gap-y-6'>
            <div className='space-y-4 md:space-y-6'>
              <div className='flex flex-col sm:flex-row sm:items-center'>
                <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Mã nhóm</Label>
                <FormField type='text' name='ma_nhom' disabled={isDisabled || mode === 'edit'} />
              </div>

              <div className='flex flex-col sm:flex-row sm:items-center'>
                <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tên nhóm</Label>
                <FormField type='text' name='ten_phan_nhom' disabled={isDisabled} />
              </div>

              <div className='flex flex-col sm:flex-row sm:items-center'>
                <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tên 2</Label>
                <FormField type='text' name='ten2' disabled={isDisabled} />
              </div>

              <div className='flex flex-col sm:flex-row sm:items-center'>
                <Label htmlFor='trang_thai' className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>
                  Trạng thái
                </Label>
                <FormField
                  id='trang_thai'
                  type='select'
                  name='trang_thai'
                  disabled={isDisabled}
                  options={[
                    { label: '1. Còn sử dụng', value: '1' },
                    { label: '0. Không sử dụng', value: '0' }
                  ]}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SupplierGroupForm;
