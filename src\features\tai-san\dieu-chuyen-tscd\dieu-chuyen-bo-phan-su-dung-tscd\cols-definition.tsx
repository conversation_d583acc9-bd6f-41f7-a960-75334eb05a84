import { GridColDef } from '@mui/x-data-grid';

export const getDataTableColumns = (): GridColDef[] => [
  {
    field: 'ma_ts',
    headerName: 'Mã tài sản',
    width: 200
  },
  {
    field: 'ten_ts',
    headerName: 'Tên tài sản',
    width: 350
  },
  { field: 'ky', headerName: 'Kỳ', width: 100 },
  { field: 'nam', headerName: 'Năm', width: 100 },
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 150
  },
  {
    field: 'tk_ts',
    headerName: 'TK tài sản',
    width: 150
  },
  {
    field: 'tk_kh',
    headerName: 'TK khấu hao',
    width: 150
  },
  {
    field: 'tk_cp',
    headerName: 'TK chi phí',
    width: 150
  }
];
