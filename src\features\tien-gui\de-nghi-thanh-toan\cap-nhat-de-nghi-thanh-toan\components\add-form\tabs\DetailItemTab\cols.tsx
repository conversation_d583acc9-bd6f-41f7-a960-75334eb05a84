import { GridColDef } from '@mui/x-data-grid';
import {
  customerSearchColumns,
  boPhanSearchColumns,
  vuViecSearchColumns,
  hopDongSearchColumns,
  paymentInstallmentSearchColumns,
  kheUocSearchColumns,
  phiSearchColumns,
  vatTuSearchColumns,
  chiPhiSearchColumns
} from '@/constants/search-columns';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { ChiTieuNganSach } from '@/types/schemas/chi-tieu-ngan-sach.type';
import { QUERY_KEYS } from '@/constants/query-keys';

// Budget search columns for ngân sách
const nganSachSearchColumns = [
  { field: 'ma_ns', headerName: 'Mã ngân sách', width: 150 },
  { field: 'ten_ns', headerName: 'Tên ngân sách', width: 250 }
];

// Invoice type options for dropdown
const invoiceTypeOptions = [
  { value: '0', label: '0. Không có hóa đơn' },
  { value: '1', label: '1. Hóa đơn GTGT đã tách thuế' },
  { value: '2', label: '2. Hóa đơn GTGT không tách thuế' },
  { value: '3', label: '3. Hóa đơn bán hàng thông thường' }
];

export const getDetailItemColumns = (
  handleCellValueChange: (uuid: string, field: string, value: any) => void,
  budgetCriteriaData: ChiTieuNganSach[] = [],
  hasBudget: boolean = true
): GridColDef[] => {
  // Convert budget criteria data to dropdown options
  const budgetCriteriaOptions = budgetCriteriaData.map(item => ({
    value: item.ma_ctns,
    label: item.ten_ctns
  }));

  const columns: GridColDef[] = [];

  // Conditionally add budget columns only if hasBudget is true
  if (hasBudget) {
    columns.push(
      {
        field: 'ns_kd',
        headerName: 'Chỉ tiêu ngân sách',
        width: 150,
        renderCell: params => (
          <CellField
            name='ns_kd'
            type='select'
            value={params.row.ns_kd || ''}
            options={budgetCriteriaOptions}
            onValueChange={(newValue: any) => handleCellValueChange(params.row.uuid, 'ns_kd', newValue)}
          />
        )
      },
      // 2. Ngân sách - Search field
      {
        field: 'ma_ns',
        headerName: 'Ngân sách',
        width: 120
      }
    );
  }

  // Add the remaining columns
  columns.push(
    // 3. Mã đối tượng - Search field
    {
      field: 'ma_kh',
      headerName: 'Mã đối tượng',
      width: 120,
      renderCell: params => (
        <SearchField<any>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}`}
          searchColumns={customerSearchColumns}
          dialogTitle='Danh mục đối tượng'
          columnDisplay='customer_code'
          value={params.row.ma_kh_data?.customer_code || params.row.ma_kh || ''}
          onRowSelection={row => {
            handleCellValueChange(params.row.uuid, 'ma_kh_data', row);
            handleCellValueChange(params.row.uuid, 'ma_kh', row.customer_code);
            handleCellValueChange(params.row.uuid, 'ten_kh', row.customer_name);
          }}
        />
      )
    },
    // 4. Tên đối tượng - View-only field (auto-populated)
    {
      field: 'ten_kh',
      headerName: 'Tên đối tượng',
      width: 200,
      renderCell: params => (
        <CellField
          name='ten_kh'
          type='text'
          value={params.row.ma_kh_data?.customer_name || params.row.ten_kh || ''}
          disabled={true}
        />
      )
    },
    // 5. Tiền VND - Text input field
    {
      field: 'tien',
      headerName: 'Tiền VND',
      width: 130,
      renderCell: params => (
        <CellField
          name='tien'
          type='number'
          value={params.row.tien || 0}
          onValueChange={(newValue: any) => handleCellValueChange(params.row.uuid, 'tien', newValue)}
        />
      )
    },
    // 6. Diễn giải - Text input field
    {
      field: 'dien_giai',
      headerName: 'Diễn giải',
      width: 200,
      renderCell: params => (
        <CellField
          name='dien_giai'
          type='text'
          value={params.row.dien_giai || ''}
          onValueChange={(newValue: any) => handleCellValueChange(params.row.uuid, 'dien_giai', newValue)}
        />
      )
    },
    // 7. Loại hóa đơn - Select dropdown
    {
      field: 'ma_loai_hd',
      headerName: 'Loại hóa đơn',
      width: 200,
      renderCell: params => (
        <CellField
          name='ma_loai_hd'
          type='select'
          value={params.row.ma_loai_hd || ''}
          options={invoiceTypeOptions}
          onValueChange={(newValue: any) => handleCellValueChange(params.row.uuid, 'ma_loai_hd', newValue)}
        />
      )
    },
    // 8. Bộ phận - Search field
    {
      field: 'ma_bp',
      headerName: 'Bộ phận',
      width: 120,
      renderCell: params => (
        <SearchField<any>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.BO_PHAN}`}
          searchColumns={boPhanSearchColumns}
          dialogTitle='Danh mục bộ phận'
          columnDisplay='ma_bp'
          value={params.row.ma_bp_data?.ma_bp || params.row.ma_bp || ''}
          onRowSelection={row => {
            handleCellValueChange(params.row.uuid, 'ma_bp_data', row);
            handleCellValueChange(params.row.uuid, 'ma_bp', row.ma_bp);
          }}
        />
      )
    },
    // 9. Vụ việc - Search field
    {
      field: 'ma_vv',
      headerName: 'Vụ việc',
      width: 120,
      renderCell: params => (
        <SearchField<any>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.VU_VIEC}`}
          searchColumns={vuViecSearchColumns}
          dialogTitle='Danh mục vụ việc'
          columnDisplay='ma_vv'
          value={params.row.ma_vv_data?.ma_vv || params.row.ma_vv || ''}
          onRowSelection={row => {
            handleCellValueChange(params.row.uuid, 'ma_vv_data', row);
            handleCellValueChange(params.row.uuid, 'ma_vv', row.ma_vv);
          }}
        />
      )
    },
    // 10. Hợp đồng - Search field
    {
      field: 'ma_hd',
      headerName: 'Hợp đồng',
      width: 120,
      renderCell: params => (
        <SearchField<any>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.HOP_DONG}`}
          searchColumns={hopDongSearchColumns}
          dialogTitle='Danh mục hợp đồng'
          columnDisplay='ma_hd'
          value={params.row.ma_hd_data?.ma_hd || params.row.ma_hd || ''}
          onRowSelection={row => {
            handleCellValueChange(params.row.uuid, 'ma_hd_data', row);
            handleCellValueChange(params.row.uuid, 'ma_hd', row.ma_hd);
          }}
        />
      )
    },
    // 11. Đợt thanh toán - Search field
    {
      field: 'ma_dtt',
      headerName: 'Đợt thanh toán',
      width: 130,
      renderCell: params => (
        <SearchField<any>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}`}
          searchColumns={paymentInstallmentSearchColumns}
          dialogTitle='Danh mục đợt thanh toán'
          columnDisplay='ma_dtt'
          value={params.row.ma_dtt_data?.ma_dtt || params.row.ma_dtt || ''}
          onRowSelection={row => {
            handleCellValueChange(params.row.uuid, 'ma_dtt_data', row);
            handleCellValueChange(params.row.uuid, 'ma_dtt', row.ma_dtt);
          }}
        />
      )
    },
    // 12. Khế ước - Search field
    {
      field: 'ma_ku',
      headerName: 'Khế ước',
      width: 120,
      renderCell: params => (
        <SearchField<any>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.KHE_UOC}`}
          searchColumns={kheUocSearchColumns}
          dialogTitle='Danh mục khế ước'
          columnDisplay='ma_ku'
          value={params.row.ma_ku_data?.ma_ku || params.row.ma_ku || ''}
          onRowSelection={row => {
            handleCellValueChange(params.row.uuid, 'ma_ku_data', row);
            handleCellValueChange(params.row.uuid, 'ma_ku', row.ma_ku);
          }}
        />
      )
    },
    // 13. Phí - Search field
    {
      field: 'ma_phi',
      headerName: 'Phí',
      width: 100,
      renderCell: params => (
        <SearchField<any>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.PHI}`}
          searchColumns={phiSearchColumns}
          dialogTitle='Danh mục phí'
          columnDisplay='ma_phi'
          value={params.row.ma_phi_data?.ma_phi || params.row.ma_phi || ''}
          onRowSelection={row => {
            handleCellValueChange(params.row.uuid, 'ma_phi_data', row);
            handleCellValueChange(params.row.uuid, 'ma_phi', row.ma_phi);
          }}
        />
      )
    },
    // 14. Sản phẩm - Search field
    {
      field: 'ma_sp',
      headerName: 'Sản phẩm',
      width: 120,
      renderCell: params => (
        <SearchField<any>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.VAT_TU}`}
          searchColumns={vatTuSearchColumns}
          dialogTitle='Danh mục sản phẩm'
          columnDisplay='ma_vt'
          value={params.row.ma_sp_data?.ma_vt || params.row.ma_sp || ''}
          onRowSelection={row => {
            handleCellValueChange(params.row.uuid, 'ma_sp_data', row);
            handleCellValueChange(params.row.uuid, 'ma_sp', row.ma_vt);
          }}
        />
      )
    },
    // 15. Chi phí - Search field (using invalid expense search columns)
    {
      field: 'ma_cp0',
      headerName: 'C/p không h/lệ',
      width: 130,
      renderCell: params => (
        <SearchField<any>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.CHI_PHI}`}
          searchColumns={chiPhiSearchColumns}
          dialogTitle='Danh mục chi phí'
          columnDisplay='ma_cp'
          value={params.row.ma_cp0_data?.ma_cp || params.row.ma_cp0 || ''}
          onRowSelection={row => {
            handleCellValueChange(params.row.uuid, 'ma_cp0_data', row);
            handleCellValueChange(params.row.uuid, 'ma_cp0', row.ma_cp);
          }}
        />
      )
    }
  );

  return columns;
};
