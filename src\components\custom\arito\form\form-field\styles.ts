import { SxProps, Theme } from '@mui/material';

// Form field label styles
export const formFieldLabelStyle = (labelWidth: string): SxProps<Theme> => ({
  width: { xs: '100%', sm: labelWidth },
  marginBottom: { xs: '4px', sm: 0 },
  fontWeight: 500,
  fontSize: '14px',
  color: '#374151',
  paddingRight: '8px',
  textAlign: { xs: 'left', sm: 'right' }
});

// Form field input container styles
export const formFieldInputContainerStyle = (inputWidth: string): SxProps<Theme> => ({
  width: { xs: '100%', sm: inputWidth },
  display: 'flex',
  flexDirection: { xs: 'column', sm: 'row' },
  alignItems: { xs: 'flex-start', sm: 'center' }
});

// Search icon button styles
export const searchIconButtonStyle: SxProps<Theme> = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  color: 'text.secondary',
  cursor: 'pointer',
  position: 'absolute',
  right: 4,
  padding: '4px',
  '&:hover': {
    color: '#2563EB'
  }
};

// Related field value typography styles
export const relatedFieldValueStyle: SxProps<Theme> = {
  marginLeft: { xs: 0, sm: '12px' },
  marginTop: { xs: '4px', sm: 0 },
  fontSize: '14px',
  color: '#374151',
  maxWidth: { xs: '100%', sm: '65%' },
  flexGrow: 1,
  whiteSpace: 'nowrap',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  textAlign: { xs: 'left', sm: 'left' },
  width: '100%'
};

// Search dialog title styles
export const searchDialogTitleStyle: SxProps<Theme> = {
  display: 'flex',
  alignItems: 'center',
  padding: '8px 16px',
  borderBottom: '1px solid #e5e7eb'
};

// Search dialog content styles
export const searchDialogContentStyle: SxProps<Theme> = {
  padding: 0,
  display: 'flex',
  flexDirection: 'column',
  height: 'calc(80vh - 64px - 64px)'
};

// Search input container styles
export const searchInputContainerStyle: SxProps<Theme> = {
  display: 'flex',
  alignItems: 'center',
  padding: '8px 16px',
  borderBottom: '1px solid #e5e7eb'
};

// Search results container styles
export const searchResultsContainerStyle: SxProps<Theme> = {
  flexGrow: 1,
  overflowY: 'auto',
  height: '100%',
  maxHeight: 'calc(100% - 40px)',
  '& .MuiDataGrid-root': {
    border: 'none',
    height: '100% !important'
  },
  position: 'relative'
};

// Search dialog actions styles
export const searchDialogActionsStyle: SxProps<Theme> = {
  padding: '8px 16px',
  borderTop: '1px solid #e5e7eb',
  justifyContent: 'space-between'
};

// Confirm button styles
export const confirmButtonStyle: SxProps<Theme> = {
  backgroundColor: '#2563eb',
  color: 'white',
  '&:hover': {
    backgroundColor: '#1d4ed8'
  }
};

// Cancel button styles
export const cancelButtonStyle: SxProps<Theme> = {
  color: '#6b7280',
  '&:hover': {
    backgroundColor: '#f3f4f6'
  }
};

// Fullscreen toggle button styles
export const fullscreenToggleButtonStyle: SxProps<Theme> = {
  marginLeft: 'auto',
  padding: '4px'
};

// Date picker styles
export const datePickerStyle: SxProps<Theme> = {
  width: '100%',
  '& .MuiInputBase-root': {
    height: '32px'
  },
  '& .MuiInput-root': {
    fontSize: '14px',
    height: '32px',
    display: 'flex',
    alignItems: 'center',
    '&:before': {
      borderBottom: '1px solid #e5e7eb'
    },
    '&:hover:not(.Mui-disabled):before': {
      borderBottom: '1px solid #2563EB'
    },
    '&.Mui-focused:after': {
      borderBottom: '1px solid #2563EB'
    }
  },
  '& .MuiInput-input': {
    padding: '4px 8px',
    height: '24px'
  },
  '& .MuiInputAdornment-root': {
    marginRight: '0'
  },
  '& .MuiIconButton-root': {
    padding: '4px'
  },
  '& .MuiSvgIcon-root': {
    color: '#2563EB',
    marginRight: '8px',
    fontSize: '18px'
  },
  position: 'relative',
  marginTop: '0px'
};

// Select field styles
export const selectFieldStyle: SxProps<Theme> = {
  '& .MuiOutlinedInput-notchedOutline': {
    border: 'none'
  },
  '&.MuiOutlinedInput-root': {
    borderBottom: '1px solid #e5e7eb',
    borderRadius: 0,
    height: '32px',
    '&:hover': {
      borderBottom: '1px solid #2563EB'
    },
    '&.Mui-focused': {
      borderBottom: '1px solid #2563EB'
    }
  },
  '& .MuiSelect-select': {
    padding: '4px 8px',
    fontSize: '14px',
    height: '24px',
    display: 'flex',
    alignItems: 'center'
  }
};

// Select menu props styles
export const selectMenuPaperStyle: SxProps<Theme> = {
  borderRadius: '4px',
  marginTop: '4px',
  maxHeight: '300px',
  overflowY: 'auto'
};

// Search field container styles
export const searchFieldContainerStyle: SxProps<Theme> = {
  position: 'relative',
  flexShrink: 0,
  width: '100%',
  maxWidth: {
    xs: '100%',
    sm: '33%'
  }
};

// Table container styles
export const tableContainerStyle: SxProps<Theme> = {
  width: '100%',
  gridColumn: '1 / -1'
};

// Number input styles
export const numberInputStyle = (type: string): SxProps<Theme> => ({
  '& .MuiInput-root': {
    fontSize: '14px',
    height: '32px',
    display: 'flex',
    alignItems: 'center',
    '&:before': {
      borderBottom: '1px solid #e5e7eb'
    },
    '&:hover:not(.Mui-disabled):before': {
      borderBottom: '1px solid #2563EB'
    },
    '&.Mui-focused:after': {
      borderBottom: '1px solid #2563EB'
    }
  },
  '& .MuiInput-input': {
    padding: '4px 8px',
    height: '24px'
  },
  marginTop: type === 'number' ? '8px' : '0px'
});

// Search input styles
export const searchInputFieldStyle: SxProps<Theme> = {
  width: '300px',
  ml: 1,
  '& .MuiOutlinedInput-input': {
    padding: '4px 8px 4px 12px',
    fontSize: '0.8rem',
    height: '22px'
  },
  '& .MuiOutlinedInput-root': {
    borderRadius: '0px'
  }
};

// Checkbox styles
export const checkboxStyle = (_disabled: boolean): SxProps<Theme> => ({
  color: '#d1d5db'
});

// Text field input styles
export const textFieldInputStyle = (type: string): SxProps<Theme> => ({
  '& .MuiInput-root': {
    fontSize: '14px',
    height: '32px',
    display: 'flex',
    alignItems: 'center',
    '&:before': {
      borderBottom: '1px solid #e5e7eb'
    },
    '&:hover:not(.Mui-disabled):before': {
      borderBottom: '1px solid #2563EB'
    },
    '&.Mui-focused:after': {
      borderBottom: '1px solid #2563EB'
    }
  },
  '& .MuiInput-input': {
    padding: '4px 8px',
    paddingRight: '28px', // Add space for the search icon
    textAlign: { xs: 'left', sm: 'left' }, // Left align text on mobile
    height: '24px'
  },
  marginTop: type === 'number' ? '8px' : '0px'
});

// Dialog paper styles
export const dialogPaperStyle = (isMobile: boolean, isFullScreen: boolean): SxProps<Theme> => ({
  borderRadius: '0px',
  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  overflow: 'hidden',
  ...(isMobile || isFullScreen
    ? {
        height: '100%',
        width: '100%',
        maxWidth: '100vw',
        maxHeight: '100vh',
        margin: 0
      }
    : {
        height: '450px',
        width: '600px',
        maxWidth: '90vw',
        maxHeight: '90vh'
      })
});
