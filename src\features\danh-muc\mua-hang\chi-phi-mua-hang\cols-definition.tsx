import { GridColDef } from '@mui/x-data-grid';
import { ChiPhiMuaHang } from '@/types/schemas';

export const exportMainColumns = (
  handleOpenViewForm?: (obj: ChiPhiMuaHang) => void,
  handleOpenEditForm?: (obj: ChiPhiMuaHang) => void
): GridColDef[] => [
  {
    field: 'ma_cp',
    headerName: 'Mã chi phí',
    width: 150,
    editable: false
  },
  {
    field: 'ten_cp',
    headerName: 'Tên chi phí',
    width: 250,
    editable: false,
    flex: 1
  },
  {
    field: 'loai_cp',
    headerName: 'Loại chi phí',
    width: 180,
    editable: false
  },
  {
    field: 'loai_pb',
    headerName: 'Tiêu thức phân bổ',
    width: 180,
    editable: false
  },
  {
    field: 'ma_ct',
    headerName: 'Chứng từ',
    width: 150,
    renderCell: params => params.row.ma_ct_data?.ma_ct || ''
  },
  {
    field: 'status',
    headerName: 'Trạng thái',
    width: 130,
    editable: false,
    renderCell: params => {
      return params.value === '1' ? '1. <PERSON><PERSON> sử dụng' : '0. <PERSON>h<PERSON>ng s<PERSON> dụng';
    }
  }
];
