import { useFormFieldState, useSearchFieldStates } from '../../hooks';
import { useInputTableRows } from '@/hooks/use-input-table-rows';
import { ChiTietHoaDonBanHangDichVu } from '@/types/schemas';
import { AritoForm } from '@/components/custom/arito';
import { formSchema, FormValues } from '../../schema';
import { useAuth } from '@/contexts/auth-context';
import { transformFormData } from '../../utils';
import BasicInfoTab from './BasicInfoTab';
import EInvoiceTab from './EInvoiceTab';
import { FormMode } from '@/types/form';
import { BottomBar } from './BottomBar';
import DetailTab from './DetailTab';
import OtherTab from './OtherTab';

interface FormDialogProps {
  formMode: FormMode;
  detailRows: ChiTietHoaDonBanHangDichVu[];
  setDetailRows: (rows: ChiTietHoaDonBanHangDichVu[]) => void;
  totalAmount: number;
  totalDiscount: number;
  totalTax: number;
  onClose: () => void;
  onSubmit: (data: any) => Promise<void>;
}

export default function FormDialog({
  formMode,
  detailRows,
  setDetailRows,
  totalAmount,
  totalDiscount,
  totalTax,
  onClose,
  onSubmit
}: FormDialogProps) {
  const { entityUnit } = useAuth();
  const { state, actions } = useFormFieldState();

  const {
    rows,
    selectedRowUuid,
    handleRowClick,
    handleAddRow,
    handleDeleteRow,
    handleCopyRow,
    handlePasteRow,
    handleMoveRow,
    handleCellValueChange
  } = useInputTableRows<ChiTietHoaDonBanHangDichVu>(detailRows, setDetailRows);

  const handleSubmit = (data: any) => {
    const formattedData = transformFormData(data, state, rows, totalAmount, totalDiscount, totalTax, entityUnit);
    console.log('formattedData:', formattedData);
    onSubmit(formattedData);
  };

  return (
    <AritoForm
      mode={formMode}
      title={formMode === 'add' ? 'Mới' : undefined}
      subTitle='Hóa đơn bán dịch vụ'
      schema={formSchema}
      onSubmit={handleSubmit}
      onClose={onClose}
      headerFields={<BasicInfoTab formMode={formMode} formState={{ state, actions }} />}
      tabs={[
        {
          id: 'details',
          label: 'Chi tiết',
          component: (
            <DetailTab
              rows={rows}
              selectedRowUuid={selectedRowUuid}
              handleRowClick={handleRowClick}
              handleAddRow={handleAddRow}
              handleDeleteRow={handleDeleteRow}
              handleCopyRow={handleCopyRow}
              handlePasteRow={handlePasteRow}
              handleMoveRow={handleMoveRow}
              handleCellValueChange={handleCellValueChange}
              formMode={formMode}
            />
          )
        },
        {
          id: 'other',
          label: 'Khác',
          component: <OtherTab formMode={formMode} formState={{ state, actions }} />
        },
        {
          id: 'e-invoice',
          label: 'HĐĐT(Không sử dụng)',
          component: <EInvoiceTab formMode={formMode} />
        }
      ]}
      bottomBar={<BottomBar totalAmount={totalAmount} totalDiscount={totalDiscount} totalTax={totalTax} />}
    />
  );
}
