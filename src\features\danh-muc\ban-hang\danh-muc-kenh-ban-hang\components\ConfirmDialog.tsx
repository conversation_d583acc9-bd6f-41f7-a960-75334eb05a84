import { Button } from '@mui/material';
import { AritoDialog, AritoIcon } from '@/components/custom/arito';

interface ConfirmDialogProps {
  open: boolean;
  onClose: () => void;
  onCloseConfirmDialog: () => void;
}

function ConfirmDialog({ open, onClose, onCloseConfirmDialog }: ConfirmDialogProps) {
  return (
    <AritoDialog
      open={open}
      onClose={onCloseConfirmDialog}
      title='Xác nhận'
      maxWidth='sm'
      titleIcon={<AritoIcon icon={260} />}
      actions={
        <div className='space-x-2 p-2'>
          <Button
            className='bg-[rgba(15,118,110,0.9)] text-white hover:bg-[rgba(15,118,110,1)]'
            onClick={onClose}
            variant='contained'
          >
            <AritoIcon icon={884} marginX='4px' />
            <PERSON><PERSON><PERSON> ý
          </Button>
          <Button onClick={onCloseConfirmDialog} variant='outlined'>
            <AritoIcon icon={885} marginX='4px' />
            Huỷ
          </Button>
        </div>
      }
    >
      <div className='w-96 p-4'>
        <p>Bạn muốn kết thúc?</p>
      </div>
    </AritoDialog>
  );
}

export default ConfirmDialog;
