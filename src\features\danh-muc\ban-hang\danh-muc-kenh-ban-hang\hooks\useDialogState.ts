import { useState } from 'react';
import { KenhBanHang } from '@/types/schemas/kenh-ban-hang.type';
import { FormValues, initialValues } from '../schemas';
import { useFormState } from '@/hooks/use-form-state';

export const useDialogState = (selectedObj: KenhBanHang | null, clearSelection?: () => void) => {
  const [formData, setFormData] = useState<FormValues>(initialValues);

  // Use global useFormState hook instead of custom implementation
  const {
    showForm: open,
    showDelete: showDeleteDialog,
    formMode: mode,
    isCopyMode,
    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick
  } = useFormState<KenhBanHang>();

  // Map API data to form values
  const mapApiToFormData = (apiData: KenhBanHang): FormValues => {
    return {
      uuid: apiData.uuid,
      ma_kenh_ban_hang: apiData.ma_kbh || '',
      ma_nguon_don: apiData.ten_nguondon || '',
      ma_cua_hang: apiData.ma_cuahang || '',
      hinh_thuc_thanh_toan: apiData.ma_httt_data?.ma_httt || '',
      phuong_thuc_thanh_toan: apiData.ma_pttt_data?.ma_pttt || '',
      ti_le_hoa_hong: apiData.tl_hoahong || 0,
      loai: apiData.loai_kenh?.toString() || '1',
      trang_thai: apiData.status || 1,
      ma_nguondon: apiData.ma_nguondon || '',
      ma_httt: apiData.ma_httt || '',
      ma_pttt: apiData.ma_pttt || ''
    };
  };

  // Enhanced handlers with form data management
  const handleAddButtonClick = () => {
    setFormData(initialValues);
    handleAddClick();
  };

  const handleEditButtonClick = () => {
    if (selectedObj) {
      setFormData(mapApiToFormData(selectedObj));
      handleEditClick();
    }
  };

  const handleWatchButtonClick = () => {
    if (selectedObj) {
      setFormData(mapApiToFormData(selectedObj));
      handleViewClick();
    }
  };

  const handleCopyButtonClick = () => {
    if (selectedObj) {
      // When copying, create new record with similar data but no UUID
      const copyData = mapApiToFormData(selectedObj);
      copyData.uuid = undefined; // Remove UUID for new record
      copyData.ma_kenh_ban_hang = ''; // Clear main identifier field for copy

      setFormData(copyData);
      handleCopyClick();
    }
  };

  const handleDeleteButtonClick = () => {
    handleDeleteClick();
  };

  const handleCloseDialog = () => {
    handleCloseForm();
    if (clearSelection) {
      clearSelection();
    }
  };

  const handleCloseDeleteDialog = () => {
    handleCloseDelete();
  };

  return {
    mode,
    formData,
    isCopyMode,
    handleAddButtonClick,
    handleEditButtonClick,
    handleWatchButtonClick,
    handleCopyButtonClick,
    handleCloseDialog,
    handleDeleteButtonClick,
    handleCloseDeleteDialog,
    open,
    showDeleteDialog,
    clearSelection
  };
};
