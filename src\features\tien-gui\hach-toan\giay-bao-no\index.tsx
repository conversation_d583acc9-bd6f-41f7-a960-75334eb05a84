'use client';

import { useState, useEffect } from 'react';
import Split from 'react-split';
import { InputTable, AritoDataTables, DeleteDialog, LoadingOverlay } from '@/components/custom/arito';
import { useFormState, useRows, useHoaDonBanHang, useToast, useGiayBaoNo } from '@/hooks';
import { SearchDialog, ActionBar, FormDialog, InputTableAction } from './components';
import { getDataTableColumns, getInputTableColumns } from './cols-definition';

export default function GiayBaoNoPage() {
  const [showSearchDialog, setShowSearchDialog] = useState(false);
  const [detail, setDetail] = useState<any[]>([]);
  const { toast } = useToast();
  const {
    giayBaoNos,
    isLoading,
    addGiayBaoNo,
    updateGiayBaoNo,
    deleteGiayBaoNo,
    refreshGiayBaoNos,
    getGiayBaoNoDetail
  } = useGiayBaoNo();
  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRows();
  const {
    showForm,
    showDelete,
    formMode,
    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick
  } = useFormState();

  const fetchDetail = async () => {
    setDetail([]);

    if (selectedObj?.uuid) {
      try {
        const detailData = await getGiayBaoNoDetail(selectedObj.uuid);
        setDetail((detailData as any).results);
      } catch (error) {
        setDetail([]);
      }
    } else {
      setDetail([]);
    }
  };

  useEffect(() => {
    fetchDetail();
  }, [selectedObj?.uuid]);

  const handleSearch = () => {
    setShowSearchDialog(true);
  };

  const handleSearchClose = () => {
    setShowSearchDialog(false);
  };

  const handleSearchSubmit = () => {};

  const handleSubmit = async (data: any) => {
    if (formMode === 'add') {
      await addGiayBaoNo(data);
    } else if (formMode === 'edit' && selectedObj) {
      await updateGiayBaoNo(selectedObj.uuid, data);
    }
    handleCloseForm();
    clearSelection();
  };

  const tables = [
    {
      name: 'Tất cả',
      rows: giayBaoNos,
      columns: getDataTableColumns(handleViewClick)
    },
    {
      name: 'Lập chứng từ',
      rows: giayBaoNos.filter(row => row.status === '0'),
      columns: getDataTableColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-[#EAD1DC]' />
    },
    {
      name: 'Chờ duyệt',
      rows: giayBaoNos.filter(row => row.status === '3'),
      columns: getDataTableColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-red-500' />
    },
    {
      name: 'Xóa hóa đơn',
      rows: giayBaoNos.filter(row => row.status === '5'),
      columns: getDataTableColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-[#3D85C6]' />
    },
    {
      name: 'Khác',
      rows: giayBaoNos.filter(row => row.status === '99'),
      columns: getDataTableColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-black' />
    }
  ];

  return (
    <div className='flex h-full min-h-[calc(100vh-120px)] w-full flex-col overflow-hidden'>
      {showSearchDialog && (
        <SearchDialog open={showSearchDialog} onClose={handleSearchClose} onSearch={handleSearchSubmit} />
      )}

      {showForm && (
        <FormDialog
          formMode={formMode}
          open={showForm}
          onClose={handleCloseForm}
          initialData={formMode == 'add' && !isCopyMode ? undefined : selectedObj}
          onSubmit={handleSubmit}
          onAdd={handleAddClick}
          onEdit={handleEditClick}
          onDelete={handleDeleteClick}
          onCopy={handleCopyClick}
        />
      )}

      {showDelete && (
        <DeleteDialog
          open={showDelete}
          onClose={handleCloseDelete}
          selectedObj={selectedObj}
          deleteObj={deleteGiayBaoNo}
          clearSelection={clearSelection}
        />
      )}

      {!showForm && (
        <>
          <ActionBar
            onAdd={handleAddClick}
            onEdit={handleEditClick}
            onDelete={handleDeleteClick}
            onCopy={handleCopyClick}
            onSearch={handleSearch}
            onRefresh={async () => {
              await refreshGiayBaoNos();
              await fetchDetail();
            }}
          />

          {isLoading && (
            <div className='flex h-full items-center justify-center'>
              <LoadingOverlay />
            </div>
          )}

          {!isLoading && (
            <Split
              className='flex flex-1 flex-col overflow-hidden'
              direction='vertical'
              sizes={[50, 50]}
              minSize={200}
              gutterSize={4}
              gutterAlign='center'
              snapOffset={30}
              dragInterval={1}
              cursor='row-resize'
            >
              <div className='w-full overflow-hidden'>
                <AritoDataTables
                  tables={tables}
                  onRowClick={handleRowClick}
                  selectedRowId={selectedRowIndex || undefined}
                />
              </div>

              <div className='min-h-[300px] overflow-hidden'>
                <InputTable
                  rows={detail || []}
                  columns={getInputTableColumns()}
                  mode={formMode}
                  actionButtons={
                    <InputTableAction
                      formMode={formMode}
                      handleExport={() => console.log('Export clicked')}
                      handlePin={() => console.log('Pin clicked')}
                    />
                  }
                />
              </div>
            </Split>
          )}
        </>
      )}
    </div>
  );
}
