import { useFormContext } from 'react-hook-form';
import { useEffect, useState } from 'react';
import { accountSearchColumns, MA_CHUNG_TU, QUERY_KEYS, quyenChungTuSearchColumns } from '@/constants';
import { SearchField, FormField, AritoHeaderTabs, TabItem } from '@/components/custom/arito';
import { useNgoaiTe, useQuyenChungTuByChungTu } from '@/hooks';
import { AccountModel, QuyenChungTu } from '@/types/schemas';
import { generateSoChungTuHienTai } from '@/lib/stringUtil';
import { InfoTabFormFieldStates } from '../../../hooks';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface BasicInfoProps {
  formMode: FormMode;
  formFieldStates: InfoTabFormFieldStates;
}

export default function BasicInfo({ formMode, formFieldStates }: BasicInfoProps) {
  const isViewMode = formMode === 'view';
  const [soChungTuHienTai, setSoChungTuHienTai] = useState<string>('');
  const { currencies } = useNgoaiTe();
  const { watch } = useFormContext();
  const { taiKhoan, setTaiKhoan, quyenChungTu, setQuyenChungTu } = formFieldStates;

  const ngay_ct = watch('ngay_ct');

  const { quyenChungTus } = useQuyenChungTuByChungTu({
    ma_ct: MA_CHUNG_TU.BAN_HANG.BUT_TOAN_DIEU_CHINH_GIAM_CONG_NO,
    ngay_hl: ngay_ct || new Date().toISOString().split('T')[0]
  });

  useEffect(() => {
    if (quyenChungTu) {
      setSoChungTuHienTai(generateSoChungTuHienTai(quyenChungTu.i_so_ct_ht, quyenChungTu.so_ct_mau, new Date()));
    }
  }, [quyenChungTu]);

  const InforTab = (
    <div className='grid grid-cols-[3fr_1fr] items-start space-y-2 p-4'>
      <div>
        <div className='flex items-center'>
          <Label className='w-40'>Loại phiếu thu</Label>
          <FormField
            type='select'
            className='w-[200px]'
            name='ma_ngv'
            options={[
              { value: '1', label: '1. Theo hóa đơn' },
              { value: '2', label: '2. Theo khách hàng' }
            ]}
            disabled={isViewMode}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40'>Diễn giải</Label>
          <div className='w-full'>
            <FormField name='dien_giai' disabled={isViewMode} className='w-full' />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40'>Tài khoản nợ</Label>
          <SearchField<AccountModel>
            type='text'
            displayRelatedField='name'
            columnDisplay='code'
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
            searchColumns={accountSearchColumns}
            dialogTitle='Danh mục tài khoản'
            disabled={isViewMode}
            value={taiKhoan?.code || ''}
            relatedFieldValue={taiKhoan?.name || ''}
            onRowSelection={setTaiKhoan}
          />
        </div>
      </div>

      <div className='flex flex-col items-end'>
        <div className='flex items-center'>
          <Label className='w-32'>Số chứng từ</Label>
          <SearchField<QuyenChungTu>
            type='text'
            columnDisplay='ma_nk'
            className='w-[11.25rem]'
            searchEndpoint={`/${QUERY_KEYS.QUYEN_CHUNG_TU}`}
            searchColumns={quyenChungTuSearchColumns}
            dialogTitle='Danh mục quyển chứng từ'
            disabled={isViewMode}
            value={soChungTuHienTai}
            onValueChange={e => setSoChungTuHienTai(e.target.value)}
            rows={quyenChungTus}
            onRowSelection={setQuyenChungTu}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-32'>Ngày chứng từ</Label>
          <FormField name='ngay_ct' type='date' disabled={isViewMode} className='w-[11.25rem]' />
        </div>

        <div className='flex items-center'>
          <Label className='w-32'>Ngày lập chứng từ</Label>
          <FormField name='ngay_lct' type='date' disabled={isViewMode} className='w-[11.25rem]' />
        </div>

        <div className='flex items-center'>
          <Label className='w-32'>Ngoại tệ</Label>
          <FormField
            name='ma_nt'
            type='select'
            disabled={isViewMode}
            options={currencies.map(currency => ({ value: currency.uuid, label: currency.ma_nt }))}
            className='mt-1 w-20'
          />
          <FormField name='ty_gia' type='number' disabled={isViewMode} className='ml-1 w-24' />
        </div>

        <div className='flex items-center'>
          <Label className='w-32'>Trạng thái</Label>
          <FormField
            name='status'
            type='select'
            disabled={isViewMode}
            options={[
              { value: '0', label: 'Chưa ghi sổ' },
              { value: '3', label: 'Chờ duyệt' },
              { value: '5', label: 'Đã ghi sổ' }
            ]}
            defaultValue='1'
            className='w-[11.25rem]'
          />
        </div>

        <div className='mt-1 flex items-center'>
          <FormField
            name='transfer_yn'
            type='checkbox'
            label='Dữ liệu được nhận'
            disabled={isViewMode}
            defaultValue={false}
          />
          <Label className='w-9'></Label>
        </div>
      </div>
    </div>
  );

  const tabs: TabItem[] = [
    {
      id: 'info',
      label: 'Thông tin',
      component: InforTab
    }
  ];

  return <AritoHeaderTabs tabs={tabs} />;
}
