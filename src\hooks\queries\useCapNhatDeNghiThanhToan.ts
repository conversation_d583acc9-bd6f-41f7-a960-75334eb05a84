import { useState, useEffect, useCallback } from 'react';
import {
  CapNhatDeNghiThanhToan,
  CapNhatDeNghiThanhToanInput,
  ChiTietCapNhatDeNghiThanhToan,
  ChiTietCapNhatDeNghiThanhToanInput
} from '@/types/schemas';
import { QUERY_KEYS } from '@/constants/query-keys';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/api';

interface UseCapNhatDeNghiThanhToanReturn {
  capNhatDeNghiThanhToans: CapNhatDeNghiThanhToan[];
  isLoading: boolean;
  addCapNhatDeNghiThanhToan: (
    newCapNhatDeNghiThanhToan: CapNhatDeNghiThanhToanInput
  ) => Promise<CapNhatDeNghiThanhToan>;
  updateCapNhatDeNghiThanhToan: (
    uuid: string,
    updatedCapNhatDeNghiThanhToan: Partial<CapNhatDeNghiThanhToanInput>
  ) => Promise<CapNhatDeNghiThanhToan>;
  deleteCapNhatDeNghiThanhToan: (uuid: string) => Promise<void>;
  refreshCapNhatDeNghiThanhToans: () => Promise<void>;
  getCapNhatDeNghiThanhToanById: (uuid: string) => Promise<CapNhatDeNghiThanhToan | null>;

  // Chi tiết methods
  getChiTietByBaoCao: (baoCaoUuid: string) => Promise<ChiTietCapNhatDeNghiThanhToan[]>;
  addChiTiet: (chiTiet: ChiTietCapNhatDeNghiThanhToanInput) => Promise<ChiTietCapNhatDeNghiThanhToan>;
  updateChiTiet: (
    uuid: string,
    chiTiet: Partial<ChiTietCapNhatDeNghiThanhToanInput>
  ) => Promise<ChiTietCapNhatDeNghiThanhToan>;
  deleteChiTiet: (uuid: string) => Promise<void>;
}

export const useCapNhatDeNghiThanhToan = (
  initialCapNhatDeNghiThanhToans: CapNhatDeNghiThanhToan[] = []
): UseCapNhatDeNghiThanhToanReturn => {
  const [capNhatDeNghiThanhToans, setCapNhatDeNghiThanhToans] =
    useState<CapNhatDeNghiThanhToan[]>(initialCapNhatDeNghiThanhToans);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const { entity } = useAuth();

  const fetchCapNhatDeNghiThanhToans = useCallback(async () => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.get(`/entities/${entity.slug}/erp/${QUERY_KEYS.CAP_NHAT_DE_NGHI_THANH_TOAN}/`);
      const mappedData: CapNhatDeNghiThanhToan[] = response.data.results;
      setCapNhatDeNghiThanhToans(mappedData);
      return mappedData;
    } catch (error) {
      console.error('Error fetching payment request updates:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [entity?.slug]);

  const getCapNhatDeNghiThanhToanById = async (uuid: string): Promise<CapNhatDeNghiThanhToan | null> => {
    if (!entity?.slug) return null;

    setIsLoading(true);
    try {
      const response = await api.get(`/entities/${entity.slug}/erp/${QUERY_KEYS.CAP_NHAT_DE_NGHI_THANH_TOAN}/${uuid}/`);
      return response.data as CapNhatDeNghiThanhToan;
    } catch (error) {
      console.error('Error fetching payment request update by ID:', error);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const addCapNhatDeNghiThanhToan = async (
    newCapNhatDeNghiThanhToan: CapNhatDeNghiThanhToanInput
  ): Promise<CapNhatDeNghiThanhToan> => {
    if (!entity?.slug) throw new Error('Entity not available');

    setIsLoading(true);
    try {
      const response = await api.post(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.CAP_NHAT_DE_NGHI_THANH_TOAN}/`,
        newCapNhatDeNghiThanhToan
      );
      const addedCapNhatDeNghiThanhToan: CapNhatDeNghiThanhToan = response.data;
      setCapNhatDeNghiThanhToans(prev => [...prev, addedCapNhatDeNghiThanhToan]);
      return addedCapNhatDeNghiThanhToan;
    } catch (error) {
      console.error('Error adding payment request update:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateCapNhatDeNghiThanhToan = async (
    uuid: string,
    updatedCapNhatDeNghiThanhToan: Partial<CapNhatDeNghiThanhToanInput>
  ): Promise<CapNhatDeNghiThanhToan> => {
    if (!entity?.slug) throw new Error('Entity not available');

    setIsLoading(true);
    try {
      const response = await api.put(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.CAP_NHAT_DE_NGHI_THANH_TOAN}/${uuid}/`,
        updatedCapNhatDeNghiThanhToan
      );
      const updatedData: CapNhatDeNghiThanhToan = response.data;
      setCapNhatDeNghiThanhToans(prev => prev.map(item => (item.uuid === updatedData.uuid ? updatedData : item)));
      return updatedData;
    } catch (error) {
      console.error('Error updating payment request update:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteCapNhatDeNghiThanhToan = async (uuid: string) => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      await api.delete(`/entities/${entity.slug}/erp/${QUERY_KEYS.CAP_NHAT_DE_NGHI_THANH_TOAN}/${uuid}/`);
      setCapNhatDeNghiThanhToans(prev => prev.filter(item => item.uuid !== uuid));
    } catch (error) {
      console.error('Error deleting payment request update:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Chi tiết methods
  const getChiTietByBaoCao = async (baoCaoUuid: string): Promise<ChiTietCapNhatDeNghiThanhToan[]> => {
    if (!entity?.slug) return [];

    // Don't set main loading state for detail fetching
    try {
      const response = await api.get(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.CHI_TIET_CAP_NHAT_DE_NGHI_THANH_TOAN}/?bao_cao=${baoCaoUuid}`
      );
      return response.data.results as ChiTietCapNhatDeNghiThanhToan[];
    } catch (error) {
      console.error('Error fetching payment request update details by report ID:', error);
      return [];
    }
  };

  const addChiTiet = async (chiTiet: ChiTietCapNhatDeNghiThanhToanInput): Promise<ChiTietCapNhatDeNghiThanhToan> => {
    if (!entity?.slug) throw new Error('Entity not available');

    setIsLoading(true);
    try {
      const response = await api.post(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.CHI_TIET_CAP_NHAT_DE_NGHI_THANH_TOAN}/`,
        chiTiet
      );
      return response.data as ChiTietCapNhatDeNghiThanhToan;
    } catch (error) {
      console.error('Error adding payment request update detail:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateChiTiet = async (
    uuid: string,
    chiTiet: Partial<ChiTietCapNhatDeNghiThanhToanInput>
  ): Promise<ChiTietCapNhatDeNghiThanhToan> => {
    if (!entity?.slug) throw new Error('Entity not available');

    setIsLoading(true);
    try {
      const response = await api.patch(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.CHI_TIET_CAP_NHAT_DE_NGHI_THANH_TOAN}/${uuid}/`,
        chiTiet
      );
      return response.data as ChiTietCapNhatDeNghiThanhToan;
    } catch (error) {
      console.error('Error updating payment request update detail:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteChiTiet = async (uuid: string): Promise<void> => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      await api.delete(`/entities/${entity.slug}/erp/${QUERY_KEYS.CHI_TIET_CAP_NHAT_DE_NGHI_THANH_TOAN}/${uuid}/`);
    } catch (error) {
      console.error('Error deleting payment request update detail:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchCapNhatDeNghiThanhToans();
  }, [entity?.slug, fetchCapNhatDeNghiThanhToans]);

  // Wrapper function to ensure correct return type for refreshCapNhatDeNghiThanhToans
  const refreshCapNhatDeNghiThanhToans = async (): Promise<void> => {
    await fetchCapNhatDeNghiThanhToans();
  };

  return {
    capNhatDeNghiThanhToans,
    isLoading,
    addCapNhatDeNghiThanhToan,
    updateCapNhatDeNghiThanhToan,
    deleteCapNhatDeNghiThanhToan,
    refreshCapNhatDeNghiThanhToans,
    getCapNhatDeNghiThanhToanById,
    getChiTietByBaoCao,
    addChiTiet,
    updateChiTiet,
    deleteChiTiet
  };
};
