import {
  QUERY_KEYS,
  accountSearchColumns,
  hanThanhToanSearchColumns,
  khachHangSearchColumns,
  nhanVienSearchColumns,
  quyenChungTuSearchColumns,
  taiKhoanNganHangSearchColumns
} from '@/constants';
import { AccountModel, HanThanhToan, KhachHang, NhanVien, QuyenChungTu, type TaiKhoanNganHang } from '@/types/schemas';
import { SearchField, FormField, AritoIcon, UnitDropdown } from '@/components/custom/arito';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';
import { useNgoaiTe } from '@/hooks';

interface BasicInfoTabProps {
  formMode: FormMode;
  formState: {
    state: any;
    actions: any;
  };
}

export function BasicInfoTab({ formMode, formState: { state, actions } }: BasicInfoTabProps) {
  const { currencies } = useNgoaiTe();

  const currencyOptions = currencies.map(currency => ({
    value: currency.uuid,
    label: currency.ma_nt
  }));

  return (
    <div className='flex flex-col gap-3 p-4'>
      {/* Customer Information Section */}
      <div className='flex'>
        {/* Left Column */}
        <div className='flex basis-2/3 flex-col gap-3'>
          <div className='w-full'>
            <div className='flex items-center gap-2'>
              <Label className='w-32 min-w-32 text-left'>Loại chứng từ</Label>
              <div className='max-w-md flex-1'>
                <FormField
                  type='select'
                  options={[
                    { value: '1', label: '1. Chi theo hóa đơn' },
                    { value: '2', label: '2. Chi theo đối tượng' },
                    { value: '3', label: '3. Chi khác' },
                    { value: '4', label: '4. Rút tiền về nhập quỹ' },
                    { value: '5', label: '5. Chuyển giữa các ngân hàng' }
                  ]}
                  name='ma_ngv'
                  value={state.loaiChungTu}
                  onValueChange={actions.setLoaiChungTu}
                  disabled={formMode === 'view'}
                />
              </div>
            </div>
          </div>
          <div className='flex items-center'>
            <Label className='w-32 min-w-32'>Địa chỉ</Label>
            <FormField type='text' name='dia_chi' disabled={formMode === 'view'} />
          </div>

          <div className='flex items-center'>
            <Label className='w-32 min-w-32'>Người nhận tiền</Label>
            <FormField type='text' name='ong_ba' disabled={formMode === 'view'} />
          </div>

          {/* Description Field */}
          <div className='flex items-center'>
            <Label className='w-32 min-w-32'>Diễn giải</Label>
            <FormField type='text' name='dien_giai' disabled={formMode === 'view'} />
          </div>

          {/* Bank and Account Fields */}
          <div className='flex items-center gap-2'>
            <Label className='w-32 min-w-32'>Ngân hàng</Label>
            <div className='flex gap-2'>
              <SearchField<TaiKhoanNganHang>
                type='text'
                value={state.bank?.ma_ngan_hang || ''}
                searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN_NGAN_HANG}`}
                searchColumns={taiKhoanNganHangSearchColumns}
                columnDisplay='account_code'
                displayRelatedField='name'
                dialogTitle='Danh mục tài khoản ngân hàng'
                onRowSelection={actions.setBank}
                relatedFieldValue={state.bank?.name || ''}
                disabled={formMode === 'view'}
              />
              <div className='flex items-center gap-2'>
                <Label className='whitespace-nowrap'>Tài khoản có</Label>
                <SearchField<AccountModel>
                  type='text'
                  value={state.account?.code || ''}
                  searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
                  searchColumns={accountSearchColumns}
                  columnDisplay='code'
                  displayRelatedField='name'
                  dialogTitle='Danh mục tài khoản'
                  className='w-40'
                  onRowSelection={actions.setAccount}
                  disabled={formMode === 'view'}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className='flex basis-1/3'>
          {/* Tab Content */}
          <div className='flex-1 pr-2'>
            <div>
              <UnitDropdown formMode={formMode} labelClassName='w-32' selectClassName='min-w-40' />

              <div className='flex items-center'>
                <Label className='w-32'>Số chứng từ</Label>
                <SearchField<QuyenChungTu>
                  type='text'
                  value={state.quyenChungTu?.ma_nk || ''}
                  searchEndpoint={`/${QUERY_KEYS.QUYEN_CHUNG_TU}`}
                  searchColumns={quyenChungTuSearchColumns}
                  columnDisplay='ma_nk'
                  dialogTitle='Danh mục quyển'
                  onRowSelection={actions.setQuyenChungTu}
                  disabled={formMode === 'view'}
                  className='w-8'
                />
                <FormField name='so_ct' type='text' disabled={formMode === 'view'} className='ml-2 w-32' />
              </div>
              <div className='flex items-center'>
                <Label className='w-32'>Ngày chứng từ</Label>
                <FormField name='ngay_ct' type='date' disabled={formMode === 'view'} className='w-40' />
              </div>
              <div className='flex items-center'>
                <Label className='w-32'>Ngày lập chứng từ</Label>
                <FormField name='ngay_lct' type='date' disabled={formMode === 'view'} className='w-40' />
              </div>

              <div className='flex'>
                <div className='flex items-center'>
                  <Label className='w-[127px]'>Ngoại tệ</Label>
                  <div className='w-[100px]'>
                    <FormField
                      name='ma_nt'
                      type='select'
                      disabled={formMode === 'view'}
                      labelClassName='w-32 shrink-0'
                      options={currencyOptions}
                    />
                  </div>
                </div>
                <div>
                  <FormField name='ty_gia' type='text' disabled={formMode === 'view'} />
                </div>
              </div>
              <div className='flex items-center'>
                <Label className='w-32'>Trạng thái</Label>
                <FormField
                  name='status'
                  type='select'
                  className='min-w-40'
                  disabled={formMode === 'view'}
                  options={[
                    { value: '0', label: 'Lập chứng từ' },
                    { value: '3', label: 'Chờ duyệt' },
                    { value: '5', label: 'Xuất hóa đơn' },
                    { value: '7', label: 'Bỏ duyệt đơn hàng' }
                  ]}
                />
              </div>

              <div className='mt-2 flex'>
                <div className='mb-4 h-2 w-32 shrink-0' />
                <FormField
                  label='Dữ liệu nhận được'
                  name='transfer_yn'
                  type='checkbox'
                  disabled={true}
                  labelClassName='w-32'
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
