/**
 * Document Type Codes (MA_CHUNG_TU) Constants
 *
 * This file contains all the document type codes used throughout the ERP system,
 * organized by business categories for easy reference and maintenance.
 */

export const MA_CHUNG_TU = {
  TIEN_MAT: {
    PHIEU_THU: 'PT1',
    PHIEU_CHI: 'PC1',
    CAP_NHAT_DE_NGHI_CHI: 'DNC'
  },

  TIEN_GUI: {
    GIAY_BAO_CO: 'BC1',
    GIAY_BAO_NO: 'BN1',
    CAP_NHAT_DE_NGHI_CHI: 'DNC'
  },

  BAN_HANG: {
    BAO_GIA: 'BG1',
    CHUNG_TU_PHAI_THU_KHAC: 'CN1',
    BUT_TOAN_DIEU_CHINH_GIAM_CONG_NO: 'CN2',
    DON_HANG: 'DH1',
    HOA_DON_DIEU_CHINH_GIA_HANG_BAN: 'GG1',
    HOA_DON_BAN_HANG: 'HD1',
    HOA_DON_BAN_DICH_VU: 'HD2',
    PHIEU_NHAP_HANG_BAN_TRA_LAI: 'TL1',
    HOA_DON_DICH_VU_TRA_LAI_GIAM_GIA: 'TL2',
    HOA_DON_DIEU_CHINH_THONG_TIN: 'DC9',
    DON_HANG_LAZADA: 'DHO'
  },

  HOA_DON: {
    HOA_DON_DIEU_CHINH_GIA_HANG_BAN: 'GG1',
    HOA_DON_BAN_HANG: 'HD1',
    HOA_DON_BAN_DICH_VU: 'HD2',
    HOA_DON_DAU_VAO: '130',
    PHIEU_XUAT_DIEU_CHUYEN: 'PX2',
    HOA_DON_DICH_VU_TRA_LAI_GIAM_GIA: 'TL2',
    PHIEU_XUAT_TRA_LAI_NHA_CUNG_CAP: 'TL5',
    HOA_DON_DICH_VU_TRA_LAI_NHA_CUNG_CAP: 'TL6'
  },

  MUA_HANG: {
    CHUNG_TU_PHAI_TRA_KHAC: 'CN5',
    BUT_TOAN_DIEU_CHINH_GIAM_CONG_NO: 'CN6',
    PHIEU_NHAP_CHI_PHI_MUA_HANG: 'CP1',
    PHIEU_NHAP_DIEU_CHINH_GIA_HANG_MUA: 'DC5',
    DON_HANG_MUA_TRONG_NUOC: 'DH5',
    DON_HANG_MUA_NHAP_KHAU: 'DH6',
    HOA_DON_MUA_HANG_TRONG_NUOC: 'HD4',
    HOA_DON_MUA_HANG_NHAP_KHAU: 'HD5',
    HOA_DON_MUA_DICH_VU: 'HD6',
    HOA_DON_DAU_VAO: '130',
    PHIEU_XUAT_TRA_LAI_NHA_CUNG_CAP: 'TL5',
    HOA_DON_DICH_VU_TRA_LAI_NHA_CUNG_CAP: 'TL6',
    PHIEU_THANH_TOAN_TAM_UNG: 'TU1',
    HOA_DON_NHAP_MUA_XUAT_THANG: 'HD7',
    PHIEU_XUAT_THANG: 'PX4'
  },

  TON_KHO: {
    PHIEU_YEU_CAU_KIEM_KE: 'KK1',
    PHIEU_NHAP_KHO: 'PN1',
    PHIEU_NHAP_DIEU_CHUYEN: 'PN2',
    PHIEU_NHAP_XUAT_THANG: 'PNX',
    PHIEU_XUAT_KHO: 'PX1',
    PHIEU_XUAT_DIEU_CHUYEN: 'PX2',
    PHIEU_YEU_CAU_XUAT_KHO: 'YC1'
  },

  GIA_THANH: {
    DINH_MUC_NGUYEN_VAT_LIEU: 'DM1',
    LENH_SAN_XUAT: 'SX1'
  },

  THUE: {
    TO_KHAI: 'TK1'
  },

  TONG_HOP: {
    PHIEU_KE_TOAN: 'PK1',
    BUT_TOAN_HUY_DAO: 'PK2',
    PHIEU_KE_TOAN_THEO_NGHIEP_VU: 'PK3',
    VAO_CAC_HOA_DON_GTGT_DAU_RA: 'R20',
    VAO_CAC_HOA_DON_GTGT_DAU_VAO: 'R30',
    SO_DU_DAU_KY_HOA_DON_DAU_RA: 'B20',
    SO_DU_DAU_KY_HOA_DON_DAU_VAO: 'B30',
    CHUNG_TU_THANH_TOAN_DAU_KY_HOA_DON_DAU_RA: 'C20',
    CHUNG_TU_THANH_TOAN_DAU_KY_HOA_DON_DAU_VAO: 'C30',
    CHUNG_TU_TAT_TOAN: 'T30'
  },

  BAN_HANG_2: {
    BAO_GIA: 'BG1',
    DON_HANG: 'DH1',
    PHIEU_GIAO_HANG: 'GH1',
    HOA_DON_BAN_HANG: 'HD1',
    LENH_XUAT_BAN_HANG: 'LX1',
    PHIEU_XUAT_BAN_HANG: 'PX3',
    PHIEU_NHAP_HANG_BAN_TRA_LAI: 'TL1',
    PHIEU_NHAP_HANG_BAN_TRA_LAI_THUC_TE: 'TL3',
    DON_HANG_LAZADA: 'DHO'
  },

  E_PROCUREMENT: {
    GIAY_DE_NGHI_BAO_GIA: 'BG5',
    CAP_NHAT_GIAY_BAO_GIA: 'BG6',
    DON_HANG_MUA_TRONG_NUOC: 'DH5',
    DON_HANG_MUA_NHAP_KHAU: 'DH6',
    HOA_DON_MUA_HANG_TRONG_NUOC: 'HD4',
    HOA_DON_MUA_HANG_NHAP_KHAU: 'HD5',
    PHIEU_NHU_CAU_VAT_TU: 'NC1',
    PHIEU_NHAP_MUA_HANG: 'PN4',
    SO_SANH_GIA: 'SS1',
    PHIEU_XUAT_TRA_LAI_NHA_CUNG_CAP: 'TL5',
    PHIEU_XUAT_TRA_LAI_NHA_CUNG_CAP_THUC_TE: 'TL7',
    CHON_NHA_CUNG_CAP: 'BG7',
    PHIEU_XUAT_THANG: 'PX4'
  },

  KHO: {
    PHIEU_YEU_CAU_KIEM_KE: 'KK1',
    PHIEU_NHAP_KHO: 'PN1',
    PHIEU_NHAP_DIEU_CHUYEN: 'PN2',
    PHIEU_NHAP_XUAT_THANG: 'PNX',
    PHIEU_XUAT_KHO: 'PX1',
    PHIEU_XUAT_DIEU_CHUYEN: 'PX2',
    PHIEU_YEU_CAU_XUAT_KHO: 'YC1'
  },

  NHAN_SU: {
    DANG_KY_TANG_CA: 'DKC',
    CAP_NHAT_YEU_CAU_TUYEN_DUNG: 'TD1'
  },

  HOA_DON_DAU_VAO: {
    HOA_DON_DAU_VAO: 'I30'
  }
} as const;

/**
 * Type for MA_CHUNG_TU keys
 */
export type MaChungTuCategory = keyof typeof MA_CHUNG_TU;

/**
 * Type for document codes within each category
 */
export type MaChungTuCode<T extends MaChungTuCategory> = (typeof MA_CHUNG_TU)[T][keyof (typeof MA_CHUNG_TU)[T]];

/**
 * Helper function to get all document codes from a category
 */
export const getDocumentCodes = <T extends MaChungTuCategory>(category: T): MaChungTuCode<T>[] => {
  return Object.values(MA_CHUNG_TU[category]) as MaChungTuCode<T>[];
};

/**
 * Helper function to get all document codes as a flat array
 */
export const getAllDocumentCodes = (): string[] => {
  return Object.values(MA_CHUNG_TU).flatMap(category => Object.values(category));
};
