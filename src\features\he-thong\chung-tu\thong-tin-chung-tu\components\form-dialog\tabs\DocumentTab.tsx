'use client';

import { DocumentColumns } from '@/features/he-thong/chung-tu/thong-tin-chung-tu/cols-definition';
import { AritoDataTables } from '@/components/custom/arito';
import { useRows, useQuyenChungTuByChungTu } from '@/hooks';

export const DocumentTab = ({ chung_tu_uuid }: { chung_tu_uuid: string }) => {
  const { selectedObj, selectedRowIndex, handleRowClick } = useRows();
  const { quyenChungTus, isLoading } = useQuyenChungTuByChungTu({
    chung_tu_uuid
  });

  const handleReadOnlyView = () => {};

  const tables = [
    {
      name: '',
      rows: quyenChungTus,
      columns: DocumentColumns(handleReadOnlyView)
    }
  ];

  return <AritoDataTables tables={tables} selectedRowId={selectedRowIndex || undefined} onRowClick={handleRowClick} />;
};
