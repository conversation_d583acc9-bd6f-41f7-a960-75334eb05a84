import { GridColDef } from '@mui/x-data-grid';
import {
  QUERY_KEYS,
  accountSearchColumns,
  boPhanSearchColumns,
  chiPhiSearchColumns,
  dotThanhToanSearchColumns,
  hopDongSearchColumns,
  khachHangSearchColumns,
  kheUocSearchColumns,
  lenhSanXuatSearchColumns,
  phiSearchColumns,
  vatTuSearchColumns,
  vuViecSearchColumns
} from '@/constants';
import {
  AccountModel,
  BoPhan,
  ChiPhi,
  KhachHang,
  DotThanhToan,
  HopDong,
  KheUoc,
  Phi,
  VatTu,
  VuViec
} from '@/types/schemas';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { SearchField } from '@/components/custom/arito';

// Main table
export const getDataTableColumns = (): GridColDef[] => [
  { field: 'trang_thai', headerName: 'Trạng thái', width: 100 },
  { field: 'so_chung_tu', headerName: 'Số c/từ', width: 120 },
  { field: 'ngay_chung_tu', headerName: 'Ngày c/từ', width: 120 },
  { field: 'ma_khach_hang', headerName: 'Mã khách hàng', width: 150 },
  { field: 'ten_khach_hang', headerName: 'Tên khách hàng', width: 200 },
  { field: 'dien_giai', headerName: 'Diễn giải', width: 250 },
  { field: 'tai_khoan_no', headerName: 'Tk nợ', width: 100 },
  { field: 'tong_tien', headerName: 'Tổng tiền', width: 150 },
  { field: 'ngoai_te', headerName: 'Ngoại tệ', width: 100 },
  { field: 'loai_phieu_thu', headerName: 'Loại phiếu thu', width: 150 }
];

// Chi Tiết tab
export const getDetailTableColumns = (
  onCellValueChange?: (rowUuid: string, field: string, newValue: any) => void
): GridColDef[] => [
  {
    field: 'ma_kh',
    headerName: 'Mã khách hàng',
    width: 120,
    renderCell: params => (
      <SearchField<KhachHang>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
        searchColumns={khachHangSearchColumns}
        columnDisplay='customer_code'
        dialogTitle='Danh mục khách hàng'
        value={params.row.ma_kh_data?.customer_code || ''}
        onRowSelection={(row: any) => {
          onCellValueChange?.(params.row.uuid, 'ma_kh_data', row);
        }}
      />
    )
  },
  {
    field: 'ten_kh',
    headerName: 'Tên khách hàng',
    width: 200,
    renderCell: params => <CellField name='ten_kh' type='text' value={params.row.ma_kh_data?.customer_name || ''} />
  },
  {
    field: 'du_cn',
    headerName: 'Dư công nợ',
    width: 150,
    renderCell: params => <CellField name='du_cn' type='number' value={params.row.ma_kh_data?.credit_limit || ''} />
  },
  {
    field: 'id_hd',
    headerName: 'Hóa đơn',
    width: 120,
    renderCell: params => <CellField name='id_hd' type='text' value={params.row.id_hd || ''} />
  },
  {
    field: 'ngay_ct_hd',
    headerName: 'Ngày hóa đơn',
    width: 120,
    renderCell: params => <CellField name='ngay_ct_hd' type='date' value={params.row.ngay_ct_hd || ''} />
  },
  {
    field: 'tk_co',
    headerName: 'Tài khoản có',
    width: 120,
    renderCell: params => <CellField name='tk_co' type='text' value={params.row.tk_co || ''} />
  },
  {
    field: 'ma_nt_hd',
    headerName: 'Ngoại tệ',
    width: 100,
    renderCell: params => <CellField name='ma_nt_hd' type='text' value={params.row.ma_nt_hd || ''} />
  },
  {
    field: 'ty_gia_hd',
    headerName: 'Tỷ giá hđ',
    width: 100,
    renderCell: params => <CellField name='ty_gia_hd' type='number' value={params.row.ty_gia_hd || ''} />
  },
  {
    field: 'tien_hd_nt',
    headerName: 'Tiền trên hóa đơn',
    width: 150,
    renderCell: params => <CellField name='tien_hd_nt' type='number' value={params.row.tien_hd_nt || ''} />
  },
  {
    field: 'da_pb_nt',
    headerName: 'Đã phân bổ',
    width: 120,
    renderCell: params => <CellField name='da_pb_nt' type='number' value={params.row.da_pb_nt || ''} />
  },
  {
    field: 'cl_nt',
    headerName: 'Còn lại',
    width: 120,
    renderCell: params => <CellField name='cl_nt' type='number' value={params.row.cl_nt || ''} />
  },
  {
    field: 'tien_nt',
    headerName: 'Tiền VND',
    width: 150,
    renderCell: params => (
      <CellField
        name='tien_nt'
        type='number'
        value={params.row.tien_nt || 0.0}
        onValueChange={newValue => onCellValueChange?.(params.row.uuid, 'tien_nt', newValue)}
      />
    )
  },
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 250,
    renderCell: params => (
      <CellField
        name='dien_giai'
        type='text'
        value={params.row.dien_giai || ''}
        onValueChange={newValue => onCellValueChange?.(params.row.uuid, 'dien_giai', newValue)}
      />
    )
  },
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 120,
    renderCell: params => (
      <SearchField<BoPhan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
        searchColumns={boPhanSearchColumns}
        columnDisplay='ma_bp'
        dialogTitle='Danh mục bộ phận'
        value={params.row.ma_bp_data?.ma_bp || ''}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'ma_bp_data', row)}
      />
    )
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 120,
    renderCell: params => (
      <SearchField<VuViec>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VU_VIEC}/`}
        searchColumns={vuViecSearchColumns}
        columnDisplay='ma_vu_viec'
        dialogTitle='Danh mục vụ việc'
        value={params.row.ma_vv_data?.ma_vu_viec || ''}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'ma_vv_data', row)}
      />
    )
  },
  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 120,
    renderCell: params => (
      <SearchField<HopDong>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.HOP_DONG}/`}
        searchColumns={hopDongSearchColumns}
        columnDisplay='ma_hd'
        dialogTitle='Danh mục hợp đồng'
        value={params.row.ma_hd_data?.ma_hd || ''}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'ma_hd_data', row)}
      />
    )
  },
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 150,
    renderCell: params => (
      <SearchField<DotThanhToan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}/`}
        searchColumns={dotThanhToanSearchColumns}
        columnDisplay='ma_dtt'
        dialogTitle='Danh mục đợt thanh toán'
        value={params.row.ma_dtt_data?.ma_dtt || ''}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'ma_dtt_data', row)}
      />
    )
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 120,
    renderCell: params => (
      <SearchField<KheUoc>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHE_UOC}/`}
        searchColumns={kheUocSearchColumns}
        columnDisplay='ma_ku'
        dialogTitle='Danh mục khế ước'
        value={params.row.ma_ku_data?.ma_ku || ''}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'ma_ku_data', row)}
      />
    )
  },
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 80,
    renderCell: params => (
      <SearchField<Phi>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.PHI}/`}
        searchColumns={phiSearchColumns}
        columnDisplay='ma_phi'
        dialogTitle='Danh mục phí'
        value={params.row.ma_phi_data?.ma_phi || ''}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'ma_phi_data', row)}
      />
    )
  },
  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 150,
    renderCell: params => (
      <SearchField<VatTu>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
        searchColumns={vatTuSearchColumns}
        columnDisplay='ma_vt'
        dialogTitle='Danh mục sản phẩm'
        value={params.row.ma_sp_data?.ma_vt || ''}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'ma_sp_data', row)}
      />
    )
  },
  {
    field: 'ma_lsx',
    headerName: 'Lệnh sản xuất',
    width: 150,
    renderCell: params => (
      <SearchField<any>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.LENH_SAN_XUAT}/`}
        searchColumns={lenhSanXuatSearchColumns}
        columnDisplay='ma_lsx'
        dialogTitle='Danh mục lệnh sản xuất'
        value={params.row.ma_lsx_data?.ma_lsx || ''}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'ma_lsx_data', row)}
      />
    )
  },
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 150,
    renderCell: params => (
      <SearchField<ChiPhi>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.CHI_PHI}/`}
        searchColumns={chiPhiSearchColumns}
        columnDisplay='ma_cp'
        dialogTitle='Danh mục chi phí không hợp lệ'
        value={params.row.ma_cp_data?.ma_cp || ''}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'ma_cp_data', row)}
      />
    )
  }
];
