import {
  Binoculars,
  FileDown,
  FileSpreadsheet,
  FileText,
  FileUp,
  Pencil,
  Plus,
  Printer,
  RefreshCw,
  Table,
  Trash
} from 'lucide-react';
import AritoColoredDot from '@/components/custom/arito/icon/colored-dot';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoMenuButton } from '@/components/custom/arito';
import { AritoActionBar } from '@/components/custom/arito';

interface Props {
  onAddClick: () => void;
  onEditClick: () => void;
  onDeleteClick: () => void;
  onCopyClick: () => void;
  onViewClick: () => void;
  onRefreshClick: () => void;
  className?: string;
  groupType?: string;
}

export function ActionBar({
  className,
  groupType,
  onAddClick,
  onEditClick,
  onDeleteClick,
  onCopyClick,
  onViewClick,
  onRefreshClick
}: Props) {
  return (
    <AritoActionBar
      titleComponent={
        <div className='flex items-center gap-2'>
          <div>
            <h1 className='text-xl font-bold'>Danh mục nhóm thuế</h1>
            {groupType && (
              <div className='flex flex-row items-center'>
                <AritoColoredDot color='maroon' />
                <div className='ml-2 text-sm text-gray-600'>{groupType}</div>
              </div>
            )}
          </div>
        </div>
      }
    >
      <div className={`grid gap-2 lg:flex ${className}`}>
        <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} variant='primary' />
        <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} />
        <AritoActionButton title='Xóa' icon={Trash} onClick={onDeleteClick} />
        <AritoActionButton title='Sao chép' icon={FileText} onClick={onCopyClick} />
        <AritoActionButton title='Xem' icon={Binoculars} onClick={onViewClick} />
        <AritoMenuButton
          title='Khác'
          items={[
            {
              title: 'Tìm kiếm',
              icon: Binoculars,
              onClick: () => {},
              group: 0
            },
            {
              title: 'Refresh',
              icon: RefreshCw,
              onClick: onRefreshClick,
              group: 1
            },
            {
              title: 'Cố định cột',
              icon: Table,
              onClick: () => {},
              group: 1
            },
            {
              title: 'In nhiều',
              icon: Printer,
              onClick: () => {},
              group: 2
            },
            {
              title: 'Kết xuất dữ liệu',
              icon: FileDown,
              onClick: () => {},
              group: 2
            },
            {
              title: 'Tải mẫu Excel',
              icon: FileSpreadsheet,
              onClick: () => {},
              group: 3
            },
            {
              title: 'Lấy dữ liệu từ Excel',
              icon: FileUp,
              onClick: () => {},
              group: 3
            }
          ]}
        />
      </div>
    </AritoActionBar>
  );
}
