import { GridColDef } from '@mui/x-data-grid';
import type { ExtendedGridColDef } from '@/components/custom/arito';

export const getDataTableColumns = (): GridColDef[] => [
  { field: 'ma_dcnh', headerName: 'Mã địa chỉ', width: 120 },
  { field: 'ten_dcnh', headerName: 'Tên địa chỉ', width: 200 },
  {
    field: 'ma_kho',
    headerName: 'Mã kho',
    width: 100,
    renderCell: (params: any) => params.row.ma_kho_data?.ma_kho || ''
  }
];

export const khoSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_kho', headerName: 'Mã kho', width: 120 },
  { field: 'ten_kho', headerName: 'Tên kho', width: 250 },
  {
    field: 'don_vi',
    headerName: 'Đơn vị',
    width: 120,
    renderCell: (params: { row: any }) => params?.row?.unit_data?.ten_unit || ''
  },
  { field: 'theo_doi_vi_tri', headerName: '<PERSON> dõi vị trí', width: 120, checkboxSelection: true }
];
