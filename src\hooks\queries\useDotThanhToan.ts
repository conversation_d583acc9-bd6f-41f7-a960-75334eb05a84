import { useState, useEffect } from 'react';
import { DotThanhToan, DotThanhToanInput, DotThanhToanResponse } from '@/types/schemas/dot-thanh-toan.type';
import { useAuth } from '@/contexts/auth-context';
import { QUERY_KEYS } from '@/constants';
import api from '@/lib/api';

interface UseDotThanhToanReturn {
  paymentInstallments: DotThanhToan[];
  isLoading: boolean;
  addPaymentInstallment: (newPaymentInstallment: DotThanhToanInput) => Promise<void>;
  updatePaymentInstallment: (uuid: string, updatedPaymentInstallment: DotThanhToanInput) => Promise<void>;
  deletePaymentInstallment: (uuid: string) => Promise<void>;
  refreshPaymentInstallments: () => Promise<void>;
}

export const useDotThanhToan = (initialPaymentInstallments: DotThanhToan[] = []): UseDotThanhToanReturn => {
  const [paymentInstallments, setPaymentInstallments] = useState<DotThanhToan[]>(initialPaymentInstallments);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const { entity } = useAuth();

  const fetchPaymentInstallments = async () => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.get<DotThanhToanResponse>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.DOT_THANH_TOAN}/`
      );
      setPaymentInstallments(response.data.results);
    } catch (error) {
      console.error('Error fetching payment installments:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const addPaymentInstallment = async (newPaymentInstallment: DotThanhToanInput) => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.post(`/entities/${entity.slug}/erp/${QUERY_KEYS.DOT_THANH_TOAN}/`, {
        ma_dtt: newPaymentInstallment.ma_dtt,
        ten_dtt: newPaymentInstallment.ten_dtt,
        ten_dtt2: newPaymentInstallment.ten_dtt2 === null ? '' : newPaymentInstallment.ten_dtt2,
        stt: newPaymentInstallment.stt,
        status: newPaymentInstallment.status
      });
      await fetchPaymentInstallments();
      return response.data;
    } catch (error) {
      console.error('Error adding payment installment:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updatePaymentInstallment = async (uuid: string, updatedPaymentInstallment: DotThanhToanInput) => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.put(`/entities/${entity.slug}/erp/${QUERY_KEYS.DOT_THANH_TOAN}/${uuid}/`, {
        ma_dtt: updatedPaymentInstallment.ma_dtt,
        ten_dtt: updatedPaymentInstallment.ten_dtt,
        ten_dtt2: updatedPaymentInstallment.ten_dtt2 === null ? '' : updatedPaymentInstallment.ten_dtt2,
        stt: updatedPaymentInstallment.stt,
        status: updatedPaymentInstallment.status
      });
      await fetchPaymentInstallments();
      return response.data;
    } catch (error) {
      console.error('Error updating payment installment:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const deletePaymentInstallment = async (uuid: string) => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      await api.delete(`/entities/${entity.slug}/erp/${QUERY_KEYS.DOT_THANH_TOAN}/${uuid}/`);
      await fetchPaymentInstallments();
    } catch (error) {
      console.error('Error deleting payment installment:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const refreshPaymentInstallments = fetchPaymentInstallments;

  useEffect(() => {
    if (initialPaymentInstallments.length === 0) {
      fetchPaymentInstallments();
    } else {
      setIsLoading(false);
    }
  }, [entity?.slug]);

  return {
    paymentInstallments,
    isLoading,
    addPaymentInstallment,
    updatePaymentInstallment,
    deletePaymentInstallment,
    refreshPaymentInstallments
  };
};
