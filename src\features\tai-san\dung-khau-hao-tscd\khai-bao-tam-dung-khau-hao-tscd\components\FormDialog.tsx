import { useState } from 'react';
import { AritoForm, BottomBar, AritoDialog, AritoIcon } from '@/components/custom/arito';
import ConfirmationDialog from '@/components/custom/arito/dialog/confirm-dialog';
import { KhaiBaoTamDungKhauHaoTSCDInput, TaiSanCoDinh } from '@/types/schemas';
import { FormValues, FormSchema } from '../schema';
import BasicInfoTab from './BasicInfoTab';

interface FormDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: KhaiBaoTamDungKhauHaoTSCDInput) => void;
  onAdd?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onCopy?: () => void;
  formMode: 'add' | 'edit' | 'view';
  initialData: any;
}

const FormDialog = ({
  open,
  onClose,
  onSubmit,
  onAdd,
  onEdit,
  onDelete,
  onCopy,
  formMode,
  initialData
}: FormDialogProps) => {
  const [taiSan, setTaiSan] = useState<TaiSanCoDinh | null>(initialData?.ma_ts_data || null);
  const [showConfirmation, setShowConfirmation] = useState(false);

  const handleSubmit = async (data: FormValues) => {
    try {
      const formattedData: KhaiBaoTamDungKhauHaoTSCDInput = {
        ngay_hl_tu: data.ngay_hl_tu || '',
        ngay_hl_den: data.ngay_hl_den || '',
        ma_ts: taiSan?.uuid || ''
      };
      await onSubmit(formattedData);
      onClose();
    } catch (err: any) {
      console.log('err', err);
    }
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title={`${formMode === 'add' ? 'Mới' : formMode === 'edit' ? 'Sửa' : 'Khai báo tạm dừng khấu hao TSCĐ'}`}
      maxWidth='xl'
      disableBackdropClose={false}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={281} />}
    >
      <AritoForm
        mode={formMode}
        hasAritoActionBar={false}
        schema={FormSchema}
        onSubmit={handleSubmit}
        initialData={initialData}
        className='w-[800px]'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
            <BasicInfoTab formMode={formMode} taiSan={taiSan} setTaiSan={setTaiSan} />
          </div>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2'
        bottomBar={
          <BottomBar
            mode={formMode}
            onAdd={onAdd}
            onEdit={onEdit}
            onDelete={onDelete}
            onCopy={onCopy}
            onClose={onClose}
          />
        }
      />

      {showConfirmation && (
        <ConfirmationDialog
          open={showConfirmation}
          onClose={() => setShowConfirmation(false)}
          onConfirm={onClose}
          title='Cảnh báo'
          message={'Bạn muốn kết thúc?'}
        />
      )}
    </AritoDialog>
  );
};

export default FormDialog;
