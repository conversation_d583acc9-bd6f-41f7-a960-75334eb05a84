'use client';

import React from 'react';
import { useTaxRateIntegration } from './hooks/useTaxRateIntegration';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { FormDialog, ActionBar, DeleteDialog } from './components';
import { getDataTableColumns } from './cols-definition';
import { useFormState, useRows } from './hooks';

export default function ThueSuatThueGTGTPage() {
  const {
    showForm,
    showDelete,
    formMode,
    isCopyMode,
    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick
  } = useFormState();

  const { selectedObj, selectedRowIndex, tableData, isLoading, refreshTaxRates, handleRowClick, clearSelection } =
    useRows();

  const { handleAddTaxRate, handleUpdateTaxRate, handleDeleteTaxRate } = useTaxRateIntegration();

  const tables = [
    {
      name: '',
      rows: tableData,
      columns: getDataTableColumns()
    }
  ];

  const handleSubmit = async (data: any): Promise<void> => {
    let success = false;

    if (formMode === 'add') {
      success = await handleAddTaxRate(data);
    } else if (formMode === 'edit' && selectedObj?.uuid) {
      success = await handleUpdateTaxRate(selectedObj.uuid, data);
    }

    if (success) {
      handleCloseForm();
      await refreshTaxRates();
    } else {
      // If not successful, throw an error to keep the form open
      throw new Error('Failed to save tax rate');
    }
  };

  const handleDelete = async () => {
    if (selectedObj?.uuid) {
      const success = await handleDeleteTaxRate(selectedObj.uuid);
      if (success) {
        handleCloseDelete();
        clearSelection();
        await refreshTaxRates();
      }
    }
  };

  return (
    <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
      <ActionBar
        onAddClick={handleAddClick}
        onEditClick={() => selectedObj && handleEditClick()}
        onViewClick={() => selectedObj && handleViewClick()}
        onCopyClick={() => selectedObj && handleCopyClick()}
        onDeleteClick={() => selectedObj && handleDeleteClick()}
        onRefreshClick={refreshTaxRates}
        isEditDisabled={!selectedObj}
        isViewDisabled={!selectedObj}
        isDeleteDisabled={!selectedObj}
        isCopyDisabled={!selectedObj}
      />

      {isLoading ? (
        <div className='flex h-64 items-center justify-center'>
          <div className='size-12 animate-spin rounded-full border-b-2 border-t-2 border-blue-500'></div>
        </div>
      ) : (
        <AritoDataTables tables={tables} onRowClick={handleRowClick} selectedRowId={selectedRowIndex || undefined} />
      )}

      {showForm && (
        <FormDialog
          open={showForm}
          onSubmit={handleSubmit}
          formMode={formMode}
          initialData={
            selectedObj && (formMode === 'edit' || formMode === 'view' || (formMode === 'add' && isCopyMode))
              ? selectedObj
              : undefined
          }
          onClose={handleCloseForm}
          onAdd={() => {
            handleCloseForm();
            clearSelection();
            setTimeout(() => {
              handleAddClick();
            }, 100);
          }}
          onEdit={() => {
            if (selectedObj) {
              handleCloseForm();
              handleEditClick();
            }
          }}
          onDelete={() => {
            if (selectedObj) {
              handleCloseForm();
              handleDeleteClick();
            }
          }}
          onCopy={() => {
            if (selectedObj) {
              handleCloseForm();
              handleCopyClick();
            }
          }}
        />
      )}

      {showDelete && (
        <DeleteDialog
          open={showDelete}
          onClose={handleCloseDelete}
          selectedObj={selectedObj}
          deleteObj={handleDelete}
          clearSelection={clearSelection}
        />
      )}
    </div>
  );
}
