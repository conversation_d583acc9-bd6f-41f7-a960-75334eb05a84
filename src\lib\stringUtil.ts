/**
 * String utility functions for generating document codes in ERP system
 * Following Vietnamese business document formatting conventions
 */

/**
 * Generates document code based on provided format
 * @param soChungTuHienTai - Current document sequence number
 * @param format - Document format string (e.g., "HD.[MM].[YY].[######]" or "CD.[MM].[YY].[#####]")
 * @param date - Optional date parameter (defaults to current date)
 * @returns Formatted document code
 * @example
 * generateSoChungTuHienTai(1, "HD.[MM].[YY].[######]") // Returns: "HD.06.25.000001" (assuming current date is June 2025)
 * generateSoChungTuHienTai(2, "CD.[MM].[YY].[#####]") // Returns: "CD.06.25.00002" (assuming current date is June 2025)
 * generateSoChungTuHienTai(1, "HD.[MM].[YY].[######]", new Date('2024-12-15')) // Returns: "HD.12.24.000001"
 */
export function generateSoChungTuHienTai(soChungTuHienTai: number, format: string, date?: Date): string {
  const targetDate = date || new Date();
  const month = String(targetDate.getMonth() + 1).padStart(2, '0');
  const year = String(targetDate.getFullYear()).slice(-2);

  let result = format;

  // Replace month and year
  result = result.replace('[MM]', month);
  result = result.replace('[YY]', year);

  // Handle sequence number formats generically (e.g., [#####], [######], [###], etc.)
  const sequencePattern = /\[(#+)\]/g;
  result = result.replace(sequencePattern, (_, hashes) => {
    const paddingLength = hashes.length;
    return String(soChungTuHienTai + 1).padStart(paddingLength, '0');
  });

  return result;
}
