import { GridEventListener } from '@mui/x-data-grid';
import { ChiTietHoaDonBanHangDichVu } from '@/types/schemas/hoa-don-ban-dich-vu.type';
import { InputTable } from '@/components/custom/arito/custom-input-table';
import { getDetailTableColumns } from '../../../cols-definition';
import { useTaxRate } from '@/hooks/queries/useTaxRate';
import InputTableActionBar from './InputTableActionBar';
import { FormMode } from '@/types/form';

interface DetailTabProps {
  rows: ChiTietHoaDonBanHangDichVu[];
  selectedRowUuid: string | null;
  handleRowClick: GridEventListener<'rowClick'>;
  handleAddRow: () => void;
  handleDeleteRow: () => void;
  handleCopyRow: () => void;
  handlePasteRow: () => void;
  handleMoveRow: (direction: 'up' | 'down') => void;
  handleCellValueChange: (rowUuid: string, field: string, newValue: any) => void;
  formMode: FormMode;
}

export default function DetailTab({
  rows,
  selectedRowUuid,
  handleRowClick,
  handleAddRow,
  handleDeleteRow,
  handleCopyRow,
  handlePasteRow,
  handleMoveRow,
  handleCellValueChange,
  formMode
}: DetailTabProps) {
  const { taxRates } = useTaxRate();

  return (
    <div className='h-screen overflow-hidden'>
      <InputTable<ChiTietHoaDonBanHangDichVu>
        rows={rows}
        onRowClick={handleRowClick}
        selectedRowId={selectedRowUuid || undefined}
        columns={getDetailTableColumns(taxRates, handleCellValueChange)}
        getRowId={row => row?.uuid || ''}
        mode={formMode}
        actionButtons={
          <InputTableActionBar
            mode={formMode}
            handleAddRow={handleAddRow}
            handleDeleteRow={handleDeleteRow}
            handleCopyRow={handleCopyRow}
            handlePasteRow={handlePasteRow}
            handleMoveRow={handleMoveRow}
            onExport={() => console.log('Export clicked')}
            onPin={() => console.log('Pin clicked')}
          />
        }
        className='h-full'
      />
    </div>
  );
}
