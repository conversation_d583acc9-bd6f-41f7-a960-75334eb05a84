'use client';

import React from 'react';
import { AritoSplitPane } from '@/components/custom/arito/split-pane';
import SidebarButton from '@/components/custom/arito/sidebar-button';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { ActionBar, FormDialog, DeleteDialog } from './components';
import { LoadingOverlay } from '@/components/custom/arito';
import { getDataTableColumns } from './cols-definition';
import { useFormState, useTaxGroup } from './hooks';

export default function NhomThue() {
  const { taxGroups, isLoading, addTaxGroup, updateTaxGroup, deleteTaxGroup, refreshTaxGroups, fetchTaxGroupsByType } =
    useTaxGroup();

  const {
    showForm,
    showDelete,
    formMode,
    selectedObj,
    selectedRowIndex,
    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick,
    handleRowClick,
    clearSelection
  } = useFormState();

  // Group types and filtering state
  const taxGroupTypes = [{ label: 'Nhóm thuế (TAX)', value: 'TAX' }];

  const [activeGroup, setActiveGroup] = React.useState<string>(taxGroupTypes[0].value);

  // Filter groups based on active group type
  const filteredGroups = React.useMemo(() => {
    return taxGroups.filter(group => group.loai_nhom === activeGroup);
  }, [taxGroups, activeGroup]);

  // Fetch initial data with active group
  React.useEffect(() => {
    fetchTaxGroupsByType(activeGroup);
  }, []);

  // Handle group type filter change
  const handleFilter = (group: string) => {
    setActiveGroup(group);
    fetchTaxGroupsByType(group);
  };

  const handleFormSubmit = async (data: any) => {
    try {
      // Add the active group type to the form data
      const formData = {
        ...data,
        loai_nhom: activeGroup
      };

      if (formMode === 'add') {
        await addTaxGroup(formData);
        await refreshTaxGroups(activeGroup);
      } else if (formMode === 'edit' && selectedObj) {
        await updateTaxGroup({
          uuid: selectedObj.uuid,
          ...formData
        });
        await refreshTaxGroups(activeGroup);
      }
      handleCloseForm();
      clearSelection();
    } catch (error) {
      return;
    }
  };

  const tables = [
    {
      name: '',
      rows: filteredGroups,
      columns: getDataTableColumns()
    }
  ];
  console.log('Selected obj', selectedObj);
  return (
    <div className='flex h-screen flex-col lg:overflow-hidden'>
      <AritoSplitPane split='vertical' minSize={200} defaultSize={250}>
        <div className='h-full overflow-y-auto border-r p-4'>
          {/* Sidebar: Loại nhóm */}
          <h2 className='mb-4 text-lg font-semibold'>Loại nhóm</h2>
          <ul>
            {taxGroupTypes.map(group => (
              <li key={group.value} className='mb-2 text-sm'>
                <SidebarButton isActive={activeGroup === group.value} onClick={() => handleFilter(group.value)}>
                  {group.label}
                </SidebarButton>
              </li>
            ))}
          </ul>
        </div>
        <div className='h-full overflow-y-auto'>
          <div className='w-full'>
            <ActionBar
              onAddClick={handleAddClick}
              onEditClick={() => selectedObj && handleEditClick()}
              onDeleteClick={() => selectedObj && handleDeleteClick()}
              onCopyClick={() => selectedObj && handleCopyClick()}
              onViewClick={() => selectedObj && handleViewClick()}
              onRefreshClick={() => refreshTaxGroups(activeGroup)}
            />

            {isLoading && <LoadingOverlay />}

            {!isLoading && (
              <AritoDataTables
                tables={tables}
                selectedRowId={selectedRowIndex || undefined}
                onRowClick={handleRowClick}
              />
            )}
          </div>
        </div>
      </AritoSplitPane>

      {showDelete && (
        <DeleteDialog
          open={showDelete}
          onClose={handleCloseDelete}
          selectedObj={selectedObj}
          deleteTaxGroup={deleteTaxGroup}
          clearSelection={clearSelection}
        />
      )}

      {showForm && (
        <FormDialog
          open={showForm}
          onClose={handleCloseForm}
          onSubmit={handleFormSubmit}
          formMode={formMode}
          initialData={formMode === 'add' && !isCopyMode ? undefined : selectedObj}
          onAddButtonClick={() => {
            handleCloseForm();
            clearSelection();
            handleAddClick();
          }}
          onEditButtonClick={() => {
            if (selectedObj) {
              handleCloseForm();
              handleEditClick();
            }
          }}
          onDeleteButtonClick={() => {
            if (selectedObj) {
              handleCloseForm();
              handleDeleteClick();
            }
          }}
          onCopyButtonClick={() => {
            if (selectedObj) {
              handleCloseForm();
              handleCopyClick();
            }
          }}
        />
      )}
    </div>
  );
}
