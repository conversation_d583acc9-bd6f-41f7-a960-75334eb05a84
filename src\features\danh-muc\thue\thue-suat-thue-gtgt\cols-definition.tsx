import { GridColDef } from '@mui/x-data-grid';

export const getDataTableColumns = (): GridColDef[] => [
  { field: 'ma_thue', headerName: 'Mã thuế', width: 120 },
  { field: 'ten_thue', headerName: 'Tên thuế', width: 200 },
  { field: 'thue_suat', headerName: 'Thuế suất', width: 120 },
  { field: 'nhom_thue', headerName: 'Nhóm thuế', width: 150 },
  { field: 'tk_thue_dau_ra', headerName: 'TK thuế đầu ra', width: 150 },
  { field: 'tk_thue_dau_ra_duoc_gia', headerName: 'TK thuế đầu ra được giảm', width: 180 },
  { field: 'tk_thue_dau_vao', headerName: 'TK thuế đầu vào', width: 150 },
  { field: 'tk_thue_dau_vao_duoc_gia', headerName: 'TK thuế đầu vào được giảm', width: 180 }
];
