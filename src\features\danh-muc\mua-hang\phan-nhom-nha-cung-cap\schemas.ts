import { z } from 'zod';
import { GroupType } from '@/types/schemas';

export interface SupplierGroupFormattedData {
  ma_nhom: string;
  ten_phan_nhom: string;
  ten2?: string | null;
  trang_thai: string;
  loai_nhom: GroupType;
}

export const searchSchema = z.object({
  ma_nhom: z.string().min(1, 'Mã nhóm là bắt buộc'),
  ten_phan_nhom: z.string().min(1, 'Tên nhóm là bắt buộc'),
  ten2: z.string().optional().nullable(),
  trang_thai: z.coerce.string()
});
export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialValues = {
  ma_nhom: '',
  ten_phan_nhom: '',
  ten2: '',
  trang_thai: '1'
};
