'use client';

import { useState, useEffect, useMemo } from 'react';
import { getFormTitle, getFormActionButtons, calculateTotals, transformFormData } from '../../utils';
import { AritoHeaderTabs, AritoForm, LoadingOverlay } from '@/components/custom/arito';
import { useFormFieldState, useLoading } from '../../hooks';
import { useDetailRows, useAccountRows } from './hooks';
import { useAuth } from '@/contexts/auth-context';
import { AccountInfoTab } from './AccountInfoTab';
import { ConfirmDialog } from '../../components';
import { initialFormValues } from '../../schema';
import { BasicInfoTab } from './BasicInfoTab';
import { HistoryTab } from './HistoryTab';
import { FormMode } from '@/types/form';
import { BottomBar } from './BottomBar';
import { DetailTab } from './DetailTab';
import { OtherTab } from './OtherTab';
import { HDDTTab } from './HDDTTab';

interface FormDialogProps {
  formMode: FormMode;
  open: boolean;
  initialData?: any;
  onSubmit?: (data: any) => void;
  onClose: () => void;
  onAdd?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onCopy?: () => void;
}

const FormDialog = ({
  open,
  formMode,
  initialData,
  onClose,
  onSubmit,
  onAdd,
  onEdit,
  onDelete,
  onCopy
}: FormDialogProps) => {
  const [isConfirm, setIsConfirm] = useState<boolean>(false);
  const [isFormDirty, setIsFormDirty] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>('info');
  const { entityUnit } = useAuth();
  const {
    rows: detailRows,
    selectedRowUuid: detailSelectedRowUuid,
    handleRowClick: detailHandleRowClick,
    handleAddRow: detailHandleAddRow,
    handleDeleteRow: detailHandleDeleteRow,
    handleCopyRow: detailHandleCopyRow,
    handlePasteRow: detailHandlePasteRow,
    handleMoveRow: detailHandleMoveRow,
    handleCellValueChange: detailHandleCellValueChange
  } = useDetailRows(initialData?.chi_tiet || []);
  const {
    rows: accountRows,
    selectedRowUuid: accountSelectedRowUuid,
    handleRowClick: accountHandleRowClick,
    handleAddRow: accountHandleAddRow,
    handleDeleteRow: accountHandleDeleteRow,
    handleCopyRow: accountHandleCopyRow,
    handlePasteRow: accountHandlePasteRow,
    handleMoveRow: accountHandleMoveRow,
    handleCellValueChange: accountHandleCellValueChange
  } = useAccountRows(initialData?.thong_tin_thanh_toan || []);
  const { state, actions } = useFormFieldState(initialData);

  useEffect(() => {
    if (!open) {
      setIsFormDirty(false);
      setActiveTab('info');
    }
  }, [open]);

  const detail = useMemo(() => {
    return initialData?.chi_tiet || detailRows;
  }, [initialData]);

  const { totalAmount, totalQuantity, totalDiscount, totalTax, totalPayment } = useMemo(() => {
    return calculateTotals(detail);
  }, [detail]);

  const handleSubmit = (data: any) => {
    const formData = transformFormData(data, state, detailRows, accountRows);
    console.log('formData: ', { ...formData, unit_id: entityUnit?.uuid });
    onSubmit?.({ ...formData, unit_id: entityUnit?.uuid });
    setIsFormDirty(false);
    onClose();
  };

  const handleClose = () => {
    if (isFormDirty && formMode !== 'view') {
      setIsConfirm(true);
    } else {
      onClose();
    }
  };

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  const title = getFormTitle(formMode, activeTab);
  const actionButtons = getFormActionButtons(formMode, activeTab, {
    onAdd,
    onEdit,
    onDelete,
    onCopy,
    handleClose
  });

  return (
    <>
      <AritoForm
        mode={formMode}
        initialData={initialData || initialFormValues}
        title={title}
        actionButtons={actionButtons}
        subTitle='Hóa đơn bán hàng'
        onSubmit={handleSubmit}
        onClose={handleClose}
        headerFields={
          <div onChange={() => setIsFormDirty(true)}>
            <AritoHeaderTabs
              tabs={[
                {
                  id: 'info',
                  label: 'Thông tin',
                  component: <BasicInfoTab formMode={formMode} formState={{ state, actions }} />
                },
                ...(formMode === 'view'
                  ? [
                      {
                        id: 'history',
                        label: 'Lịch sử',
                        component: <HistoryTab />
                      }
                    ]
                  : [])
              ]}
              onTabChange={handleTabChange}
              defaultTabIndex={activeTab === 'info' ? 0 : activeTab === 'history' ? 1 : 0}
            />
          </div>
        }
        tabs={
          activeTab === 'info' && [
            {
              id: 'details',
              label: 'Chi tiết',
              component: (
                <DetailTab
                  formMode={formMode}
                  rows={detailRows}
                  selectedRowUuid={detailSelectedRowUuid}
                  onRowClick={detailHandleRowClick}
                  onAddRow={detailHandleAddRow}
                  onDeleteRow={detailHandleDeleteRow}
                  onCopyRow={detailHandleCopyRow}
                  onPasteRow={detailHandlePasteRow}
                  onMoveRow={detailHandleMoveRow}
                  onCellValueChange={detailHandleCellValueChange}
                />
              )
            },
            {
              id: 'hddt',
              label: 'HĐĐT(Không sử dụng)',
              component: <HDDTTab formMode={formMode} />
            },
            {
              id: 'other',
              label: 'Khác',
              component: <OtherTab formMode={formMode} formState={{ state, actions }} />
            },
            ...(state.pt_tao_yn && state.ma_httt === 'KB'
              ? [
                  {
                    id: 'account',
                    label: 'Thông tin tài khoản',
                    component: (
                      <AccountInfoTab
                        formMode={formMode}
                        rows={accountRows}
                        selectedRowUuid={accountSelectedRowUuid}
                        onRowClick={accountHandleRowClick}
                        onAddRow={accountHandleAddRow}
                        onDeleteRow={accountHandleDeleteRow}
                        onCopyRow={accountHandleCopyRow}
                        onPasteRow={accountHandlePasteRow}
                        onMoveRow={accountHandleMoveRow}
                        onCellValueChange={accountHandleCellValueChange}
                      />
                    )
                  }
                ]
              : [])
          ]
        }
        bottomBar={
          activeTab === 'info' && (
            <BottomBar
              totalQuantity={totalQuantity}
              totalPayment={totalPayment}
              totalAmount={totalAmount}
              totalDiscount={totalDiscount}
              totalTax={totalTax}
              state={state}
            />
          )
        }
      />

      {isConfirm && (
        <ConfirmDialog
          open={isConfirm}
          onClose={() => setIsConfirm(false)}
          onConfirm={() => {
            setIsConfirm(false);
            setIsFormDirty(false);
            onClose();
          }}
          title='Cảnh báo'
          content='Bạn muốn kết thúc?'
        />
      )}
    </>
  );
};

export default FormDialog;
