import { useState } from 'react';
import { getDataTableColumns } from '../cols-definition';

export const useTableData = () => {
  const [documentRows, setDocumentRows] = useState<any[]>([]);
  const [detailRows, setDetailRows] = useState<any[]>([]);

  const tables = [
    {
      name: 'Tất cả',
      rows: documentRows,
      columns: getDataTableColumns()
    },
    {
      name: 'Chưa ghi sổ',
      rows: documentRows.filter(row => row.status === 'Chưa ghi sổ'),
      columns: getDataTableColumns(),
      icon: <div className='mr-2 size-[7px] rounded-full bg-[#EAD1DC]' />
    },
    {
      name: 'Chờ duyệt',
      rows: documentRows.filter(row => row.status === 'Chờ duyệt'),
      columns: getDataTableColumns(),
      icon: <div className='mr-2 size-[7px] rounded-full bg-red-500' />
    },
    {
      name: 'Đã ghi sổ',
      rows: documentRows.filter(row => row.status === 'Đã ghi sổ'),
      columns: getDataTableColumns(),
      icon: <div className='mr-2 size-[7px] rounded-full bg-[#3D85C6]' />
    },
    {
      name: 'Khác',
      rows: documentRows.filter(row => row.other === true),
      columns: getDataTableColumns(),
      icon: <div className='mr-2 size-[7px] rounded-full bg-black' />
    }
  ];

  return {
    documentRows,
    setDocumentRows,
    detailRows,
    setDetailRows,
    tables
  };
};
