import { hanThanhToanSearchColumns, QUERY_KEYS } from '@/constants';
import { FormField, SearchField } from '@/components/custom/arito';
import { HanThanhToan } from '@/types/schemas';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface ThanhToanTabProps {
  formMode: FormMode;
  hanTT: HanThanhToan;
  setHanTT: (value: HanThanhToan) => void;
}

export function ThanhToanTab({ formMode, hanTT, setHanTT }: ThanhToanTabProps) {
  return (
    <div className='space-y-3 p-4'>
      <div className='flex items-center'>
        <FormField
          name='hd_yn'
          type='checkbox'
          label='Theo dõi thanh toán'
          disabled={formMode === 'view'}
          labelClassName='w-40'
          className='w-96'
        />
      </div>
      <div className='flex items-center'>
        <Label className='w-40'><PERSON><PERSON> chứng từ</Label>
        <FormField name='so_ct' type='text' disabled={formMode === 'view'} />
      </div>
      <div className='flex items-center'>
        <Label className='w-40'>Ngày chứng từ</Label>
        <FormField name='ngay_ct0' type='date' disabled={formMode === 'view'} />
      </div>
      <div className='flex items-center'>
        <Label className='w-40'>Mã thanh toán</Label>
        <SearchField<HanThanhToan>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.HAN_THANH_TOAN}/`}
          searchColumns={hanThanhToanSearchColumns}
          columnDisplay='ma_tt'
          displayRelatedField='ten_tt'
          dialogTitle='Danh mục thanh toán'
          value={hanTT?.ma_tt || ''}
          relatedFieldValue={hanTT?.ten_tt || ''}
          onRowSelection={setHanTT}
          disabled={formMode === 'view'}
        />
      </div>
    </div>
  );
}
