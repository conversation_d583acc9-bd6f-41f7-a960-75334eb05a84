import { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>T<PERSON>, Han<PERSON><PERSON><PERSON><PERSON>oan, QuyenChungTu } from '@/types/schemas';

/**
 * Hook for managing search field states in the phieu-thu AritoForm component
 *
 * This hook centralizes all search field state management for the form tabs:
 * - InfoTab: account (TaiKhoan), voucher (ChungTu)
 * - PaymentInfoTab: paymentTerm (HanThanhToan)
 *
 * @returns Object with states and setters for all search fields used in form tabs
 */
export const useSearchFieldStates = () => {
  const [account, setAccount] = useState<TaiKhoan | null>(null);
  const [quyenChungTu, setQuyenChungTu] = useState<QuyenChungTu | null>(null);
  const [paymentTerm, setPaymentTerm] = useState<HanThanhToan | null>(null);

  return {
    account,
    setAccount,
    quyenChungTu,
    setQuyenChungTu,

    paymentTerm,
    setPaymentTerm
  };
};

export interface SearchFieldStatesProps {
  account: <PERSON><PERSON><PERSON><PERSON> | null;
  setAccount: (account: TaiKhoan | null) => void;
  quyenChungTu: QuyenChungTu | null;
  setQuyenChungTu: (quyenChungTu: QuyenChungTu | null) => void;
  paymentTerm: HanThanhToan | null;
  setPaymentTerm: (paymentTerm: HanThanhToan | null) => void;
}

export interface InfoTabSearchFieldStates {
  account: TaiKhoan | null;
  setAccount: (account: TaiKhoan | null) => void;
  quyenChungTu: QuyenChungTu | null;
  setQuyenChungTu: (quyenChungTu: QuyenChungTu | null) => void;
}

export interface PaymentInfoTabSearchFieldStates {
  paymentTerm: HanThanhToan | null;
  setPaymentTerm: (paymentTerm: HanThanhToan | null) => void;
}
