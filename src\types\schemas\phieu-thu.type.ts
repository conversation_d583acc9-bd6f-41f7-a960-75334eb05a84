/**
 * TypeScript types for PhieuThu (Receipt Voucher) based on Django backend models
 * Generated from @erp-be/django_ledger/models/tien_mat/hach_toan/phieu_thu/
 *
 * Uses existing types from the schemas folder instead of creating new interfaces
 */

// Import existing types from schemas
import { HanThanhToan } from './han-thanh-toan.type';
import { DotThanhToan } from './dot-thanh-toan.type';
import { KhachHang } from './khach-hang.type';
import { AccountModel } from './account.type';
import { Contract } from './contract.type';
import { ChungTu } from './chung-tu.type';
import { NgoaiTe } from './ngoai-te.type';
import { ApiResponse } from '../api.type';
import { BoPhan } from './bo-phan.type';
import { VuViec } from './vu-viec.type';
import { KheUoc } from './khe-uoc.type';
import { VatTu } from './vat-tu.type';
import { DonVi } from './don-vi.type';
import { Phi } from './phi.type';

// PhieuThu Detail Model (matching exact backend fields)
export interface PhieuThuChiTiet {
  uuid: string;
  phieu_thu: string; // UUID reference to PhieuThuModel
  phieu_thu_data?: PhieuThu;
  id?: number;
  line: number;
  dien_giai?: string;
  ma_kh?: string; // UUID reference to KhachHang
  ma_kh_data?: KhachHang;
  id_hd?: string; // Contract ID (first reference)
  id_hd_data?: Contract;
  tk_co: string; // UUID reference to AccountModel (Credit account)
  tk_co_data?: AccountModel;
  ty_gia_hd?: string; // Contract exchange rate (decimal as string)
  ty_gia2?: string; // Exchange rate 2 (decimal as string)
  tien_nt?: string; // Foreign currency amount (decimal as string)
  tien?: string; // Amount (decimal as string)
  ma_bp?: string; // Department code
  ma_bp_data?: BoPhan;
  ma_vv?: string; // Case code
  ma_vv_data?: VuViec;
  ma_hd?: string; // Contract code (second reference)
  ma_hd_data?: Contract;
  ma_dtt?: string; // Payment batch code
  ma_dtt_data?: DotThanhToan;
  ma_ku?: string; // Loan agreement code
  ma_ku_data?: KheUoc;
  ma_phi?: string; // Fee code
  ma_phi_data?: Phi;
  ma_sp?: string; // Product code
  ma_sp_data?: VatTu;
  ma_lsx?: string; // Production order code
  ma_cp0?: string; // Invalid expense code
  ma_cp0_data?: any; // ChiPhiKhongHopLe type not available
  id_tt?: number; // Payment ID
  created: string;
  updated: string;
}

// Main PhieuThu Model
export interface PhieuThu {
  // Primary fields
  uuid: string;
  entity_model: string; // UUID reference to EntityModel
  id: number;

  // Basic information
  ma_ngv?: string; // Business operation code
  dia_chi?: string; // Address
  ong_ba?: string; // Person name
  dien_giai?: string; // Description

  // Account and unit references
  tk: string; // UUID reference to AccountModel (required)
  tk_data?: AccountModel;
  unit_id?: string; // UUID reference to DonVi (EntityUnitModel)
  unit_id_data?: DonVi;

  // Document information
  i_so_ct?: string; // Internal document number
  ma_nk?: string; // UUID reference to QuyenChungTu
  ma_nk_data?: any; // QuyenChungTu type not available
  so_ct?: string; // UUID reference to ChungTu
  so_ct_data?: ChungTu;
  ngay_ct?: string; // Document date (ISO date string)
  ngay_lct?: string; // Document creation date (ISO date string)

  // Currency and exchange rate
  ma_nt?: string; // UUID reference to NgoaiTe
  ma_nt_data?: NgoaiTe;
  ty_gia: string; // Exchange rate (decimal as string)

  // Status and original document
  status: string; // Status (default "1")
  so_ct0?: string; // Original document number (string)
  ngay_ct0?: string; // Original document date (ISO date string)
  so_ct_goc?: number; // Original document number
  dien_giai_ct_goc?: string; // Original document description

  // Payment and customer information
  ma_tt?: string; // UUID reference to HanThanhToan
  ma_tt_data?: HanThanhToan;
  ma_kh?: string; // UUID reference to KhachHang
  ma_kh_data?: KhachHang;

  // Totals (calculated fields - read-only)
  t_tien_nt?: string; // Total amount in foreign currency (decimal as string)
  t_tien?: string; // Total amount (decimal as string)

  // Related data
  child_data?: PhieuThuChiTiet[]; // Detail items
  child_items?: PhieuThuChiTiet[]; // Alternative field name for details

  // Timestamps
  created: string; // ISO datetime string
  updated: string; // ISO datetime string
}

export interface PhieuThuListResponse {
  count: number;
  next?: string;
  previous?: string;
  results: PhieuThu[];
}

// API Response types
export type PhieuThuApiResponse = ApiResponse<PhieuThu>;
export type PhieuThuListApiResponse = ApiResponse<PhieuThu[]>;

/**
 * Type for creating or updating a PhieuThu
 * Used for API requests (POST/PUT operations)
 */
export interface PhieuThuInput {
  /**
   * Employee code
   */
  ma_ngv?: string;

  /**
   * Address
   */
  dia_chi?: string;

  /**
   * Person name (Mr./Mrs.)
   */
  ong_ba?: string;

  /**
   * Description/explanation
   */
  dien_giai?: string;

  /**
   * Account code (required)
   */
  tk: string;

  /**
   * Unit ID (required)
   */
  unit_id: string;

  /**
   * Document series number
   */
  i_so_ct?: string;

  /**
   * Warehouse code
   */
  ma_nk?: string;

  /**
   * Document number
   */
  so_ct?: string;

  /**
   * Document date
   */
  ngay_ct?: string;

  /**
   * Last change date
   */
  ngay_lct?: string;

  /**
   * Currency code
   */
  ma_nt?: string;

  /**
   * Exchange rate
   */
  ty_gia?: string;

  /**
   * Status
   */
  status?: string;

  /**
   * Original document number
   */
  so_ct0?: string;

  /**
   * Original document date
   */
  ngay_ct0?: string;

  /**
   * Original document number (numeric)
   */
  so_ct_goc?: number;

  /**
   * Original document description
   */
  dien_giai_ct_goc?: string;

  /**
   * Payment status code
   */
  ma_tt?: string;

  /**
   * Customer code
   */
  ma_kh?: string;

  /**
   * Total amount in foreign currency
   */
  t_tien_nt?: string;

  /**
   * Total amount in VND
   */
  t_tien?: string;

  /**
   * Detail items for the receipt voucher
   */
  child_items?: PhieuThuChiTietInput[];
}

/**
 * Type for creating or updating a PhieuThuChiTiet
 * Used for API requests (POST/PUT operations)
 */
export interface PhieuThuChiTietInput {
  /**
   * Line number
   */
  line?: number;

  /**
   * Description/explanation
   */
  dien_giai?: string;

  /**
   * Customer code
   */
  ma_kh?: string;

  /**
   * Invoice ID reference
   */
  id_hd?: string;

  /**
   * Credit account
   */
  tk_co?: string;

  /**
   * Invoice exchange rate
   */
  ty_gia_hd?: string;

  /**
   * Secondary exchange rate
   */
  ty_gia2?: string;

  /**
   * Amount in foreign currency
   */
  tien_nt?: string;

  /**
   * Amount in VND
   */
  tien?: string;

  /**
   * Department code
   */
  ma_bp?: string;

  /**
   * Project code
   */
  ma_vv?: string;

  /**
   * Contract code
   */
  ma_hd?: string;

  /**
   * Cost object code
   */
  ma_dtt?: string;

  /**
   * Area code
   */
  ma_ku?: string;

  /**
   * Fee code
   */
  ma_phi?: string;

  /**
   * Product code
   */
  ma_sp?: string;

  /**
   * Production order code
   */
  ma_lsx?: string;

  /**
   * Cost center code
   */
  ma_cp0?: string;

  /**
   * Payment status ID
   */
  id_tt?: number;
}

// All types are exported above with correct naming convention
