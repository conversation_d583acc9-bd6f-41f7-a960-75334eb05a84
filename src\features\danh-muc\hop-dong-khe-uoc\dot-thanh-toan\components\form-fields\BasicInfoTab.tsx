'use client';

import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

interface BasicInfoTabProps {
  formMode: 'add' | 'edit' | 'view';
}

const BasicInfoTab: React.FC<BasicInfoTabProps> = ({ formMode }) => {
  const isViewMode = formMode === 'view';

  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã đợt thanh toán</Label>
          <FormField type='text' name='ma_dtt' className='w-64' disabled={isViewMode || formMode === 'edit'} />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tên đợt thanh toán</Label>
          <FormField type='text' name='ten_dtt' className='w-96' disabled={isViewMode} />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tên khác</Label>
          <FormField type='text' name='ten_dtt2' className='w-96' disabled={isViewMode} />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Số thứ tự</Label>
          <FormField type='number' name='stt' className='w-64' disabled={isViewMode} />
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Trạng thái</Label>
          <div className='w-64'>
            <FormField
              name='status'
              type='select'
              options={[
                { value: '1', label: '1. Còn sử dụng' },
                { value: '0', label: '0. Không sử dụng' }
              ]}
              defaultValue='1'
              disabled={isViewMode}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfoTab;
