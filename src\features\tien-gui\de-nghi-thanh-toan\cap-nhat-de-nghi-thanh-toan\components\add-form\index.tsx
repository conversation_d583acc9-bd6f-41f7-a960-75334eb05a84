import { useState, useEffect } from 'react';
import { BasicInfoTab, BottomBar, DetailItemTab, OtherTab } from './tabs';
import { exportPaymentRequestSchema } from '../../schemas';
import { AritoForm } from '@/components/custom/arito';
import { Bo<PERSON>han, ChungTu } from '@/types/schemas';
import { useNgoaiTe, useUser } from '@/hooks';
import { FormMode } from '@/types/form';

export const AddForm = ({
  formMode,
  currentObj,
  inputDetails,
  setInputDetails,
  handleFormSubmit,
  handleCloseForm,
  calculateTotals
}: {
  formMode: FormMode;
  currentObj: any;
  inputDetails: any[];
  setInputDetails: (details: any[]) => void;
  handleFormSubmit: (data: any) => void;
  handleCloseForm: () => void;
  calculateTotals: (details: any[]) => number;
}) => {
  const [selectedChungTu, setSelectedChungTu] = useState<ChungTu | null>(null);
  const [selectedBo<PERSON>han, setSelectedBoPhan] = useState<BoPhan | null>(null);
  const { currencies } = useNgoaiTe();
  const { users } = useUser();

  // Initialize SearchField states when currentObj changes (for view/edit mode)
  useEffect(() => {
    if ((formMode === 'view' || formMode === 'edit') && currentObj) {
      // Initialize BoPhan if we have the data
      if (currentObj.boPhanCode || currentObj.ma_bp) {
        const mockBoPhan: BoPhan = {
          uuid: currentObj.boPhanCode || currentObj.ma_bp || '',
          entity_model: '',
          ma_bp: currentObj.boPhanCode || currentObj.ma_bp || '',
          ten_bp: currentObj.boPhanName || currentObj.ten_bp || currentObj.boPhanCode || currentObj.ma_bp || '',
          status: '1',
          created_by: '',
          updated_by: '',
          created: '',
          updated: ''
        };
        setSelectedBoPhan(mockBoPhan);
      }

      if (currentObj.chungTuCode || currentObj.ma_ct) {
        const mockChungTu: ChungTu = {
          uuid: currentObj.chungTuCode || currentObj.ma_ct || '',
          entity_model: '',
          ma_ct: currentObj.chungTuCode || currentObj.ma_ct || '',
          ten_ct: currentObj.chungTuName || currentObj.ten_ct || currentObj.chungTuCode || currentObj.ma_ct || '',
          ngay_ks: '',
          stt: 0,
          i_so_ct: 0,
          d_page_count: 0,
          order_type: '0',
          df_status: '5',
          ct_kt_ton: '0',
          loai_dl_ton: '0',
          ct_sd_vi_tri: false,
          user_id0_yn: false,
          user_id2_yn: false,
          ngay_lct_yn: true,
          ct_save_log: '0',
          created: '',
          updated: ''
        };
        setSelectedChungTu(mockChungTu);
      }
    } else if (formMode === 'add') {
      // Clear states for add mode
      setSelectedBoPhan(null);
      setSelectedChungTu(null);
    }
  }, [formMode, currentObj]);

  // Prepare initial data with form mode included
  const initialData = {
    ...currentObj,
    formMode: formMode
  };

  // Handle form submission with input details
  const handleFormSubmitWithDetails = (formData: any) => {
    // Include input details in the form data
    const dataWithDetails = {
      ...formData,
      inputDetails: inputDetails
    };
    handleFormSubmit(dataWithDetails);
  };

  return (
    <div className='h-full flex-1 overflow-auto'>
      <AritoForm
        mode={formMode}
        initialData={initialData}
        onSubmit={handleFormSubmitWithDetails}
        onClose={handleCloseForm}
        schema={exportPaymentRequestSchema}
        subTitle='Đề nghị thanh toán'
        hasAritoActionBar={true}
        headerFields={
          <BasicInfoTab
            formMode={formMode}
            currencies={currencies}
            users={users}
            selectedBoPhan={selectedBoPhan}
            setSelectedBoPhan={setSelectedBoPhan}
            selectedChungTu={selectedChungTu}
            setSelectedChungTu={setSelectedChungTu}
          />
        }
        tabs={[
          {
            id: 'details',
            label: 'Chi tiết',
            component: <DetailItemTab value={inputDetails} onChange={setInputDetails} formMode={formMode} />
          },
          {
            id: 'other',
            label: 'Khác',
            component: <OtherTab value={inputDetails} onChange={setInputDetails} formMode={formMode} />
          }
        ]}
        bottomBar={<BottomBar totalMoney={calculateTotals(inputDetails)} totalPayment={0} formMode={formMode} />}
      />
    </div>
  );
};
