import type { ApiResponse } from '../api.type';

export interface GiayBaoNo {
  uuid?: string | null;
  entity_model?: any | null;
  ma_ngv?: string | null;
  dia_chi?: string | null;
  ong_ba?: string | null;
  dien_giai?: string | null;
  tk?: any | null;
  i_so_ct?: string | null;
  ngay_ct?: string | null;
  ngay_lct?: string | null;
  ty_gia?: number | null;
  status?: string | null;
  transfer_yn?: boolean | null;
  hd_yn?: boolean | null;
  tg_dd?: boolean | null;
  cltg_yn?: boolean | null;
  ngay_ct0?: string | null;
  lenh_ct_yn?: string | null;
  ten_kh?: string | null;
  loai_lenh?: string | null;
  stk_kh?: string | null;
  ten_nh?: string | null;
  chi_nhanh_nh?: string | null;
  tinh_thanh_nh?: string | null;
  dien_giai_nh?: string | null;
  ma_lenh_nh?: string | null;
  tt_lenh_nh?: string | null;
  phi_nhan_yn?: boolean | null;
  dien_giai_ct_goc?: string | null;
  t_tien_nt?: number | null;
  t_tien?: number | null;
  t_cp_nt?: number | null;
  t_cp?: number | null;
  t_thue_nt?: number | null;
  t_thue?: number | null;
  chi_tiet?: ChiTietGiayBaoNo[] | null;
  phi_ngan_hang?: PhiNganHang[] | null;
  thue?: ThueGiayBaoNo[] | null;
}

interface ChiTietGiayBaoNo {
  line?: number | null;
  dien_giai?: string | null;
  id_hd?: string | null;
  ty_gia2?: number | null;
  tien_nt?: number | null;
  tien?: number | null;
  ma_loai_hd?: string | null;
  ten_thue?: string | null;
  thue_suat?: number | null;
  ten_tk_thue?: string | null;
  so_ct2?: string | null;
  ngay_ct0?: string | null;
  ma_mau_ct?: string | null;
  ma_mau_bc?: string | null;
  ma_tc_thue?: string | null;
  ma_kh_thue?: string | null;
  ten_kh_thue?: string | null;
  dia_chi?: string | null;
  ma_so_thue?: string | null;
  ten_vt_thue?: string | null;
  thue_nt?: number | null;
  thue?: number | null;
  ten_kh9?: string | null;
  id_dn?: number | null;
  line_dn?: number | null;
  ghi_chu?: string | null;
  id_tt?: number | null;
}

interface PhiNganHang {
  line?: number | null;
  tien_cp_nt?: number | null;
  ngay_ct0?: string | null;
  dien_giai?: string | null;
  ten_tk_cpnh?: string | null;
  ten_tk_du?: string | null;
  thue_suat?: number | null;
  t_thue_nt?: number | null;
  tien_cp?: number | null;
  t_thue?: number | null;
}

interface ThueGiayBaoNo {
  line?: number | null;
  ngay_ct0?: string | null;
  thue_suat?: number | null;
  ma_mau_ct?: string | null;
  ma_mau_bc?: string | null;
  ma_tc_thue?: string | null;
  ten_kh_thue?: string | null;
  dia_chi?: string | null;
  ma_so_thue?: string | null;
  ten_vt_thue?: string | null;
  t_tien_nt?: number | null;
  t_tien?: number | null;
  ten_tk_thue_no?: string | null;
  ten_tk_du?: string | null;
  t_thue_nt?: number | null;
  t_thue?: number | null;
  ten_kh9?: string | null;
  ten_tt?: string | null;
  ghi_chu?: string | null;
  id_tt?: number | null;
}

export type GiayBaoNoResponse = ApiResponse<GiayBaoNo>;

export type GiayBaoNoInput = Omit<GiayBaoNo, 'uuid' | 'entity_model'>;
