import React from 'react';
import { AritoDialog } from '@/components/custom/arito';
import { TaxRateFormattedData } from '../../schemas';
import { Button } from '@/components/ui/button';

interface DeleteDialogProps {
  open: boolean;
  onClose: () => void;
  selectedObj: TaxRateFormattedData | null;
  deleteObj: () => Promise<void>;
  clearSelection: () => void;
}

export const DeleteDialog: React.FC<DeleteDialogProps> = ({
  open,
  onClose,
  selectedObj,
  deleteObj,
  clearSelection
}) => {
  const [isDeleting, setIsDeleting] = React.useState(false);

  const handleDelete = async () => {
    if (!selectedObj) return;

    setIsDeleting(true);
    try {
      await deleteObj();
      clearSelection();
      onClose();
    } catch (error) {
      console.error('Error deleting tax rate:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Xác nhận xóa'
      maxWidth='sm'
      disableBackdropClose={isDeleting}
      disableEscapeKeyDown={isDeleting}
    >
      <div className='p-4'>
        <p className='mb-4'>
          Bạn có chắc chắn muốn xóa thuế suất <strong>{selectedObj?.ten_thue}</strong> không?
        </p>
        <div className='flex justify-end gap-2'>
          <Button variant='outline' onClick={onClose} disabled={isDeleting}>
            Hủy
          </Button>
          <Button variant='destructive' onClick={handleDelete} disabled={isDeleting}>
            {isDeleting ? 'Đang xóa...' : 'Xóa'}
          </Button>
        </div>
      </div>
    </AritoDialog>
  );
};
