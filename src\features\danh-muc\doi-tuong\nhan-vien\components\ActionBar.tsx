import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Plus, Refresh<PERSON><PERSON>, Trash, Pin } from 'lucide-react';
import Link from 'next/link';
import { AritoActionButton } from '@/components/custom/arito/action-button';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import { AritoIcon } from '@/components/custom/arito';

interface ActionBarProps {
  onAdd?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onCopy?: () => void;
  onPrint?: () => void;
  onSearch?: () => void;
  isEditDisabled?: boolean;
  className?: string;
}

export default function ActionBar({
  className,
  onAdd,
  onEdit,
  onDelete,
  onCopy,
  onPrint,
  onSearch,
  isEditDisabled = false
}: ActionBarProps) {
  return (
    <AritoActionBar
      titleComponent={
        <div>
          <h1 className='text-xl font-bold'><PERSON><PERSON> mục nhân viên</h1>
        </div>
      }
    >
      <AritoActionButton title='Thêm' icon={Plus} onClick={onAdd} />
      <AritoActionButton title='Sửa' icon={Pencil} onClick={onEdit} disabled={isEditDisabled} />
      <AritoActionButton title='Xóa' icon={Trash} onClick={onDelete} disabled={isEditDisabled} />
      <AritoActionButton title='Sao chép' icon={Copy} onClick={onCopy} disabled={isEditDisabled} />
      <AritoActionButton title='Refresh' icon={RefreshCw} onClick={() => {}} disabled={isEditDisabled} />
      <AritoActionButton title='Cố định cột' icon={Pin} onClick={() => {}} variant='secondary' />
      <AritoMenuButton
        items={[
          {
            title: 'Kết xuất dữ liệu',
            icon: <AritoIcon icon={555} />,
            onClick: () => {},
            group: 1
          },
          {
            title: 'Tải mẫu Excel',
            icon: <AritoIcon icon={28} />,
            onClick: () => {},
            group: 2
          },
          {
            title: 'Lấy dữ liệu từ Excel',
            icon: <AritoIcon icon={29} />,
            onClick: () => {},
            group: 2
          }
        ]}
      />
    </AritoActionBar>
  );
}
