'use client';
import { useState } from 'react';
import { AritoDataTables, DeleteDialog, LoadingOverlay } from '@/components/custom/arito';
import { useDieuChinhGiaTriTSCD, useFormState, useRows } from '@/hooks';
import DownloadDataDialog from './components/download-data-dialog';
import { Actionbar, FormDialog, SearchDialog } from './components';
import { DieuChinhGiaTriTSCDInput } from '@/types/schemas';
import { getDataTableColumns } from './cols-definition';

export default function DieuChinhGiaTriTSCD() {
  const [showSearchDialog, setShowSearchDialog] = useState(true);
  const [showTable, setShowTable] = useState(false);
  const [showDownloadDialog, setShowDownloadDialog] = useState(false);

  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRows();
  const {
    showForm,
    showDelete,
    formMode,
    isCopyMode,
    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleDeleteClick,
    handleCopyClick
  } = useFormState();

  const {
    dieuChinhGiaTriTSCDs,
    isLoading,
    refreshDieuChinhGiaTriTSCDs,
    deleteDieuChinhGiaTriTSCD,
    addDieuChinhGiaTriTSCD,
    updateDieuChinhGiaTriTSCD
  } = useDieuChinhGiaTriTSCD();

  const handleSearch = () => {
    setShowSearchDialog(false);
    setShowTable(true);
  };

  const handleFormSubmit = async (data: DieuChinhGiaTriTSCDInput) => {
    try {
      if (formMode === 'add' && isCopyMode) {
        await addDieuChinhGiaTriTSCD(data);
      } else if (formMode === 'edit') {
        await updateDieuChinhGiaTriTSCD(selectedObj.uuid, data);
      } else if (formMode === 'add') {
        await addDieuChinhGiaTriTSCD(data);
      }
      await refreshDieuChinhGiaTriTSCDs();
      handleCloseForm();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  const tables = [
    {
      name: '',
      rows: dieuChinhGiaTriTSCDs,
      columns: getDataTableColumns()
    }
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {showSearchDialog && (
        <SearchDialog open={showSearchDialog} onClose={() => setShowSearchDialog(false)} onSearch={handleSearch} />
      )}

      {showDownloadDialog && (
        <DownloadDataDialog onClose={() => setShowDownloadDialog(false)} open={showDownloadDialog} />
      )}

      {showTable && (
        <div className='w-full'>
          <Actionbar
            onAddIconClick={handleAddClick}
            onEditIconClick={handleEditClick}
            onDeleteIconClick={handleDeleteClick}
            onCopyIconClick={handleCopyClick}
            onWatchIconClick={() => setShowSearchDialog(true)}
            onDownloadDataIconClick={() => setShowDownloadDialog(true)}
          />
          {isLoading && <LoadingOverlay />}
          {!isLoading && (
            <AritoDataTables
              tables={tables}
              selectedRowId={selectedRowIndex || undefined}
              onRowClick={handleRowClick}
            />
          )}
        </div>
      )}

      {showForm && (
        <FormDialog
          open={showForm}
          onClose={handleCloseForm}
          onSubmit={handleFormSubmit}
          mode={formMode}
          initialData={
            selectedObj && (formMode === 'edit' || formMode === 'view' || (formMode === 'add' && isCopyMode))
              ? selectedObj
              : formMode === 'add' && !isCopyMode
                ? undefined
                : undefined
          }
        />
      )}

      {showDelete && (
        <DeleteDialog
          open={showDelete}
          onClose={handleCloseDelete}
          selectedObj={selectedObj}
          deleteObj={deleteDieuChinhGiaTriTSCD}
          clearSelection={clearSelection}
        />
      )}
    </div>
  );
}
