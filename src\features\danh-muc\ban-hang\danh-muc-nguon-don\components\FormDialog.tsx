import { useState } from 'react';
import { AritoDialog, AritoForm, AritoIcon, BottomBar } from '@/components/custom/arito';
import ConfirmationDialog from '@/components/custom/arito/dialog/confirm-dialog';
import { formSchema, initialValues } from '../schemas';
import { FormMode } from '@/types/form';
import BasicInfo from './BasicInfo';

interface FormDialogProps {
  mode: FormMode;
  open: boolean;
  onClose: () => void;
  onSubmit?: (data: any) => void;
  onAddButtonClick?: () => void;
  onEditButtonClick?: () => void;
  onDeleteButtonClick?: () => void;
  onCopyButtonClick?: () => void;
  onWatchButtonClick?: () => void;

  initialData: any;
}
function FormDialog({
  mode,
  open,
  onClose,
  onAddButtonClick,
  onCopyButtonClick,
  onDeleteButtonClick,
  onEditButtonClick,
  initialData,
  onSubmit
}: FormDialogProps) {
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  const handleSubmit = async (data: any) => {
    onSubmit?.(data);
  };

  const handleCloseDialog = () => {
    setShowConfirmDialog(false);
    onClose();
  };

  const handleClose = () => {
    setShowConfirmDialog(true);
    onClose();
  };
  return (
    <>
      <AritoDialog
        open={open}
        onClose={handleClose}
        title={mode === 'add' ? 'Mới' : mode === 'edit' ? 'Sửa' : 'Xem'}
        maxWidth='lg'
        disableBackdropClose={true}
        disableEscapeKeyDown={true}
        titleIcon={<AritoIcon icon={281} />}
      >
        <AritoForm
          mode={mode}
          hasAritoActionBar={false}
          schema={formSchema}
          onSubmit={handleSubmit}
          initialData={initialData || initialValues}
          className='w-full md:min-w-[500px] lg:min-w-[600px]'
          headerFields={<BasicInfo formMode={mode} />}
          classNameBottomBar='relative w-full flex justify-end gap-2'
          bottomBar={
            <BottomBar
              mode={mode}
              onAdd={onAddButtonClick}
              onEdit={onEditButtonClick}
              onDelete={onDeleteButtonClick}
              onCopy={onCopyButtonClick}
              onClose={() => setShowConfirmDialog(true)}
            />
          }
        />
      </AritoDialog>

      <ConfirmationDialog
        open={showConfirmDialog}
        onClose={() => setShowConfirmDialog(false)}
        onConfirm={handleCloseDialog}
        title='Cảnh báo'
        message='Bạn muốn kết thúc?'
      />
    </>
  );
}

export default FormDialog;
