'use client';

import { useState, useEffect } from 'react';
import { AdditionalInfoTab, BasicInfoTab, GeneralTab, OtherTab, HDDTTab } from './tabs';
import { AritoForm, AritoHeaderTabs } from '@/components/custom/arito';
import { getFormTitle, getFormActionButtons } from '../../utils';
import { FormSchema, initialFormValues } from '../../schema';
import ConfirmDialog from '../ConfirmDialog';
import { useFormState } from '../../hooks';
import { FormMode } from '@/types/form';

interface FormDialogProps {
  formMode: FormMode;
  open: boolean;
  initialData?: any;
  onSubmit?: (data: any) => void;
  onClose: () => void;
  onAdd?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onCopy?: () => void;
}

const FormDialog = ({
  open,
  onClose,
  onSubmit,
  formMode,
  initialData,
  onAdd,
  onEdit,
  onDelete,
  onCopy
}: FormDialogProps) => {
  const [activeTab, setActiveTab] = useState<string>('info');
  const [isConfirm, setIsConfirm] = useState<boolean>(false);
  const [isFormDirty, setIsFormDirty] = useState<boolean>(false);
  const { state, actions } = useFormState(initialData);

  useEffect(() => {
    if (!open) {
      setIsFormDirty(false);
    }
  }, [open]);

  const handleSubmit = (data: any) => {
    console.log('📝 FormDialog handleSubmit called:', {
      formData: data,
      searchFieldState: {
        nhanVien: state.nhanVien?.uuid,
        taiKhoanNgamDinh: state.taiKhoanNgamDinh?.uuid,
        thanhToan: state.thanhToan?.uuid,
        phuongThucThanhToan: state.phuongThucThanhToan?.uuid,
        nhom1: state.nhom1?.uuid,
        nhom2: state.nhom2?.uuid,
        nhom3: state.nhom3?.uuid,
        khuVuc: state.khuVuc?.uuid
      },
      timestamp: new Date().toISOString()
    });

    const submitData = {
      ...data,
      sales_rep: state.nhanVien?.uuid,
      account: state.taiKhoanNgamDinh?.uuid,
      payment_term: state.thanhToan?.uuid,
      payment_method: state.phuongThucThanhToan?.uuid,
      customer_group1: state.nhom1?.uuid,
      customer_group2: state.nhom2?.uuid,
      customer_group3: state.nhom3?.uuid,
      region: state.khuVuc?.uuid
    };

    console.log('📤 Final submit data:', submitData);

    onSubmit?.(submitData);
    setIsFormDirty(false);
    onClose();
  };

  const handleClose = () => {
    if (isFormDirty && formMode !== 'view') {
      setIsConfirm(true);
    } else {
      onClose();
    }
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  const title = getFormTitle(formMode, activeTab, initialData?.customer_code);
  const actionButtons = getFormActionButtons(formMode, activeTab, {
    onAdd: onAdd,
    onEdit: onEdit,
    onDelete: onDelete,
    onCopy: onCopy,
    handleClose: handleClose
  });

  return (
    <>
      <AritoForm
        mode={formMode}
        title={title}
        actionButtons={actionButtons}
        subTitle='Danh mục nhà cung cấp'
        schema={FormSchema}
        onSubmit={handleSubmit}
        onClose={handleClose}
        initialData={initialData || initialFormValues}
        className='w-full'
        headerFields={
          <div onChange={() => setIsFormDirty(true)}>
            <AritoHeaderTabs
              tabs={[
                {
                  id: 'info',
                  label: 'Thông tin cơ bản',
                  component: <BasicInfoTab formMode={formMode} />
                }
              ]}
              onTabChange={handleTabChange}
              defaultTabIndex={activeTab === 'info' ? 0 : activeTab === 'price' ? 1 : activeTab === 'review' ? 2 : 0}
            />
          </div>
        }
        tabs={
          <AritoHeaderTabs
            tabs={[
              {
                id: 'basic-info',
                label: 'Thông tin cơ bản',
                component: <GeneralTab formMode={formMode} formState={{ state, actions }} />
              },
              {
                id: 'other',
                label: 'Khác',
                component: <OtherTab formMode={formMode} />
              },
              {
                id: 'additional-info',
                label: 'Thông tin thêm',
                component: <AdditionalInfoTab formMode={formMode} />
              },
              {
                id: 'hddt',
                label: 'HDDT',
                component: <HDDTTab formMode={formMode} />
              }
            ]}
          />
        }
      />

      {isConfirm && (
        <ConfirmDialog
          open={isConfirm}
          onClose={() => setIsConfirm(false)}
          onConfirm={() => {
            setIsConfirm(false);
            setIsFormDirty(false);
            onClose();
          }}
          title='Cảnh báo'
          content='Bạn muốn kết thúc?'
        />
      )}
    </>
  );
};

export default FormDialog;
