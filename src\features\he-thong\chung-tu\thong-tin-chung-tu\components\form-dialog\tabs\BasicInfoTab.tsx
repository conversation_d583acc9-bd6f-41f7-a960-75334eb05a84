'use client';

import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface BasicInfoTabProps {
  formMode: FormMode;
}

export const BasicInfoTab: React.FC<BasicInfoTabProps> = ({ formMode }) => {
  const isReadOnly = formMode !== 'edit';

  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>M<PERSON> chứng từ</Label>
          <div className='w-[250px]'>
            <FormField type='text' name='ma_ct' disabled={isReadOnly} />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tê<PERSON> chứng từ</Label>
          <div className='w-full'>
            <FormField type='text' name='ten_ct' disabled={isReadOnly} />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tên 2</Label>
          <div className='w-full'>
            <FormField type='text' name='ten_ct2' disabled={isReadOnly} />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Ngày khoá sổ</Label>
          <div className='w-[250px]'>
            <FormField type='date' name='ngay_ks' disabled={isReadOnly} />
          </div>
        </div>
      </div>
    </div>
  );
};
