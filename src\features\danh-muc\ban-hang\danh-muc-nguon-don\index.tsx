'use client';

import ConfirmationDialog from '@/components/custom/arito/dialog/confirm-dialog';
import { AritoDataTables, LoadingOverlay } from '@/components/custom/arito';
import { NguonDonInput, NguonDon } from '@/types/schemas';
import { getDataTableColumns } from './cols-definition';
import { ActionBar, FormDialog } from './components';
import { useCRUD, useFormState } from '@/hooks';
import { QUERY_KEYS } from '@/constants';

export default function DanhMucNguonDon() {
  const { addItem, updateItem, deleteItem, refreshData, isLoading, data } = useCRUD<NguonDon, NguonDonInput>({
    endpoint: QUERY_KEYS.DANH_MUC_NGUON_DON
  });

  const {
    showForm,
    showDelete,
    selectedObj,
    selectedRowIndex,
    formMode: mode,
    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick,
    handleRowClick,
    clearSelection
  } = useFormState<NguonDon>();

  const handleSubmit = async (data: NguonDonInput) => {
    try {
      if (mode === 'add') {
        await addItem(data);
      } else if (mode === 'edit' && selectedObj) {
        await updateItem(selectedObj.uuid, data);
      }
      handleCloseForm();
      clearSelection();
    } catch (e) {
      () => {};
    }
  };

  const tables = [
    {
      name: '',
      rows: data,
      columns: getDataTableColumns()
    }
  ];

  return (
    <div className='flex h-screen flex-col lg:overflow-hidden'>
      {showForm && (
        <FormDialog
          mode={mode}
          open={showForm}
          onClose={handleCloseForm}
          onAddButtonClick={handleAddClick}
          onEditButtonClick={handleEditClick}
          onCopyButtonClick={handleCopyClick}
          onWatchButtonClick={handleViewClick}
          onDeleteButtonClick={handleDeleteClick}
          onSubmit={handleSubmit}
          initialData={mode === 'add' && !isCopyMode ? undefined : selectedObj}
        />
      )}

      <div className='w-full'>
        <ActionBar
          onAddClick={handleAddClick}
          onEditClick={handleEditClick}
          onDeleteClick={handleDeleteClick}
          onCopyClick={handleCopyClick}
          onViewClick={handleViewClick}
          onRefreshClick={refreshData}
        />
        {isLoading && <LoadingOverlay />}
        {!isLoading && (
          <AritoDataTables tables={tables} selectedRowId={selectedRowIndex || undefined} onRowClick={handleRowClick} />
        )}
      </div>

      {showDelete && (
        <ConfirmationDialog
          onClose={handleCloseDelete}
          onConfirm={() => {
            deleteItem(selectedObj!.uuid);
            handleCloseDelete();
            clearSelection();
            handleCloseForm();
          }}
        />
      )}
    </div>
  );
}
