import { But<PERSON> } from '@mui/material';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoIcon } from '@/components/custom/arito';

interface ConfirmDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title?: string;
  content?: string;
}

export const ConfirmDialog = ({
  open,
  onClose,
  onConfirm,
  title = 'Cảnh báo',
  content = 'Bạn muốn kết thúc?'
}: ConfirmDialogProps) => {
  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title={title}
      maxWidth='sm'
      disableBackdropClose={false}
      disableEscapeKeyDown={true}
      showFullscreenToggle={false}
      titleIcon={<AritoIcon icon={260} />}
      actions={
        <>
          <Button
            onClick={onConfirm}
            type='submit'
            variant='contained'
            className='bg-[rgba(15,118,110,0.9)] normal-case hover:bg-[rgba(15,118,110,1)]'
          >
            <AritoIcon icon={884} className='mr-2' />
            <PERSON><PERSON>ng ý
          </Button>
          <Button onClick={onClose} variant='outlined'>
            <AritoIcon icon={885} className='mr-2' />
            Huỷ
          </Button>
        </>
      }
    >
      <p className='min-w-[40vw] p-4 text-base font-medium'>{content}</p>
    </AritoDialog>
  );
};
