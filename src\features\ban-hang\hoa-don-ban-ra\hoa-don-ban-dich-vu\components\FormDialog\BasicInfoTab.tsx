import { useFormContext } from 'react-hook-form';
import React, { useEffect } from 'react';
import {
  khachHangSearchColumns,
  MA_CHUNG_TU,
  nhanVienSearchColumns,
  QUERY_KEYS,
  quyenChungTuSearchColumns,
  taiKhoanSearchColumns
} from '@/constants';
import { SearchField, FormField, AritoHeaderTabs, TabItem } from '@/components/custom/arito';
import { CurrencyField } from '@/components/custom/arito/form/form-field/components';
import { KhachHang, NhanVien, QuyenChungTu, TaiKhoan } from '@/types/schemas';
import { useHanThanhToan, useQuyenChungTuByChungTu } from '@/hooks';
import { FormFieldState, FormFieldActions } from '../../hooks';
import { generateSoChungTuHienTai } from '@/lib/stringUtil';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface BasicInformationTabProps {
  formMode: FormMode;
  formState: {
    state: FormFieldState;
    actions: FormFieldActions;
  };
}

const InfoTab = ({ formMode, formState: { state, actions } }: BasicInformationTabProps) => {
  const { paymentTerms, isLoading } = useHanThanhToan();
  const { watch } = useFormContext();

  const ngayCt = watch('ngay_ct');

  const { quyenChungTus } = useQuyenChungTuByChungTu({
    ma_ct: MA_CHUNG_TU.BAN_HANG.HOA_DON_BAN_DICH_VU,
    ngay_hl: ngayCt || new Date().toISOString().split('T')[0]
  });

  useEffect(() => {
    if (state.quyenChungTu) {
      actions.setSoChungTuHienTai(
        generateSoChungTuHienTai(state.quyenChungTu.i_so_ct_ht, state.quyenChungTu.so_ct_mau, new Date())
      );
    }
  }, [state.quyenChungTu, actions]);

  const handleCheckbox = (checked: boolean) => {
    actions.setPtTaoYn(checked);
  };

  return (
    <div className='flex flex-col gap-3 p-4'>
      <div className='grid grid-cols-12 gap-6'>
        <div className='col-span-6 space-y-1'>
          <div className='flex items-center gap-2'>
            <div className='flex items-center'>
              <Label className='w-32 min-w-32 text-sm font-medium'>Mã khách hàng</Label>
              <SearchField<KhachHang>
                type='text'
                columnDisplay='customer_code'
                searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}`}
                searchColumns={khachHangSearchColumns}
                value={state.khachHang?.customer_code || ''}
                onRowSelection={actions.setKhachHang}
                disabled={formMode === 'view'}
                dialogTitle='Danh mục khách hàng'
              />
            </div>

            <div className='flex items-center'>
              <Label className='w-24 min-w-24 text-sm font-medium'>Mã số thuế</Label>
              <FormField
                name='ma_so_thue'
                type='text'
                disabled={formMode === 'view'}
                placeholder='Nhập và tra cứu'
                value={state.khachHang?.tax_code || ''}
              />
            </div>

            <div className='ml-4 flex items-center space-x-2'>
              <Checkbox
                id='pt_tao_yn'
                name='pt_tao_yn'
                checked={state.pt_tao_yn}
                onCheckedChange={handleCheckbox}
                disabled={formMode === 'view'}
              />

              {!state.pt_tao_yn && <Label>Thu tiền</Label>}
              {state.pt_tao_yn && (
                <FormField
                  name='ma_httt'
                  type='select'
                  className='w-36'
                  options={[
                    { value: 'tien-mat', label: 'Tiền mặt' },
                    { value: 'chuyen-khoan', label: 'Chuyển khoản' },
                    { value: 'khac', label: 'Khác' }
                  ]}
                  disabled={formMode === 'view'}
                />
              )}
            </div>
          </div>

          <div className='flex items-center'>
            <Label className='w-32 min-w-32 text-sm font-medium'>Tên khách hàng</Label>
            <div className='flex-1'>
              <FormField
                name='ten_kh_thue'
                type='text'
                disabled={formMode === 'view'}
                placeholder='Nhập tên khách hàng/đơn vị'
                value={state.khachHang?.customer_name || ''}
              />
            </div>
          </div>

          <div className='flex items-center'>
            <Label className='w-32 min-w-32 text-sm font-medium'>Địa chỉ</Label>
            <div className='flex-1'>
              <FormField
                name='dia_chi'
                type='text'
                disabled={formMode === 'view'}
                placeholder='Nhập địa chỉ khách hàng'
                value={state.khachHang?.address || ''}
              />
            </div>
          </div>

          <div className='flex items-center'>
            <Label className='w-32 min-w-32 text-sm font-medium'>Mã nhân viên</Label>
            <SearchField<NhanVien>
              type='text'
              displayRelatedField='ma_nhan_vien'
              columnDisplay='ma_nhan_vien'
              searchEndpoint={`/${QUERY_KEYS.NHAN_VIEN}`}
              searchColumns={nhanVienSearchColumns}
              value={state.nhanVien?.ma_nhan_vien || state.khachHang?.sales_rep_data?.ma_nhan_vien || ''}
              relatedFieldValue={
                state.nhanVien?.ho_ten_nhan_vien || state.khachHang?.sales_rep_data?.ho_ten_nhan_vien || ''
              }
              onRowSelection={actions.setNhanVien}
              disabled={formMode === 'view'}
              dialogTitle='Danh mục nhân viên bán hàng'
            />
          </div>

          <div className='flex items-center'>
            <Label className='w-32 min-w-32 text-sm font-medium'>Tài khoản nợ</Label>
            <SearchField<TaiKhoan>
              type='text'
              displayRelatedField='code'
              columnDisplay='code'
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
              searchColumns={taiKhoanSearchColumns}
              value={state.taiKhoan?.code || state.khachHang?.account_data?.code || ''}
              relatedFieldValue={state.taiKhoan?.name || state.khachHang?.account_data?.name || ''}
              onRowSelection={actions.setTaiKhoan}
              disabled={formMode === 'view'}
              dialogTitle='Danh mục tài khoản'
            />
          </div>

          <div className='flex items-center'>
            <Label className='w-32 min-w-32 text-sm font-medium'>Diễn giải</Label>
            <div className='flex-1'>
              <FormField
                name='dien_giai'
                type='text'
                disabled={formMode === 'view'}
                value={state.khachHang?.description || ''}
              />
            </div>
          </div>
        </div>

        <div className='col-span-3 space-y-1'>
          <div className='ml-4 flex h-8 items-center'></div>

          <div className='flex items-center gap-3'>
            <Label className='w-32 min-w-32 text-sm font-medium'>Dư công nợ</Label>
            <Label className='font-medium text-red-500'>0</Label>
          </div>

          <div className='flex items-center'>
            <Label className='w-32 min-w-32 text-sm font-medium'>Người mua hàng</Label>
            <div className='flex-1'>
              <FormField
                name='ong_ba'
                type='text'
                disabled={formMode === 'view'}
                placeholder='Người mua hàng'
                value={state.khachHang?.contact_person || ''}
              />
            </div>
          </div>

          <div className='flex items-center'>
            <Label className='w-32 min-w-32 text-sm font-medium'>Email</Label>
            <div className='flex-1'>
              <FormField
                name='e_mail'
                type='text'
                disabled={formMode === 'view'}
                placeholder='Nhập email'
                value={state.khachHang?.email || ''}
              />
            </div>
          </div>

          {!state.pt_tao_yn && (
            <div className='flex items-center'>
              <Label className='w-32 min-w-32 text-sm font-medium'>Hạn thanh toán</Label>
              <div className='flex-1'>
                {!isLoading && (
                  <FormField
                    name='ma_tt'
                    type='select'
                    options={paymentTerms.map(term => ({ value: term.uuid, label: term.ten_tt }))}
                    disabled={formMode === 'view'}
                  />
                )}
              </div>
            </div>
          )}
        </div>

        <div className='col-span-3 space-y-1'>
          <div className='flex items-center'>
            <Label className='w-32 min-w-32 text-sm font-medium'>Số chứng từ</Label>
            <div className='flex-1'>
              <SearchField<QuyenChungTu>
                name='so_ct'
                type='text'
                disabled={formMode === 'view'}
                placeholder='Nhập số chứng từ'
                searchEndpoint={`/${QUERY_KEYS.QUYEN_CHUNG_TU}`}
                searchColumns={quyenChungTuSearchColumns}
                value={state.soChungTuHienTai}
                onValueChange={e => actions.setSoChungTuHienTai(e.target.value)}
                rows={quyenChungTus}
                onRowSelection={actions.setQuyenChungTu}
              />
            </div>
          </div>

          <div className='flex items-center'>
            <Label className='w-32 min-w-32 text-sm font-medium'>Ngày chứng từ</Label>
            <div className='flex-1'>
              <FormField name='ngay_ct' type='date' disabled={formMode === 'view'} />
            </div>
          </div>

          <CurrencyField name='ma_nt' disabled={formMode === 'view'} labelClassName='w-32' />

          <div className='flex items-center'>
            <Label className='w-32 min-w-32 text-sm font-medium'>Trạng thái</Label>
            <div className='flex-1'>
              <FormField
                name='status'
                type='select'
                disabled={formMode === 'view'}
                options={[
                  { value: '0', label: 'Chưa ghi sổ' },
                  { value: '1', label: 'Chờ duyệt' },
                  { value: '2', label: 'Đã ghi sổ' },
                  { value: '3', label: 'Hủy' }
                ]}
              />
            </div>
          </div>

          <div className='flex items-center'>
            <Label className='w-32 min-w-32 text-sm font-medium'></Label>
            <Checkbox id='recordedData' disabled={true} />
            <Label className='ml-2 text-sm font-medium' htmlFor='recordedData'>
              Dữ liệu được ghi nhận
            </Label>
          </div>
        </div>
      </div>
    </div>
  );
};

export default function BasicInfoTab({ formMode, formState }: BasicInformationTabProps) {
  const tabs: TabItem[] = [
    {
      id: 'info',
      label: 'Thông tin',
      component: <InfoTab formMode={formMode} formState={formState} />
    }
  ];

  return <AritoHeaderTabs tabs={tabs} />;
}
