import { z } from 'zod';

export const formSchema = z.object({
  ma_dcnh: z.string().nonempty('Mã địa chỉ là bắt buộc'),
  ten_dcnh: z.string().nonempty('Tên địa chỉ là bắt buộc'),
  ten_dcnh2: z.string().optional(),
  status: z.string().optional()
});

export type FormValues = z.infer<typeof formSchema>;

export const initialValues: FormValues = {
  ma_dcnh: '',
  ten_dcnh: '',
  ten_dcnh2: '',
  status: '1'
};
