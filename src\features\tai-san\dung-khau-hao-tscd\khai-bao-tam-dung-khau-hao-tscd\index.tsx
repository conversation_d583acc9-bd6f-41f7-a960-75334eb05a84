'use client';

import ConfirmationDialog from '@/components/custom/arito/dialog/confirm-dialog';
import { AritoDataTables, LoadingOverlay } from '@/components/custom/arito';
import { useFormState, useKhaiBaoTamDungKhauHaoTSCD } from '@/hooks';
import { KhaiBaoTamDungKhauHaoTSCDInput } from '@/types/schemas';
import { StopDepreciationColumns } from './cols-definition';
import { FormDialog, ActionBar } from './components';
import { initialValues } from './schema';

export default function KhaiBaoTamDungKhauHaoTSCD() {
  const {
    showForm,
    formMode,
    isCopyMode,
    showDelete,
    selectedObj,
    selectedRowIndex,

    handleCloseForm,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick,
    handleCloseDelete,
    handleRowClick,
    clearSelection
  } = useFormState();

  const {
    khaiBaoTamDungKhauHaoTSCDs,
    isLoading,
    addKhaiBaoTamDungKhauHaoTSCD,
    updateKhaiBaoTamDungKhauHaoTSCD,
    deleteKhaiBaoTamDungKhauHaoTSCD,
    refreshKhaiBaoTamDungKhauHaoTSCDs
  } = useKhaiBaoTamDungKhauHaoTSCD();

  const tables = [
    {
      name: 'Tất cả',
      rows: khaiBaoTamDungKhauHaoTSCDs || [],
      columns: StopDepreciationColumns
    }
  ];

  const handleFormSubmit = async (data: KhaiBaoTamDungKhauHaoTSCDInput) => {
    if (formMode === 'add') {
      await addKhaiBaoTamDungKhauHaoTSCD(data);
    } else if (formMode === 'edit' && selectedObj) {
      await updateKhaiBaoTamDungKhauHaoTSCD(selectedObj.uuid, data);
    }
    handleCloseForm();
    clearSelection();
  };

  return (
    <div className='flex h-screen flex-col lg:overflow-hidden'>
      {showForm && (
        <FormDialog
          open={showForm}
          onClose={handleCloseForm}
          onSubmit={handleFormSubmit}
          onAdd={handleAddClick}
          onEdit={handleEditClick}
          onDelete={handleDeleteClick}
          onCopy={handleCopyClick}
          formMode={formMode}
          initialData={formMode === 'add' && !isCopyMode ? initialValues : selectedObj!}
        />
      )}

      <ActionBar
        onAddClick={handleAddClick}
        onEditClick={handleEditClick}
        onDeleteClick={handleDeleteClick}
        onCopyClick={handleCopyClick}
        onViewClick={handleViewClick}
        onRefreshClick={refreshKhaiBaoTamDungKhauHaoTSCDs}
        selectedObj={selectedObj}
      />

      <div className='w-full overflow-hidden'>
        {isLoading && <LoadingOverlay />}

        {!isLoading && (
          <AritoDataTables tables={tables} selectedRowId={selectedRowIndex || undefined} onRowClick={handleRowClick} />
        )}
      </div>

      {showDelete && (
        <ConfirmationDialog
          open={showDelete}
          onClose={handleCloseDelete}
          onConfirm={() => {
            if (selectedObj) {
              deleteKhaiBaoTamDungKhauHaoTSCD(selectedObj.uuid);
            }
            handleCloseDelete();
            clearSelection();
          }}
        />
      )}
    </div>
  );
}
