import { z } from 'zod';

export const searchSchema = z.object({
  ma_thue: z.string().min(1, '<PERSON><PERSON> thuế là bắt buộc'),
  ten_thue: z.string().min(1, 'Tên thuế là bắt buộc'),
  nhom_thue: z.string().optional(),
  nhom_thue_data: z.string().optional(),
  tk_thue_dau_ra: z.string().min(1, 'TK thuế đầu ra là bắt buộc'),
  tk_thue_dau_ra_data: z.string().optional(),
  tk_thue_dau_ra_duoc_gia: z.string().min(1, 'TK thuế đầu ra được giảm trừ là bắt buộc'),
  tk_thue_dau_ra_duoc_gia_data: z.string().optional(),
  tk_thue_dau_vao: z.string().min(1, 'TK thuế đầu vào là bắt buộc'),
  tk_thue_dau_vao_data: z.string().optional(),
  tk_thue_dau_vao_duoc_gia: z.string().min(1, 'T<PERSON> thuế đầu vào được giảm trừ là bắt buộc'),
  tk_thue_dau_vao_duoc_gia_data: z.string().optional(),
  thue_suat: z.union([z.string().transform(val => (val === '' ? 0 : parseFloat(val))), z.number()]).default(0)
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export interface TaxRate {
  uuid: string;
  ma_thue: string;
  ten_thue: string;
  nhom_thue: string;
  nhom_thue_data?: string;
  tk_thue_dau_ra: string;
  tk_thue_dau_ra_data?: string;
  tk_thue_dau_ra_duoc_gia: string;
  tk_thue_dau_ra_duoc_gia_data?: string;
  tk_thue_dau_vao: string;
  tk_thue_dau_vao_data?: string;
  tk_thue_dau_vao_duoc_gia: string;
  tk_thue_dau_vao_duoc_gia_data?: string;
  thue_suat: number;
}

export interface TaxRateInput {
  ma_thue: string;
  ten_thue: string;
  thue_suat: number;
  nhom_thue?: string | null;
  tk_thue_dau_ra?: string | null;
  tk_thue_dau_ra_duoc_gia?: string | null;
  tk_thue_dau_vao?: string | null;
  tk_thue_dau_vao_duoc_gia?: string | null;
}

export interface TaxRateResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: TaxRate[];
}

// Alias for backward compatibility
export type TaxRateFormattedData = TaxRate;
