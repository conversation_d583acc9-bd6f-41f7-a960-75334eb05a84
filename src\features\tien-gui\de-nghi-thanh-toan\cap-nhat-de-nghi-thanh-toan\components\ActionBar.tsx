import { Binoculars, FileDown, FileText, Pencil, Plus, Printer, Table, Trash } from 'lucide-react';
import { AritoActionBar, AritoActionButton, AritoIcon, AritoMenuButton } from '@/components/custom/arito';

interface ActionBarProps {
  onAddClick: () => void;
  onEditClick: () => void;
  onDeleteClick: () => void;
  onCopyClick: () => void;
  onSearchClick: () => void;
  onRefreshClick: () => void;
  onPrintClick: () => void;
}

export const ActionBar = ({
  onAddClick,
  onEditClick,
  onDeleteClick,
  onCopyClick,
  onSearchClick,
  onRefreshClick,
  onPrintClick
}: ActionBarProps) => (
  <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Cập nhật đề nghị chi</h1>}>
    <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} variant='primary' />
    <AritoActionButton
      title='Sửa'
      icon={Pencil}
      onClick={onEditClick}
      // disabled={isEditDisabled}
    />
    <AritoActionButton
      title='Xóa'
      icon={Trash}
      onClick={onDeleteClick}
      // disabled={isDeleteDisabled}
    />
    <AritoActionButton title='Sao chép' icon={FileText} onClick={onCopyClick} />
    <AritoActionButton title='In ấn' icon={Printer} onClick={() => {}} />
    <AritoActionButton title='Tìm kiếm' icon={Binoculars} onClick={onSearchClick} />
    <AritoMenuButton
      items={[
        {
          title: 'Refresh',
          icon: <AritoIcon icon={15} />,
          onClick: () => {},
          group: 0
        },
        {
          title: 'Cố định cột',
          icon: <AritoIcon icon={16} />,
          onClick: () => {},
          group: 0
        },
        {
          title: 'In nhiều',
          icon: <AritoIcon icon={883} />,
          onClick: () => {},
          group: 1
        },
        {
          title: 'Kết xuất dữ liệu',
          icon: <AritoIcon icon={555} />,
          onClick: () => {},
          group: 1
        }
      ]}
    />
  </AritoActionBar>
);
