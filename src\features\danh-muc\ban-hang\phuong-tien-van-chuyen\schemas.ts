import { z } from 'zod';

export const formSchema = z.object({
  ma_ptvc: z.string().nonempty('<PERSON>ã phương tiện là bắt buộc'),
  ten_ptvc: z.string().nonempty('Tên phương tiện là bắt buộc'),
  ten_ptvc2: z.string().optional(),
  status: z.string().optional()
});

export type FormValues = z.infer<typeof formSchema>;

export const initialValues: FormValues = {
  ma_ptvc: '',
  ten_ptvc: '',
  ten_ptvc2: '',
  status: '1'
};
