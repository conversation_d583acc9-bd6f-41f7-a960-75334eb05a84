import { useState } from 'react';
import { SearchFormValues, TaxRateInput } from '../schemas';
import { useTaxRate } from '@/hooks/queries/useTaxRate';

/**
 * <PERSON><PERSON><PERSON><PERSON> đổi từ TaxRateInput local sang TaxRateInput API
 */
const adaptToApiTaxRate = (localTaxRate: TaxRateInput): any => {
  return {
    ma_thue: localTaxRate.ma_thue,
    ten_thue: localTaxRate.ten_thue,
    ten_thue2: null,
    thue_suat: localTaxRate.thue_suat || 0,
    thue_suat_hddt: 0,
    nhom_thue: localTaxRate.nhom_thue,
    tk_thue_dau_ra: localTaxRate.tk_thue_dau_ra,
    tk_thue_dau_ra_duoc_gia: localTaxRate.tk_thue_dau_ra_duoc_gia,
    tk_thue_dau_vao: localTaxRate.tk_thue_dau_vao,
    tk_thue_dau_vao_duoc_gia: localTaxRate.tk_thue_dau_vao_duoc_gia,
    stt: 0,
    loai_thue: null,
    status: 1
  };
};

export const useTaxRateIntegration = () => {
  const { taxRates, isLoading, addTaxRate, updateTaxRate, deleteTaxRate, refreshTaxRates } = useTaxRate();
  const [error, setError] = useState<string | null>(null);

  const handleAddTaxRate = async (formData: SearchFormValues): Promise<boolean> => {
    try {
      setError(null);

      // Convert form data to API input format
      const taxRateInput: TaxRateInput = {
        ma_thue: formData.ma_thue,
        ten_thue: formData.ten_thue,
        thue_suat: formData.thue_suat,
        nhom_thue: formData.nhom_thue || null,
        tk_thue_dau_ra: formData.tk_thue_dau_ra || null,
        tk_thue_dau_ra_duoc_gia: formData.tk_thue_dau_ra_duoc_gia || null,
        tk_thue_dau_vao: formData.tk_thue_dau_vao || null,
        tk_thue_dau_vao_duoc_gia: formData.tk_thue_dau_vao_duoc_gia || null
      };

      // Check if tax rate with same code already exists
      const existingTaxRate = taxRates.find(item => item.ma_thue === taxRateInput.ma_thue);
      if (existingTaxRate) {
        setError('Mã thuế đã tồn tại');
        return false;
      }

      // Chuyển đổi sang định dạng API trước khi gọi
      const apiTaxRateInput = adaptToApiTaxRate(taxRateInput);
      await addTaxRate(apiTaxRateInput);
      await refreshTaxRates();
      return true;
    } catch (err: any) {
      setError(err.message || 'Lỗi khi thêm thuế suất');
      return false;
    }
  };

  const handleUpdateTaxRate = async (uuid: string, formData: SearchFormValues): Promise<boolean> => {
    try {
      setError(null);

      // Convert form data to API input format
      const taxRateInput: TaxRateInput = {
        ma_thue: formData.ma_thue,
        ten_thue: formData.ten_thue,
        thue_suat: formData.thue_suat,
        nhom_thue: formData.nhom_thue || null,
        tk_thue_dau_ra: formData.tk_thue_dau_ra || null,
        tk_thue_dau_ra_duoc_gia: formData.tk_thue_dau_ra_duoc_gia || null,
        tk_thue_dau_vao: formData.tk_thue_dau_vao || null,
        tk_thue_dau_vao_duoc_gia: formData.tk_thue_dau_vao_duoc_gia || null
      };

      // Check if tax rate with same code already exists (excluding current record)
      const existingTaxRate = taxRates.find(item => item.uuid !== uuid && item.ma_thue === taxRateInput.ma_thue);
      if (existingTaxRate) {
        setError('Mã thuế đã tồn tại');
        return false;
      }

      // Chuyển đổi sang định dạng API trước khi gọi
      const apiTaxRateInput = adaptToApiTaxRate(taxRateInput);
      await updateTaxRate(uuid, apiTaxRateInput);
      await refreshTaxRates();
      return true;
    } catch (err: any) {
      console.error('Error updating tax rate:', err);
      setError(err.message || 'Lỗi khi cập nhật thuế suất');
      return false;
    }
  };

  const handleDeleteTaxRate = async (uuid: string): Promise<boolean> => {
    try {
      setError(null);
      await deleteTaxRate(uuid);
      await refreshTaxRates();
      return true;
    } catch (err: any) {
      console.error('Error deleting tax rate:', err);
      setError(err.message || 'Lỗi khi xóa thuế suất');
      return false;
    }
  };

  return {
    taxRates,
    isLoading,
    error,
    handleAddTaxRate,
    handleUpdateTaxRate,
    handleDeleteTaxRate,
    refreshTaxRates
  };
};
