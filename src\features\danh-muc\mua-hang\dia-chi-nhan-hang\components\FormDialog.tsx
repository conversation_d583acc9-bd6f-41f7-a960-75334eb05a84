import { useState } from 'react';
import { on } from 'events';
import { AritoDialog, AritoForm, AritoIcon, BottomBar } from '@/components/custom/arito';
import ConfirmationDialog from '@/components/custom/arito/dialog/confirm-dialog';
import { FormValues, formSchema, initialValues } from '../schemas';
import type { KhoHang } from '@/types/schemas';
import { FormMode } from '@/types/form';
import Form from './BasicForm';

interface FormDialogProps {
  mode: FormMode;
  open: boolean;
  onClose: () => void;

  onAddButtonClick?: () => void;
  onEditButtonClick?: () => void;
  onDeleteButtonClick?: () => void;
  onCopyButtonClick?: () => void;
  onWatchButtonClick?: () => void;
  onSubmit: (data: any) => void;
  initialData: any;
}
function FormDialog({
  mode,
  open,
  onClose,
  onAddButtonClick,
  onCopyButtonClick,
  onDeleteButtonClick,
  onEditButtonClick,
  onSubmit,
  initialData
}: FormDialogProps) {
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [kho, setKho] = useState<KhoHang | null>(initialData?.ma_kho_data || null);
  const handleSubmit = async (data: FormValues) => {
    try {
      await onSubmit({
        ...data,
        ma_kho: kho?.uuid || null
      });
    } catch (err: any) {
      () => {};
    }
  };
  const handleCloseDialog = () => {
    setShowConfirmDialog(false);
    onClose();
  };
  const handleClose = () => {
    setShowConfirmDialog(false);
    onClose();
  };
  return (
    <>
      <AritoDialog
        open={open}
        onClose={handleClose}
        title={mode === 'add' ? 'Mới' : mode === 'edit' ? 'Sửa' : 'Xem'}
        maxWidth='lg'
        disableBackdropClose={true}
        disableEscapeKeyDown={true}
        titleIcon={<AritoIcon icon={281} />}
      >
        <AritoForm
          mode={mode}
          hasAritoActionBar={false}
          schema={formSchema}
          onSubmit={handleSubmit}
          initialData={initialData || initialValues}
          className='w-full md:min-w-[500px] lg:min-w-[600px]'
          headerFields={<Form mode={mode} kho={kho} setKho={setKho} />}
          classNameBottomBar='relative w-full flex justify-end gap-2'
          bottomBar={
            <BottomBar
              mode={mode}
              onAdd={onAddButtonClick}
              onEdit={onEditButtonClick}
              onDelete={onDeleteButtonClick}
              onCopy={onCopyButtonClick}
              onClose={() => setShowConfirmDialog(true)}
            />
          }
        />
      </AritoDialog>

      <ConfirmationDialog
        open={showConfirmDialog}
        onClose={() => setShowConfirmDialog(false)}
        onConfirm={handleCloseDialog}
        title='Cảnh báo'
        message='Bạn muốn kết thúc?'
      />
    </>
  );
}

export default FormDialog;
