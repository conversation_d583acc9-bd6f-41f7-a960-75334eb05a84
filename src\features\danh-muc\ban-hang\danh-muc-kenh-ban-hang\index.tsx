'use client';
import { useEffect, useState } from 'react';
import { AritoDataTables, LoadingOverlay } from '@/components/custom/arito';
import { DeleteDialog, FormDialog, ActionBar } from './components';
import { getDataTableColumns } from './cols-definition';
import { useKenhBanHang } from '@/hooks/queries';
import { useDialogState } from './hooks';
import { FormValues } from './schemas';
import { useRows } from '@/hooks';

export default function DanhMucKenhBanHang() {
  const { kenhBanHangs, isLoading, addKenhBanHang, updateKenhBanHang, deleteKenhBanHang, refreshKenhBanHangs } =
    useKenhBanHang();
  const { clearSelection, handleRowClick, selectedObj, selectedRowIndex } = useRows();

  // Form submission states
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);

  const {
    handleAddButtonClick,
    handleCloseDeleteDialog,
    handleCloseDialog,
    handleCopyButtonClick,
    handleDeleteButtonClick,
    handleEditButtonClick,
    handleWatchButtonClick,
    mode,
    open,
    showDeleteDialog,
    formData,
    isCopyMode
  } = useDialogState(selectedObj, clearSelection);

  // Handle form submission
  const handleFormSubmit = async (data: FormValues) => {
    setIsSubmitting(true);
    setFormError(null);

    try {
      if (mode === 'add') {
        await addKenhBanHang(data);
      } else if (mode === 'edit' && formData.uuid) {
        await updateKenhBanHang({
          uuid: formData.uuid,
          ...data
        });
      }

      // Refresh data after successful submission
      await refreshKenhBanHangs();

      // Close the form dialog
      handleCloseDialog();
    } catch (err: any) {
      console.error('Error submitting form:', err);
      setFormError(err.message || 'Có lỗi xảy ra khi lưu dữ liệu');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle delete confirmation
  const handleConfirmDelete = async () => {
    if (selectedObj) {
      try {
        await deleteKenhBanHang(selectedObj.uuid);

        // Refresh data after successful deletion
        await refreshKenhBanHangs();

        handleCloseDeleteDialog();
        clearSelection();
      } catch (error) {
        console.error('Error deleting sales channel:', error);
      }
    }
  };

  // Refresh data when component mounts
  useEffect(() => {
    refreshKenhBanHangs();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  let tables = [
    {
      name: '',
      rows: kenhBanHangs,
      columns: getDataTableColumns()
    }
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      <FormDialog
        mode={mode}
        open={open}
        onClose={handleCloseDialog}
        onSubmit={handleFormSubmit}
        isSubmitting={isSubmitting}
        error={formError}
        onAddButtonClick={handleAddButtonClick}
        onEditButtonClick={handleEditButtonClick}
        onCopyButtonClick={handleCopyButtonClick}
        onWatchButtonClick={handleWatchButtonClick}
        initialData={formData}
        selectedObj={selectedObj}
        isCopyMode={isCopyMode}
      />

      <DeleteDialog
        open={showDeleteDialog}
        onClose={handleCloseDeleteDialog}
        selectedObj={selectedObj}
        onConfirmDelete={handleConfirmDelete}
      />

      <div className='w-full'>
        <ActionBar
          onAddIconClick={handleAddButtonClick}
          onEditIconClick={handleEditButtonClick}
          onDeleteIconClick={handleDeleteButtonClick}
          onCopyIconClick={handleCopyButtonClick}
          onWatchIconClick={handleWatchButtonClick}
          hasRowSelected={!!selectedObj}
        />
        {isLoading && <LoadingOverlay />}

        {!isLoading && (
          <AritoDataTables tables={tables} selectedRowId={selectedRowIndex || undefined} onRowClick={handleRowClick} />
        )}
      </div>
    </div>
  );
}
