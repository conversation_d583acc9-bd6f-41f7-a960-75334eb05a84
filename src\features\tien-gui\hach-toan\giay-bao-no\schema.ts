import { z } from 'zod';

export const searchSchema = z.object({
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  voucherNumberStart: z.string().optional(),
  voucherNumberEnd: z.string().optional(),
  invoiceType: z.string().optional(),
  customerCode: z.string().optional(),
  employeeCode: z.string().optional(),
  description: z.string().optional(),
  materialCode: z.string().optional(),
  warehouseCode: z.string().optional(),
  batchCode: z.string().optional(),
  locationCode: z.string().optional(),
  objectCode: z.string().optional(),
  accountCode: z.string().optional(),
  unit: z.string().optional(),
  status: z.string().optional(),
  filterByUser: z.string().optional()
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const formSchema = z.object({
  ma_ngv: z.string().min(1, '<PERSON><PERSON><PERSON> chứng từ là bắt buộc'),
  dia_chi: z.string().optional(),
  ong_ba: z.string().optional(),
  dien_giai: z.string().optional(),
  tien_cltg: z.number().optional(),
  unit_id: z.string().min(1, 'Mã đơn vị là bắt buộc'),
  so_ct: z.string().min(1, 'Số chứng từ là bắt buộc'),
  ngay_ct: z.string().min(1, 'Ngày chứng từ là bắt buộc'),
  ngay_lct: z.string().min(1, 'Ngày lập là bắt buộc'),
  ma_nt: z.string().optional(),
  ty_gia: z.number().optional(),
  status: z.string().optional(),
  transfer_yn: z.boolean().optional(),

  hd_yn: z.boolean().optional(),

  tg_dd: z.boolean().optional(),

  loai_lenh: z.string().optional(),
  stk_kh: z.string().optional(),
  ten_kh: z.string().optional(),
  chi_nhanh_nh: z.string().optional(),
  tinh_thanh_nh: z.string().optional(),
  dien_giai_nh: z.string().optional(),
  phi_nhan_yn: z.boolean().optional(),

  ma_kh: z.string().optional(),
  so_ct_goc: z.string().optional(),
  dien_giai_ct_goc: z.string().optional()
});

export type FormSchema = z.infer<typeof formSchema>;

export const initialFormValues: FormSchema = {
  ma_ngv: '1',
  dia_chi: '',
  ong_ba: '',
  dien_giai: '',
  unit_id: '',
  so_ct: '',
  ngay_ct: new Date().toISOString().split('T')[0],
  ngay_lct: new Date().toISOString().split('T')[0],
  ma_nt: '',
  ty_gia: 1,
  status: '',
  transfer_yn: false,

  hd_yn: false,
  tg_dd: false,

  loai_lenh: '',
  stk_kh: '',
  ten_kh: '',
  chi_nhanh_nh: '',
  tinh_thanh_nh: '',
  dien_giai_nh: '',
  phi_nhan_yn: false,

  ma_kh: '',
  so_ct_goc: '',
  dien_giai_ct_goc: ''
};
