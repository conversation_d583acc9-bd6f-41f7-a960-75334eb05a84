import { QUERY_KEYS, boPhanSearchColumns, chungTuSearchColumns } from '@/constants';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { <PERSON>P<PERSON>, ChungTu, NgoaiTe, User } from '@/types/schemas';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface Props {
  formMode: FormMode;
  selectedBoPhan: BoPhan | null;
  setSelectedBoPhan: (value: BoPhan | null) => void;
  selectedChungTu: ChungTu | null;
  setSelectedChungTu: (value: ChungTu | null) => void;
  currencies: NgoaiTe[];
  users: User[];
}

export const BasicInfoTab = ({
  formMode,
  selectedBoPhan,
  setSelectedBoPhan,
  selectedChungTu,
  setSelectedChungTu,
  currencies,
  users
}: Props) => (
  <div className='space-y-2 p-4'>
    <div className='flex flex-col gap-6 lg:flex-row lg:gap-8'>
      <div className='flex-1 space-y-2'>
        <div className='flex items-center gap-2'>
          <Label className='w-40 min-w-40 text-left'>Loại phiếu chi</Label>
          <div className='max-w-xs flex-1'>
            <FormField
              type='select'
              options={[
                { value: 'hoa_don', label: '1. Theo hóa đơn' },
                { value: 'nha_cung_cap', label: '2. Theo nhà cung cấp' }
              ]}
              name='doc_type'
              disabled={formMode === 'view'}
            />
          </div>
          <FormField label='Có ngân sách' name='has_budget' type='checkbox' disabled={formMode === 'view'} />
        </div>

        {/* Address field */}
        <div className='flex items-center gap-2'>
          <Label className='w-40 min-w-40 text-left'>Nguời đề nghị</Label>
          <div className='max-w-xs flex-1'>
            <FormField
              type='select'
              options={users.map(user => ({
                value: user.id.toString(),
                label: `${user.username} - ${user.email}`
              }))}
              name='ma_ngv'
              disabled={formMode === 'view'}
            />
          </div>
          <div className='flex items-center gap-4 lg:flex-1'>
            <Label className='w-18'>Bộ phận</Label>
            <div className='flex-1'>
              <SearchField<BoPhan>
                type='text'
                displayRelatedField='ten_bp'
                columnDisplay='ma_bp'
                className='w-[180px]'
                searchEndpoint={`/${QUERY_KEYS.BO_PHAN}`}
                searchColumns={boPhanSearchColumns}
                dialogTitle='Danh mục tài khoản'
                value={selectedBoPhan?.ma_bp || ''}
                onRowSelection={setSelectedBoPhan}
                disabled={formMode === 'view'}
              />
              {/* Hidden field to capture BoPhan value in form */}
              <div style={{ display: 'none' }}>
                <FormField name='boPhanCode' type='text' value={selectedBoPhan?.ma_bp || ''} />
              </div>
            </div>
          </div>
        </div>

        {/* Payer field */}
        <div className='flex items-center gap-2'>
          <Label className='w-40 min-w-40 text-left'>Nhận tiền</Label>
          <div className='flex flex-1 gap-2'>
            <div className='flex-[3]'>
              <FormField name='ong_ba' type='text' disabled={formMode === 'view'} placeholder='Tên người nhận tiền' />
            </div>
            <div className='flex-[7]'>
              <FormField name='dia_chi' type='text' disabled={formMode === 'view'} placeholder='Địa chỉ nhận tiền' />
            </div>
          </div>
        </div>

        {/* Description field */}
        <div className='flex items-center gap-2'>
          <Label className='w-40 min-w-40 text-left'>Diễn giải</Label>
          <div className='flex-1'>
            <FormField name='dien_giai' type='text' disabled={formMode === 'view'} />
          </div>
        </div>
      </div>

      <div className='w-full space-y-3 lg:w-80'>
        {/* Document number field */}
        <div className='flex items-center gap-2'>
          <Label className='w-32 min-w-32 text-left'>Số chứng từ</Label>
          <div className='flex-1'>
            <SearchField<ChungTu>
              type='text'
              displayRelatedField='ten_ct'
              columnDisplay='ma_ct'
              className='w-[180px]'
              searchEndpoint={`/${QUERY_KEYS.CHUNG_TU}`}
              searchColumns={chungTuSearchColumns}
              dialogTitle='Danh mục tài khoản'
              value={selectedChungTu?.ma_ct || ''}
              onRowSelection={setSelectedChungTu}
              disabled={formMode === 'view'}
            />
            {/* Hidden field to capture ChungTu value in form */}
            <div style={{ display: 'none' }}>
              <FormField name='chungTuCode' type='text' value={selectedChungTu?.ma_ct || ''} />
            </div>
          </div>
        </div>

        {/* Document date field */}
        <div className='flex items-center gap-2'>
          <Label className='w-32 min-w-32 text-left'>Ngày chứng từ</Label>
          <div className='flex-1'>
            <FormField type='date' name='ngay_ct' disabled={formMode === 'view'} />
          </div>
        </div>

        {/* Foreign currency and exchange rate - responsive row */}
        <div className='flex flex-col gap-2 sm:flex-row sm:items-center'>
          <div className='flex items-center gap-2 sm:flex-1'>
            <Label className='w-32 min-w-32 text-left sm:w-20 sm:min-w-20'>Ngoại tệ</Label>
            <div className='flex-1'>
              <FormField
                type='select'
                name='ma_nt'
                options={currencies.map(currency => ({
                  value: currency.uuid, // Use UUID instead of currency code
                  label: `${currency.ma_nt} - ${currency.ten_nt}` // Show both code and name for clarity
                }))}
                disabled={formMode === 'view'}
                placeholder='Chọn ngoại tệ'
              />
            </div>
          </div>
          <div className='flex items-center gap-2 sm:flex-1'>
            <div className='mb-2 flex-1'>
              <FormField type='number' name='ty_gia' disabled={formMode === 'view'} />
            </div>
          </div>
        </div>

        {/* Status field */}
        <div className='flex items-center gap-2'>
          <Label className='w-32 min-w-32 text-left'>Trạng thái</Label>
          <div className='flex-1'>
            <FormField
              name='status'
              type='select'
              disabled={formMode === 'view'}
              options={[
                { value: '0', label: 'Lập chứng từ' },
                { value: '1', label: 'Chờ duyệt' },
                { value: '5', label: 'Đã duyệt' }
              ]}
            />
          </div>
        </div>

        {/* Data received checkbox */}
        <div className='flex items-center gap-2'>
          <FormField label='Dữ liệu được nhận' name='data_received' type='checkbox' disabled={formMode === 'view'} />
        </div>
      </div>
    </div>
  </div>
);
