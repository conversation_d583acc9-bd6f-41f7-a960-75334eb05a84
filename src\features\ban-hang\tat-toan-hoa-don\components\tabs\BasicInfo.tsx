import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { accountSearchColumns } from '@/constants/search-columns';
import { Label } from '@/components/ui/label';
import { <PERSON><PERSON><PERSON>an } from '@/types/schemas';
import { QUERY_KEYS } from '@/constants';

interface BasicInfoProps {
  searchFieldStates: {
    account: TaiKhoan | null;
    setAccount: (account: <PERSON><PERSON><PERSON><PERSON> | null) => void;
  };
}

const BasicInfo: React.FC<BasicInfoProps> = ({ searchFieldStates }) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='flex flex-col space-y-3'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Ngày h.toán từ/đến:</Label>
          <div>
            <AritoFormDateRangeDropdown fromDateName='ngay_h_toan_tu' toDateName='ngay_h_toan_den' />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Được thanh toán đến:</Label>
          <FormField name='duoc_thanh_toan_de' defaultValue={new Date()} type='date' />
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Ngày tất toán:</Label>
          <FormField name='ngay_tat_toan' defaultValue={new Date()} type='date' />
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản nợ:</Label>
          <SearchField<TaiKhoan>
            type='text'
            columnDisplay={'code'}
            displayRelatedField={'name'}
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
            searchColumns={accountSearchColumns}
            value={searchFieldStates.account?.code || ''}
            relatedFieldValue={searchFieldStates.account?.name || ''}
            onRowSelection={searchFieldStates.setAccount}
          />
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
