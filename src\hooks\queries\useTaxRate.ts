import { useState, useEffect } from 'react';
import { TaxRate, TaxRateInput, TaxRateResponse } from '@/types/schemas';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/api';

interface UseTaxRateReturn {
  taxRates: TaxRate[];
  isLoading: boolean;
  addTaxRate: (newTaxRate: TaxRateInput) => Promise<TaxRate>;
  updateTaxRate: (uuid: string, updatedTaxRate: TaxRateInput) => Promise<TaxRate>;
  deleteTaxRate: (uuid: string) => Promise<void>;
  refreshTaxRates: () => Promise<void>;
}

/**
 * Hook for managing TaxRate data
 *
 * This hook provides functions to fetch, create, update, and delete tax rates.
 */
export const useTaxRate = (initialTaxRates: TaxRate[] = []): UseTaxRateReturn => {
  const [taxRates, setTaxRates] = useState<TaxRate[]>(initialTaxRates);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const { entity } = useAuth();

  const fetchTaxRates = async () => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.get<TaxRateResponse>(`/entities/${entity.slug}/erp/taxes/`);
      setTaxRates(response.data.results);
    } catch (error) {
      console.error('Error fetching tax rates:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const addTaxRate = async (newTaxRate: TaxRateInput): Promise<TaxRate> => {
    if (!entity?.slug) throw new Error('Entity slug is required');

    setIsLoading(true);
    try {
      const payload = newTaxRate;

      const response = await api.post<TaxRate>(`/entities/${entity.slug}/erp/taxes/`, payload, {
        headers: {
          'Content-Type': 'application/json'
        }
      });
      const addedTaxRate = response.data;

      setTaxRates(prev => [...prev, addedTaxRate]);
      return addedTaxRate;
    } catch (error: any) {
      console.error('Error adding tax rate:', error);
      if (error.response) {
        const errorMessage =
          error.response.data?.detail ||
          (typeof error.response.data === 'object'
            ? JSON.stringify(error.response.data)
            : error.message || 'Unknown error');

        throw new Error(errorMessage);
      }
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateTaxRate = async (uuid: string, updatedTaxRate: TaxRateInput): Promise<TaxRate> => {
    if (!entity?.slug) throw new Error('Entity slug is required');

    setIsLoading(true);
    try {
      const payload = updatedTaxRate;

      const response = await api.put<TaxRate>(`/entities/${entity.slug}/erp/taxes/${uuid}/`, payload, {
        headers: {
          'Content-Type': 'application/json'
        }
      });
      const updatedTaxRateData = response.data;

      setTaxRates(prev =>
        prev.map(taxRate => (taxRate.uuid === updatedTaxRateData.uuid ? updatedTaxRateData : taxRate))
      );

      return updatedTaxRateData;
    } catch (error: any) {
      console.error('Error updating tax rate:', error);
      if (error.response) {
        const errorMessage =
          error.response.data?.detail ||
          (typeof error.response.data === 'object'
            ? JSON.stringify(error.response.data)
            : error.message || 'Unknown error');

        throw new Error(errorMessage);
      }
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteTaxRate = async (uuid: string): Promise<void> => {
    if (!entity?.slug) throw new Error('Entity slug is required');

    setIsLoading(true);
    try {
      await api.delete(`/entities/${entity.slug}/erp/taxes/${uuid}/`);
      setTaxRates(prev => prev.filter(taxRate => taxRate.uuid !== uuid));
    } catch (error) {
      console.error('Error deleting tax rate:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const refreshTaxRates = async (): Promise<void> => {
    await fetchTaxRates();
  };

  useEffect(() => {
    fetchTaxRates();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [entity?.slug]);

  return {
    taxRates,
    isLoading,
    addTaxRate,
    updateTaxRate,
    deleteTaxRate,
    refreshTaxRates
  };
};
