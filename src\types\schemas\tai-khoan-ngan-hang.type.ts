/**
 * TypeScript interface for TaiKhoanNganHang (Bank Account) model
 *
 * This interface represents the structure of the TaiKhoanNganHang model from the backend.
 * It defines bank accounts used for financial transactions.
 */

import { AccountModel } from './account.type';
import { Ng<PERSON><PERSON>ang } from './ngan-hang.type';
import { ApiResponse } from '../api.type';

/**
 * Interface for TaiKhoanNganHang (Bank Account)
 */
export interface TaiKhoanNganHang {
  /**
   * Unique identifier (UUID)
   */
  uuid: string;

  /**
   * Reference to the entity model
   */
  entity_model?: string;

  /**
   * Unit identifier
   */
  unit_id: string;

  /**
   * Bank code
   */
  ma_ngan_hang: string;

  /**
   * Bank data
   */
  ma_ngan_hang_data?: NganHang;

  /**
   * Bank name
   */
  name: string;

  /**
   * Bank account code
   */
  account_code: string;
  tknh: string;

  /**
   * Bank account number
   */
  ma_tai_khoan: string;

  /**
   * Bank account owner
   */
  chu_tk: string;

  /**
   * Bank branch
   */
  chi_nhanh?: string | null;

  /**
   * Account code
   */
  account_model?: string | null;

  /**
   * Account data
   */
  account_model_data?: AccountModel;

  /**
   * Account name
   */
  ten_tk?: string | null;

  /**
   * Bank account name
   */
  ten_tknh?: string | null;

  /**
   * Alternative bank account name
   */
  sten_tknh?: string | null;

  /**
   * Province/City
   */
  tinh_thanh?: string | null;

  /**
   * Phone number
   */
  phone?: string | null;

  /**
   * Fax number
   */
  fax?: string | null;

  /**
   * Notes
   */
  ghi_chu?: string | null;

  /**
   * Status indicator ('1'=active, '0'=inactive)
   */
  status: string;

  /**
   * Timestamp of creation
   */
  created?: string;

  /**
   * Timestamp of last update
   */
  updated?: string;
}

/**
 * Type for TaiKhoanNganHang API response
 */
export type TaiKhoanNganHangResponse = ApiResponse<TaiKhoanNganHang>;

/**
 * Interface for creating or updating a TaiKhoanNganHang
 */
export interface TaiKhoanNganHangInput {
  /**
   * Unit identifier
   */
  unit_id: string;

  /**
   * Bank code
   */
  ma_ngan_hang: string;

  /**
   * Bank name
   */
  name: string;

  /**
   * Bank account code
   */
  tknh: string;

  /**
   * Bank account number
   */
  ma_tai_khoan: string;

  /**
   * Bank account owner
   */
  chu_tk: string;

  /**
   * Bank branch
   */
  chi_nhanh?: string | null;

  /**
   * Account code
   */
  account_model?: string | null;

  /**
   * Account name
   */
  ten_tk?: string | null;

  /**
   * Bank account name
   */
  ten_tknh?: string | null;

  /**
   * Alternative bank account name
   */
  sten_tknh?: string | null;

  /**
   * Province/City
   */
  tinh_thanh?: string | null;

  /**
   * Phone number
   */
  phone?: string | null;

  /**
   * Fax number
   */
  fax?: string | null;

  /**
   * Notes
   */
  ghi_chu?: string | null;

  /**
   * Status indicator ('1'=active, '0'=inactive)
   */
  status: string;
}
