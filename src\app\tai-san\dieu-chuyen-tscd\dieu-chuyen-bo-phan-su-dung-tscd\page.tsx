import DieuChuyenBoPhanSuDungTSCD from '@/features/tai-san/dieu-chuyen-tscd/dieu-chuyen-bo-phan-su-dung-tscd';

export default async function Page() {
  const accountRows = [
    // Short-term assets (Tài sản ngắn hạn)
    {
      name: '111',
      accountCode: '111',
      accountName: 'Tiền mặt',
      accountType: 'short_term_assets',
      parentAccount: null,
      isDetailAccount: false,
      isTransactionAccount: true,
      isDebitCredit: 1,
      hasForeignCurrency: 'VND',
      hasDebtTracking: 'Không',
      accountCategory: 'Tài sản',
      debtCalculation: 'Số dư nợ',
      creditCalculation: null
    },
    {
      name: '112',
      accountCode: '112',
      accountName: 'Tiền gửi ngân hàng',
      accountType: 'short_term_assets',
      parentAccount: null,
      isDetailAccount: false,
      isTransactionAccount: true,
      isDebitCredit: 1,
      hasForeignCurrency: 'VND',
      hasDebtTracking: 'Không',
      accountCategory: 'Tài sản',
      debtCalculation: 'Số dư nợ',
      creditCalculation: null
    },

    // Long-term assets (Tài sản dài hạn)
    {
      name: '211',
      accountCode: '211',
      accountName: 'Tài sản cố định hữu hình',
      accountType: 'long_term_assets',
      parentAccount: null,
      isDetailAccount: false,
      isTransactionAccount: true,
      isDebitCredit: 1,
      hasForeignCurrency: 'VND',
      hasDebtTracking: false,
      accountCategory: 'Tài sản',
      debtCalculation: 'Số dư nợ',
      creditCalculation: null
    }
  ];

  return <DieuChuyenBoPhanSuDungTSCD />;
}
