import { useState, useEffect } from 'react';
import { HinhThucThanhToan, NguonDon, PhuongThucThanhToan } from '@/types/schemas';

export const useSearchFieldStates = (initialData?: any) => {
  // Nguon don state
  const [nguonDon, setNguonDon] = useState<NguonDon | null>(initialData?.ma_nguondon_data || null);

  // Hinh thuc thanh toan state
  const [hinhThucThanhToan, setHinhThucThanhToan] = useState<HinhThucThanhToan | null>(
    initialData?.ma_httt_data || null
  );

  // Phuong thuc thanh toan state
  const [phuongThucThanhToan, setPhuongThucThanhToan] = useState<PhuongThucThanhToan | null>(
    initialData?.ma_pttt_data || null
  );

  // Reset states when initialData changes
  useEffect(() => {
    setNguonDon(initialData?.ma_nguondon_data || null);
    setHinhThucThanhToan(initialData?.ma_httt_data || null);
    setPhuongThucThanhToan(initialData?.ma_pttt_data || null);
  }, [initialData]);

  return {
    nguonDon,
    setNguonDon,
    hinhThucThanhToan,
    setHinhThucThanhToan,
    phuongThucThanhToan,
    setPhuongThucThanhToan
  };
};
