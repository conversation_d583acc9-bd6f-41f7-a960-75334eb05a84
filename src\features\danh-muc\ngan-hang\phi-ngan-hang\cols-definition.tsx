import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';

export const getDataTableColumns = (): GridColDef[] => [
  {
    field: 'ma_cpnh',
    headerName: 'Mã phí',
    width: 150
  },
  {
    field: 'ten_cpnh',
    headerName: 'Tên phí',
    width: 250
  },

  {
    field: 'tk_cpnh',
    headerName: 'Tài khoản phí',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      return params.row.tk_cpnh_data?.code || '';
    }
  },
  {
    field: 'ma_thue',
    headerName: 'Thuế suất',
    width: 180,
    renderCell: (params: GridRenderCellParams) => {
      return params.row.ma_thue_data?.ten_thue || '';
    }
  }
];

export const accountContraint = (): GridColDef[] => [
  {
    field: 'ten_doi_tuong',
    headerName: 'Tên đối tượng',
    width: 200
  },
  {
    field: 'bat_buoc_nhap',
    headerName: '<PERSON><PERSON><PERSON> buộc nhập',
    width: 200
  }
];
