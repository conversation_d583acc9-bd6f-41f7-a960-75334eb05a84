import { <PERSON><PERSON>, <PERSON>, <PERSON>cil, Plus, Trash } from 'lucide-react';
import { AritoActionBar, AritoActionButton, AritoIcon, AritoMenuButton } from '@/components/custom/arito';

interface ActionBarProps {
  onAddClick: () => void;
  onEditClick: () => void;
  onDeleteClick: () => void;
  onCopyClick: () => void;
  onViewClick: () => void;
  onRefreshClick: () => void;
}

const ActionBar = ({
  onAddClick,
  onEditClick,
  onDeleteClick,
  onCopyClick,
  onViewClick,
  onRefreshClick
}: ActionBarProps) => (
  <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Danh mục nguồn đơn</h1>}>
    <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} variant='primary' />
    <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} />
    <AritoActionButton title='Xoá' variant='destructive' icon={Trash} onClick={onDeleteClick} />
    <AritoActionButton title='Sao chép' icon={Copy} onClick={onCopyClick} />
    <AritoActionButton title='Xem' icon={Eye} onClick={onViewClick} />
    <AritoMenuButton
      items={[
        {
          title: 'Refresh',
          icon: <AritoIcon icon={15} />,
          onClick: onRefreshClick,
          group: 0
        },
        {
          title: 'Kết xuất dữ liệu',
          icon: <AritoIcon icon={18} />,
          onClick: () => {},
          group: 1
        }
      ]}
    />
  </AritoActionBar>
);

export default ActionBar;
