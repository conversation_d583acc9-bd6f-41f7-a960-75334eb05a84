import React from 'react';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { accountSearchColumns } from '@/constants/search-columns';
import { Label } from '@/components/ui/label';
interface SearchResult {
  value: string;
  label: string;
}
function HeaderTab() {
  return (
    <div className='p-4 md:p-6'>
      <div className='flex flex-col gap-y-4 md:gap-y-6'>
        <div className='space-y-2 md:space-y-2'>
          {/* Account Name Field */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tài khoản</Label>
            <FormField type='text' name='account' className='w-full' />
          </div>
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tên tài khoản</Label>
            <FormField type='text' name='accountName' className='w-full' />
          </div>

          {/* English Name Field */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tên tiếng Anh</Label>
            <FormField type='text' name='englishName' className='w-full' />
          </div>

          {/* Parent Account Field */}
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Tài khoản phí</Label>
            <SearchField
              type='text'
              searchEndpoint='/'
              searchColumns={accountSearchColumns}
              headerFields={<HeaderTab />}
            />
          </div>

          {/* Other Representation Field */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tk đại diện khác</Label>
            <FormField type='text' name='otherRepresentation' className='w-full' />
          </div>
        </div>
      </div>
    </div>
  );
}

export default HeaderTab;
