import { FormField } from '@/components/custom/arito';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface OtherTabProps {
  formMode: FormMode;
}

export function OtherTab({ formMode }: OtherTabProps) {
  return (
    <div className='p-4'>
      <div className='flex items-center'>
        <Label className='w-40'>Mã đối tượng</Label>
        <FormField name='ma_kh' type='text' disabled={formMode === 'view'} />
      </div>
      <div className='flex items-center'>
        <Label className='w-40'>Kèm theo</Label>
        <FormField name='so_ct_goc' type='number' disabled={formMode === 'view'} />
      </div>
      <div className='flex items-center'>
        <Label className='w-40'>Chứng từ gốc</Label>
        <FormField name='dien_giai_ct_goc' type='number' disabled={formMode === 'view'} />
      </div>
    </div>
  );
}
