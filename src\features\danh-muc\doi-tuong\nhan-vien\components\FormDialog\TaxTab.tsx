import { FormField } from '@/components/custom/arito/form/form-field';
import { FormMode } from '@/types/form';

interface TaxTabProps {
  formMode?: FormMode;
}

export default function TaxTab({ formMode }: TaxTabProps) {
  return (
    <div className='space-y-4 p-4'>
      {/* Mã số thuế TNCN và checkbox */}
      <div className='flex items-center gap-x-4'>
        <label className='w-40 min-w-40 text-sm'>Mã số thuế TNCN</label>
        <FormField name='taxCode' type='text' className='w-72' disabled={formMode === 'view'} />

        <FormField name='quyetToanThue' type='checkbox' label='Quyết toán thuế TNCN' disabled={formMode === 'view'} />
        <FormField name='tinhThue' type='checkbox' label='Tính thuế TNCN' disabled={formMode === 'view'} />
        <FormField name='tinhLuyTien' type='checkbox' label='Tính thuế luỹ tiến' disabled={formMode === 'view'} />
        <FormField name='hopDongLaoDong' type='checkbox' label='Hợp đồng lao động' disabled={formMode === 'view'} />
        <FormField
          name='quyetToanNgoai'
          type='checkbox'
          label='Quyết toán dưới 12 tháng (CN nước ngoài)'
          disabled={formMode === 'view'}
        />
      </div>

      {/* Đối tượng thuế */}
      <div className='flex items-center gap-x-4'>
        <label className='w-40 min-w-40 text-sm'>Đối tượng thuế</label>
        <FormField
          name='taxType'
          type='select'
          className='w-72'
          options={[
            { value: 'resident', label: '0. Cư trú' },
            { value: 'non_resident', label: '1. Không cư trú' }
          ]}
        />
      </div>

      {/* Loại giấy tờ và Số giấy tờ */}
      <div className='flex items-center gap-x-4'>
        <label className='w-40 min-w-40 text-sm'>Loại giấy tờ</label>
        <div className='flex flex-1 gap-4'>
          <FormField
            name='docType'
            type='select'
            className='w-40'
            options={[
              { value: 'cmnd', label: '1. CMND' },
              { value: 'cccd', label: '2. CCCD' },
              { value: 'passport', label: '3. Hộ chiếu' },
              { value: 'gks', label: '4. Giấy khai sinh' },
              { value: 'Khác', label: '5. Khác' }
            ]}
          />
          <div className='flex items-center gap-2'>
            <label className='text-sm'>Số giấy tờ</label>
            <FormField name='docNumber' type='text' className='w-64' />
          </div>
        </div>
      </div>
    </div>
  );
}
