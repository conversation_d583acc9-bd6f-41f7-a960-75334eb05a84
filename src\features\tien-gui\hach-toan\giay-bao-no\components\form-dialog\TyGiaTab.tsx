import { FormField } from '@/components/custom/arito';
import { FormMode } from '@/types/form';

interface TyGiaTabProps {
  formMode: FormMode;
}

export function TyGiaTab({ formMode }: TyGiaTabProps) {
  return (
    <div className='space-y-3 p-4'>
      <div className='flex items-center'>
        <FormField
          name='tg_dd'
          type='checkbox'
          label='Sửa tỷ giá ghi sổ'
          disabled={formMode === 'view'}
          labelClassName='w-40'
          className='w-96'
        />
      </div>
    </div>
  );
}
