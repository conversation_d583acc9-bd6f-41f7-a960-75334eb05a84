import { GridCellParams } from '@mui/x-data-grid';
import { useWatch } from 'react-hook-form';
import React from 'react';
import { InputTable } from '@/components/custom/arito';
import { SelectedCellInfo } from '../hooks/useTaxRows';
import { useMauSoHoaDon, useTaxRate } from '@/hooks';
import { getTaxTableColumns } from './columns';
import { FormMode } from '@/types/form';
import ActionBar from './ActionBar';

interface TaxTabProps {
  formMode: FormMode;
  rows: { uuid?: string | null }[];
  selectedRowUuid?: string | null;
  selectedCell?: SelectedCellInfo | null;
  onRowClick?: (params: any) => void;
  onCellClick?: (params: GridCellParams) => void;
  onAddRow: () => void;
  onDeleteRow: () => void;
  onCopyRow: () => void;
  onPasteRow: () => void;
  onMoveRow: (direction: 'up' | 'down') => void;
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void;
}

export const TaxTab: React.FC<TaxTabProps> = ({
  formMode,
  rows,
  selectedRowUuid,
  onRowClick,
  onAddRow,
  onDeleteRow,
  onCopyRow,
  onMoveRow,
  onCellValueChange
}) => {
  const { taxRates } = useTaxRate();
  const { mauSoHoaDons } = useMauSoHoaDon();
  const type = useWatch({ name: 'ma_ngv', defaultValue: '1' });

  return (
    <InputTable
      rows={rows}
      onRowClick={onRowClick}
      selectedRowId={selectedRowUuid || undefined}
      columns={getTaxTableColumns(taxRates, mauSoHoaDons, onCellValueChange)}
      getRowId={row => row?.uuid || ''}
      actionButtons={
        <ActionBar
          formMode={type === '1' ? 'view' : formMode}
          handleAddRow={onAddRow}
          handleDeleteRow={onDeleteRow}
          handleCopyRow={onCopyRow}
          handleMoveRow={onMoveRow}
          handleExport={() => {}}
          handlePin={() => {}}
        />
      }
    />
  );
};
