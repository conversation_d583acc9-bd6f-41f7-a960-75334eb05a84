import { FormField } from '@/components/custom/arito/form/form-field';

const BasicInfo = ({ formMode }: { formMode: 'add' | 'edit' | 'view' }) => (
  <div className='p-4'>
    <div className='grid grid-cols-1 gap-x-8 space-y-2 lg:grid-cols-5 lg:space-y-0'>
      {/* Cột trái */}
      <div className='col-span-4 space-y-2'>
        <FormField
          label='Mã nhóm'
          className='grid grid-cols-[160px,1fr] items-center'
          name='ma_nhom'
          type='text'
          disabled={formMode === 'view'}
        />
        <FormField
          label='Tên nhóm'
          className='grid grid-cols-[160px,1fr] items-center'
          name='ten_phan_nhom'
          type='text'
          disabled={formMode === 'view'}
        />
        <FormField
          label='Tên 2'
          className='grid grid-cols-[160px,1fr] items-center'
          name='ten2'
          type='text'
          disabled={formMode === 'view'}
        />
        <FormField
          label='Trạng thái'
          className='grid grid-cols-[160px,1fr] items-center'
          name='trang_thai'
          type='select'
          options={[
            { value: 'active', label: '1. Còn sử dụng' },
            { value: '0', label: '0. Không sử dụng' }
          ]}
          disabled={formMode === 'view'}
        />
      </div>
    </div>
  </div>
);
export default BasicInfo;
