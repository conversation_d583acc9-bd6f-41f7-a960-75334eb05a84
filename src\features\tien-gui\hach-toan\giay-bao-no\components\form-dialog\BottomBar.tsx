import { FormField } from '@/components/custom/arito';
import { formatNumber } from '@/lib/formatUtils';
import { Label } from '@/components/ui/label';

interface BottomBarProps {
  totalAmount?: number;
  totalQuantity?: number;
  totalBankFees?: number;
  totalTax?: number;
  totalPayment?: number;
}

export function BottomBar({
  totalAmount = 0,
  totalQuantity = 0,
  totalBankFees = 0,
  totalTax = 0,
  totalPayment = 0
}: BottomBarProps) {
  return (
    <div className='w-full border-t bg-white px-4 py-2'>
      <div className='flex'>
        <div className='ml-4 flex flex-col'>
          <div className='flex items-center'>
            <Label className='w-32 font-medium'>Tổng tiền</Label>
            <FormField
              name='t_tien_nt'
              type='number'
              disabled={true}
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatNumber(totalAmount)}
            />
          </div>
          {/* Column 3 */}
          <div className='flex items-center'>
            <Label className='w-32 font-medium'>Tổng thuế</Label>
            <FormField
              name='t_thue_nt'
              type='number'
              disabled={true}
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatNumber(totalTax)}
            />
          </div>
        </div>

        <div className='ml-auto flex w-1/3 flex-col'>
          <div className='flex items-center'>
            <Label className='w-40 font-medium'>Tổng phí ngân hàng</Label>
            <FormField
              name='t_cp_nt'
              type='number'
              disabled={true}
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatNumber(totalBankFees)}
            />
          </div>
          {/* Column 4 */}
          <div className='flex items-center'>
            <Label className='w-40 font-medium'>Tổng thanh toán</Label>
            <FormField
              name='t_tt_nt'
              type='number'
              disabled={true}
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatNumber(totalPayment)}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
