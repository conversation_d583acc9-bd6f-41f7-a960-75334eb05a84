'use client';

import React, { useState, useEffect } from 'react';
import { LoadingOverlay, AritoDataTables } from '@/components/custom/arito';
import { ChungTu, ChungTuInput, ChungTuType } from '@/types/schemas';
import { FormDialog, ActionBar, Sidebar } from './components';
import { useFormState, useRows, useChungTu } from '@/hooks';
import { DialogProvider } from './contexts/DialogContext';
import { DocumentsColumns } from './cols-definition';
import { useGroupFilter } from './hooks';

export default function ThongTinChungTu() {
  const { activeGroup, sidebarOpen, Group, handleFilter, toggleSidebar } = useGroupFilter();
  const [currentXcode, setCurrentXcode] = useState<ChungTuType | undefined>(undefined);
  const { chungTus, isLoading, updateChungTu, refreshChungTus } = useChungTu(currentXcode);

  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection, setSelectedObj } = useRows<ChungTu>();
  const {
    showForm,
    showDelete,
    formMode,
    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick
  } = useFormState();

  useEffect(() => {
    setCurrentXcode(activeGroup || undefined);
  }, [activeGroup]);

  const handleViewRowClick = (obj: ChungTu) => {
    setSelectedObj(obj);
    handleViewClick();
  };

  const tables = [
    {
      name: '',
      rows: chungTus,
      columns: DocumentsColumns(handleViewRowClick)
    }
  ];

  const handleSubmit = async (data: ChungTuInput) => {
    try {
      await updateChungTu(selectedObj?.uuid || '', data);
      refreshChungTus();
      clearSelection();
      handleCloseForm();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  return (
    <DialogProvider>
      <div className='flex h-screen flex-col lg:overflow-hidden'>
        {showForm && formMode !== 'add' && (
          <FormDialog
            open={showForm}
            formMode={formMode}
            initialData={selectedObj || undefined}
            onClose={handleCloseForm}
            onSubmit={handleSubmit}
          />
        )}

        {/* {showForm && formMode === 'add' && (
          <AddFieldDialog open={showForm} onClose={handleCloseForm} formMode={formMode} initialData={selectedObj} />
        )} */}

        {(!showForm || formMode === 'add') && (
          <div className='flex h-full'>
            <Sidebar
              activeGroup={activeGroup}
              onFilterChange={handleFilter}
              group={Group}
              isOpen={sidebarOpen}
              toggleSidebar={toggleSidebar}
            />

            <div className='flex h-full flex-1 flex-col overflow-hidden'>
              <ActionBar
                onEditClick={() => selectedObj && handleEditClick()}
                onRefreshClick={() => console.log('Refresh')}
                onFixedColumnsClick={() => console.log('Fixed Columns')}
                onAddFieldClick={() => selectedObj && handleAddClick()}
              />

              <div className='w-full overflow-hidden'>
                {isLoading && <LoadingOverlay />}
                {!isLoading && (
                  <AritoDataTables
                    tables={tables}
                    selectedRowId={selectedRowIndex || undefined}
                    onRowClick={handleRowClick}
                  />
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </DialogProvider>
  );
}
