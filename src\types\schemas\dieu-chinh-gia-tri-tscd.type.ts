/**
 * TypeScript interface for DieuChinhGiaTriTSCD (Fixed Asset Value Adjustment) model
 *
 * This interface represents the structure of the DieuChinhGiaTriTSCD model from the backend.
 * It defines fixed asset value adjustments in the system.
 */

import { LyDoTangGiamTSCD } from './ly-do-tang-giam-tscd.type';
import { TaiSanCoDinh } from './tai-san-co-dinh.type';
import { ChungTu } from './chung-tu.type';
import { NgoaiTe } from './ngoai-te.type';
import { ApiResponse } from '../api.type';

export interface DieuChinhGiaTriTSCD {
  /**
   * Unique identifier (UUID)
   */
  uuid: string;

  /**
   * Reference to the entity model
   */
  entity_model: string;

  /**
   * Fixed asset reference (UUID)
   */
  ma_ts: string;

  /**
   * Fixed asset data (nested object)
   */
  ma_ts_data?: TaiSanCoDinh;

  /**
   * Fixed asset increase/decrease reason reference (UUID)
   */
  ma_tg_ts: string;

  /**
   * Fixed asset increase/decrease reason data (nested object)
   */
  ma_tg_ts_data?: LyDoTangGiamTSCD;

  /**
   * Type of fixed asset increase/decrease
   */
  loai_tg_ts?: string | null;

  /**
   * Period
   */
  ky?: number | null;

  /**
   * Year
   */
  nam?: number | null;

  /**
   * Document reference (UUID)
   */
  so_ct?: string | null;

  /**
   * Document data (nested object)
   */
  so_ct_data?: ChungTu;

  /**
   * Currency reference (UUID)
   */
  ma_nt?: string | null;

  /**
   * Currency data (nested object)
   */
  ma_nt_data?: NgoaiTe;

  /**
   * Exchange rate
   */
  ty_gia?: string | null;

  /**
   * Original value in foreign currency
   */
  nguyen_gia_nt?: string | null;

  /**
   * Original value
   */
  nguyen_gia?: string | null;

  /**
   * Depreciated value in foreign currency
   */
  gt_da_kh_nt?: string | null;

  /**
   * Depreciated value
   */
  gt_da_kh?: string | null;

  /**
   * Difference value in foreign currency
   */
  gt_cl_nt?: number | null;

  /**
   * Difference value
   */
  gt_cl?: number | null;

  /**
   * Number of depreciation periods
   */
  so_ky_kh?: number | null;

  /**
   * Period depreciation value in foreign currency
   */
  gt_kh_ky_nt?: string | null;

  /**
   * Period depreciation value
   */
  gt_kh_ky?: string | null;

  /**
   * Current depreciation value in foreign currency
   */
  gt_kh_ht_nt?: string | null;

  /**
   * Current depreciation value
   */
  gt_kh_ht?: string | null;

  /**
   * Beginning balance depreciation value in foreign currency
   */
  gt_kh_sdc_nt?: string | null;

  /**
   * Beginning balance depreciation value
   */
  gt_kh_sdc?: string | null;

  /**
   * Description
   */
  dien_giai?: string | null;

  /**
   * Timestamp of creation
   */
  created: string;

  /**
   * Timestamp of last update
   */
  updated: string;
}

/**
 * Input type for creating or updating a DieuChinhGiaTriTSCD
 */
export interface DieuChinhGiaTriTSCDInput {
  /**
   * Fixed asset reference (UUID)
   */
  ma_ts: string;

  /**
   * Fixed asset increase/decrease reason reference (UUID)
   */
  ma_tg_ts: string;

  /**
   * Type of fixed asset increase/decrease
   */
  loai_tg_ts?: string | null;

  /**
   * Period
   */
  ky?: number | null;

  /**
   * Year
   */
  nam?: number | null;

  /**
   * Document reference (UUID)
   */
  so_ct?: string | null;

  /**
   * Currency reference (UUID)
   */
  ma_nt?: string | null;

  /**
   * Exchange rate
   */
  ty_gia?: string | null;

  /**
   * Original value in foreign currency
   */
  nguyen_gia_nt?: string | null;

  /**
   * Original value
   */
  nguyen_gia?: string | null;

  /**
   * Depreciated value in foreign currency
   */
  gt_da_kh_nt?: string | null;

  /**
   * Depreciated value
   */
  gt_da_kh?: string | null;

  /**
   * Difference value in foreign currency
   */
  gt_cl_nt?: number | null;

  /**
   * Difference value
   */
  gt_cl?: number | null;

  /**
   * Number of depreciation periods
   */
  so_ky_kh?: number | null;

  /**
   * Period depreciation value in foreign currency
   */
  gt_kh_ky_nt?: string | null;

  /**
   * Period depreciation value
   */
  gt_kh_ky?: string | null;

  /**
   * Current depreciation value in foreign currency
   */
  gt_kh_ht_nt?: string | null;

  /**
   * Current depreciation value
   */
  gt_kh_ht?: string | null;

  /**
   * Beginning balance depreciation value in foreign currency
   */
  gt_kh_sdc_nt?: string | null;

  /**
   * Beginning balance depreciation value
   */
  gt_kh_sdc?: string | null;

  /**
   * Description
   */
  dien_giai?: string | null;
}

/**
 * Type for DieuChinhGiaTriTSCD API response
 */
export type DieuChinhGiaTriTSCDResponse = ApiResponse<DieuChinhGiaTriTSCD>;

/**
 * Type for DieuChinhGiaTriTSCD list API response
 */
export type DieuChinhGiaTriTSCDListResponse = ApiResponse<DieuChinhGiaTriTSCD>;
