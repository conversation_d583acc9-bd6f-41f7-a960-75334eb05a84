import { z } from 'zod';

export const formSchema = z.object({
  uuid: z.string().optional(),
  ma_kenh_ban_hang: z
    .string()
    .nonempty('<PERSON><PERSON> kênh bán hàng là bắt buộc')
    .refine(val => !val.includes(' '), {
      message: '<PERSON>ã kênh bán hàng không được chứa dấu cách'
    }),
  ma_nguon_don: z.string().optional(),
  ma_cua_hang: z
    .string()
    .optional()
    .refine(val => !val || !val.includes(' '), {
      message: '<PERSON>ã cửa hàng không được chứa dấu cách'
    }),
  hinh_thuc_thanh_toan: z.string().optional(),
  phuong_thuc_thanh_toan: z.string().optional(),
  ti_le_hoa_hong: z
    .union([z.string().transform(val => (val === '' ? 0 : parseFloat(val) || 0)), z.number()])
    .default(0)
    .refine(val => val >= 0, {
      message: 'Tỉ lệ hoa hồng không được âm'
    }),
  loai: z.string().default('1'),
  trang_thai: z.number().default(1),
  // Hidden fields for SearchField components
  ma_nguondon: z.string().optional(),
  ma_httt: z.string().optional(),
  ma_pttt: z.string().optional()
});

export type FormValues = z.infer<typeof formSchema>;

export const initialValues: FormValues = {
  uuid: undefined,
  ma_kenh_ban_hang: '',
  ma_nguon_don: '',
  ma_cua_hang: '',
  hinh_thuc_thanh_toan: '',
  phuong_thuc_thanh_toan: '',
  ti_le_hoa_hong: 0,
  loai: '1',
  trang_thai: 1,
  ma_nguondon: '',
  ma_httt: '',
  ma_pttt: ''
};
