import { Pencil, Plus, Trash, Copy, FileSearch } from 'lucide-react';
import { AritoActionBar, AritoActionButton, AritoIcon, AritoMenuButton } from '@/components/custom/arito';

interface ActionBarProps {
  onAddClick?: () => void;
  onEditClick?: () => void;
  onDeleteClick?: () => void;
  onCopyClick?: () => void;
  onViewClick?: () => void;
  onSearchClick?: () => void;
  onRefreshClick?: () => void;
  onFixedColumnsClick?: () => void;
  selectedObj?: any | null;
}

/**
 * ActionBar component for the Balance Sheet by Unit page
 */
const ActionBar: React.FC<ActionBarProps> = ({
  onAddClick,
  onEditClick,
  onDeleteClick,
  onCopyClick,
  onViewClick,
  onSearchClick,
  onFixedColumnsClick,
  onRefreshClick,
  selectedObj
}) => (
  <AritoActionBar titleComponent={<h1 className='text-xl font-bold'><PERSON><PERSON> báo tạm dừng khấu hao TSCĐ</h1>}>
    {onAddClick && <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} />}
    {onEditClick && <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} disabled={!selectedObj} />}
    {onDeleteClick && <AritoActionButton title='Xóa' icon={Trash} onClick={onDeleteClick} disabled={!selectedObj} />}
    {onCopyClick && <AritoActionButton title='Sao chép' icon={Copy} onClick={onCopyClick} disabled={!selectedObj} />}
    {onViewClick && <AritoActionButton title='Xem' icon={FileSearch} onClick={onViewClick} disabled={!selectedObj} />}

    <AritoMenuButton
      title='Khác'
      items={[
        {
          title: 'Tìm kiếm',
          icon: <AritoIcon icon={12} />,
          onClick: onSearchClick,
          group: 0
        },
        {
          title: 'Refresh',
          icon: <AritoIcon icon={15} />,
          onClick: onRefreshClick,
          group: 1
        },
        {
          title: 'Cố định cột',
          icon: <AritoIcon icon={16} />,
          onClick: onFixedColumnsClick,
          group: 1
        }
      ]}
    />
  </AritoActionBar>
);

export default ActionBar;
