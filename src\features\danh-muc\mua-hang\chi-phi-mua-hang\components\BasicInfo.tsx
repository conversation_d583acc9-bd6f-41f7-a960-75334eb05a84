import { FormField, SearchField } from '@/components/custom/arito';
import { chungTuSearchColumns, QUERY_KEYS } from '@/constants';
import { Label } from '@/components/ui/label';
import { ChungTu } from '@/types/schemas';

export const BasicInfo = ({
  formMode,
  chungTu,
  setMaCt
}: {
  formMode: 'add' | 'edit' | 'view';
  chungTu: ChungTu | null;
  setMaCt: (maCt: ChungTu) => void;
}) => (
  <div className='p-4 pb-20'>
    <div className='grid grid-cols-1 gap-x-8 space-y-2 lg:grid-cols-5 lg:space-y-0'>
      <div className='col-span-5 space-y-2'>
        <FormField
          label='Mã chi phí'
          className='grid grid-cols-[160px,1fr] items-center'
          name='ma_cp'
          type='text'
          disabled={formMode === 'view' || formMode === 'edit'}
        />
        <FormField
          label='Tên chi phí'
          className='grid grid-cols-[160px,1fr] items-center'
          inputClassName='w-full'
          name='ten_cp'
          type='text'
          disabled={formMode === 'view'}
        />
        <FormField
          label='Tên khác'
          className='grid grid-cols-[160px,1fr] items-center'
          name='ten_cp2'
          type='text'
          disabled={formMode === 'view'}
        />
        <FormField
          label='Loại chi phí'
          className='grid grid-cols-[160px,1fr] items-center'
          name='loai_cp'
          type='text'
          disabled={formMode === 'view'}
        />
        <FormField
          label='Tiêu thức phân bổ'
          className='grid grid-cols-[160px,1fr] items-center'
          name='loai_pb'
          type='select'
          options={[
            { label: '1. Số lượng', value: '1' },
            { label: '2. Thể tích', value: '2' },
            { label: '3. Khối lượng', value: '3' },
            { label: '4. Giá trị', value: '4' }
          ]}
          disabled={formMode === 'view'}
        />
        <div className='flex items-center'>
          <Label className='grid grid-cols-[160px,1fr] items-center'>Chứng từ</Label>
          <SearchField<ChungTu>
            className='grid grid-cols-[160px,1fr] items-center'
            columnDisplay='ma_ct'
            value={chungTu?.ma_ct || ''}
            displayRelatedField='ten_ct'
            onRowSelection={setMaCt}
            searchEndpoint={`/${QUERY_KEYS.CHUNG_TU}`}
            disabled={formMode === 'view'}
            relatedFieldValue={chungTu?.ten_ct || ''}
            searchColumns={chungTuSearchColumns}
            dialogTitle='Danh sách chứng từ'
          />
        </div>
        <FormField
          label='Trạng thái'
          className='grid grid-cols-[160px,1fr] items-center'
          name='status'
          type='select'
          options={[
            { label: '1. Đang sử dụng', value: '1' },
            { label: '0. Không sử dụng', value: '0' }
          ]}
          disabled={formMode === 'view'}
        />
      </div>
    </div>
  </div>
);
