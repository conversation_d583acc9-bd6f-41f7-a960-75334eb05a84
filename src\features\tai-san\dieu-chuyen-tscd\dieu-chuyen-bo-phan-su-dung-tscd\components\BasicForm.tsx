import { accountSearchColumns, boPhanSearchColumns, taiSanSearchColumns, QUERY_KEYS } from '@/constants';
import { AccountModel, TaiSanCoDinh, BoPhan } from '@/types/schemas';
import { FormField, SearchField } from '@/components/custom/arito';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface BasicFormProps {
  formMode: FormMode;
  assetCode: TaiSanCoDinh | null;
  setAssetCode: (account: TaiSanCoDinh) => void;
  ky: number | null;
  setKy: (ky: number) => void;
  nam: number | null;
  setNam: (nam: number) => void;
  boPhan: BoPhan | null;
  setBoPhan: (boPhan: BoPhan) => void;
  tkTs: AccountModel | null;
  setTkTs: (account: AccountModel) => void;
  tkKhauHao: AccountModel | null;
  setTkKhauHao: (account: AccountModel) => void;
  tkChiPhi: AccountModel | null;
  setTkChiPhi: (account: AccountModel) => void;
}

const BasicForm: React.FC<BasicFormProps> = ({
  formMode,
  assetCode,
  setAssetCode,
  ky,
  setKy,
  nam,
  setNam,
  boPhan,
  setBoPhan,
  tkTs,
  setTkTs,
  tkKhauHao,
  setTkKhauHao,
  tkChiPhi,
  setTkChiPhi
}) => {
  const isViewMode = formMode === 'view';

  return (
    <div className='max-h-[calc(100vh-150px)] space-y-3 overflow-y-auto p-4'>
      {/* Tài sản */}
      <div className='flex items-center'>
        <Label className='min-w-40'>Tài sản</Label>
        <SearchField<TaiSanCoDinh>
          type='text'
          displayRelatedField={'ten_ts'}
          columnDisplay={'ma_ts'}
          searchEndpoint={`/groups/?loai_nhom=TS`}
          searchColumns={taiSanSearchColumns}
          dialogTitle='Danh mục tài sản'
          value={assetCode?.ma_ts || ''}
          relatedFieldValue={assetCode?.ten_ts || ''}
          onRowSelection={setAssetCode}
          disabled={isViewMode}
        />
      </div>

      {/* Tên khác */}
      <div className='flex items-center'>
        <Label className='min-w-40'>Kỳ</Label>
        <FormField type='number' name='ky' disabled={isViewMode} />
      </div>

      {/* Tên khác */}
      <div className='flex items-center'>
        <Label className='min-w-40'>Năm</Label>
        <FormField type='number' name='nam' disabled={isViewMode} />
      </div>

      {/* Tài khoản */}
      <div className='flex items-center'>
        <Label className='min-w-40'>Bộ phận</Label>
        <SearchField<BoPhan>
          type='text'
          displayRelatedField={'ten_bp'}
          columnDisplay={'ma_bp'}
          searchEndpoint={`/${QUERY_KEYS.BO_PHAN}`}
          searchColumns={boPhanSearchColumns}
          dialogTitle='Danh mục tài khoản'
          value={boPhan?.ma_bp || ''}
          relatedFieldValue={boPhan?.ten_bp || ''}
          onRowSelection={setBoPhan}
          disabled={isViewMode}
        />
      </div>

      {/* Tk tài sản */}
      <div className='flex items-center'>
        <Label className='min-w-40'>Tk tài sản</Label>
        <SearchField<AccountModel>
          type='text'
          displayRelatedField={'name'}
          columnDisplay={'code'}
          searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
          searchColumns={accountSearchColumns}
          dialogTitle='Danh mục tài khoản'
          value={tkTs?.code || ''}
          relatedFieldValue={tkTs?.name || ''}
          onRowSelection={setTkTs}
          disabled={isViewMode}
        />
      </div>

      {/* Tk khấu hao */}
      <div className='flex items-center'>
        <Label className='min-w-40'>Tk khấu hao</Label>
        <SearchField<AccountModel>
          type='text'
          displayRelatedField={'name'}
          columnDisplay={'code'}
          searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
          searchColumns={accountSearchColumns}
          dialogTitle='Danh mục tài khoản'
          value={tkKhauHao?.code || ''}
          relatedFieldValue={tkKhauHao?.name || ''}
          onRowSelection={setTkKhauHao}
          disabled={isViewMode}
        />
      </div>

      {/* Tk chi phí */}
      <div className='flex items-center'>
        <Label className='min-w-40'>Tk chi phí</Label>
        <SearchField<AccountModel>
          type='text'
          displayRelatedField={'name'}
          columnDisplay={'code'}
          searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
          searchColumns={accountSearchColumns}
          dialogTitle='Danh mục tài khoản'
          value={tkChiPhi?.code || ''}
          relatedFieldValue={tkChiPhi?.name || ''}
          onRowSelection={setTkChiPhi}
          disabled={isViewMode}
        />
      </div>
    </div>
  );
};

export default BasicForm;
