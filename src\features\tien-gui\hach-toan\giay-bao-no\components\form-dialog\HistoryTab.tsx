import { AritoDataTables } from '@/components/custom/arito';
import { historyColumns } from '../../cols-definition';
import { useRows } from '@/hooks';

export const HistoryTab = () => {
  const { selectedRowIndex, handleRowClick } = useRows();

  const tables = [
    {
      name: '',
      rows: [],
      columns: historyColumns
    }
  ];

  return <AritoDataTables tables={tables} selectedRowId={selectedRowIndex || undefined} onRowClick={handleRowClick} />;
};
