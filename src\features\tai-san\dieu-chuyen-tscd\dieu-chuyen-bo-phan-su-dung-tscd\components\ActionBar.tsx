import { Co<PERSON>, Pencil, Plus, Trash, FileSearch, RefreshCw } from 'lucide-react';
import { AritoActionBar, AritoActionButton, AritoIcon, AritoMenuButton } from '@/components/custom/arito';

interface ActionBarProps {
  onAddIconClick: () => void;
  onEditIconClick: () => void;
  onDeleteIconClick: () => void;
  onCopyIconClick: () => void;
  onWatchIconClick: () => void;
  onRefreshClick?: () => void;
}

export const ActionBar: React.FC<ActionBarProps> = ({
  onAddIconClick,
  onEditIconClick,
  onDeleteIconClick,
  onCopyIconClick,
  onWatchIconClick,
  onRefreshClick
}) => {
  return (
    <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Danh mục loại TSCĐ, CCDC</h1>}>
      <AritoActionButton title='Thêm' icon={Plus} onClick={onAddIconClick} />
      <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditIconClick} />
      <AritoActionButton title='Xóa' icon={Trash} onClick={onDeleteIconClick} />
      <AritoActionButton title='Sao chép' icon={Copy} onClick={onCopyIconClick} />
      <AritoActionButton title='Xem' icon={FileSearch} onClick={onWatchIconClick} />
      <AritoActionButton title='Làm mới' icon={RefreshCw} onClick={onRefreshClick} />

      <AritoMenuButton
        items={[
          {
            title: 'Refresh',
            icon: <AritoIcon icon={15} />,
            onClick: onRefreshClick,
            group: 0
          },
          {
            title: 'Cố định cột',
            icon: <AritoIcon icon={16} />,
            onClick: () => {},
            group: 0
          },
          {
            title: 'Kết xuất dữ liệu',
            icon: <AritoIcon icon={555} />,
            onClick: () => {},
            group: 1
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
