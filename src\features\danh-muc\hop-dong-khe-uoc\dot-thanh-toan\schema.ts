import { z } from 'zod';

export const FormSchema = z.object({
  ma_dtt: z.string().nonempty('<PERSON><PERSON> đợt thanh toán là bắt buộc'),
  ten_dtt: z.string().nonempty('Tên đợt thanh toán là bắt buộc'),
  ten_dtt2: z.string().optional(),
  stt: z.coerce.number().optional(),
  status: z.string().nonempty('Trạng thái là bắt buộc')
});
export type FormValues = z.infer<typeof FormSchema>;

export const initialFormValues: FormValues = {
  ma_dtt: '',
  ten_dtt: '',
  ten_dtt2: '',
  stt: 0,
  status: '1'
};
