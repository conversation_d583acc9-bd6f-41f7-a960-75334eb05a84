import { z } from 'zod';

export const formSchema = z.object({
  ma_nguondon: z.string().nonempty('Mã nguồn đơn là bắt buộc'),
  ten_nguondon: z.string().nonempty('Tên nguồn đơn là bắt buộc'),
  ghi_chu: z.string().optional(),
  status: z.coerce.number().optional()
});

export type FormValues = z.infer<typeof formSchema>;

export const initialValues: FormValues = {
  ma_nguondon: '',
  ten_nguondon: '',
  ghi_chu: '',
  status: 1
};
