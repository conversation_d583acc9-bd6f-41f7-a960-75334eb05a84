import { Co<PERSON>, LogOut, <PERSON>cil, Pin, Plus, Refresh<PERSON>cw, Trash, Save } from 'lucide-react';
import React from 'react';
import { AritoActionButton } from '@/components/custom/arito';
import { FormMode } from '@/types/form';

/**
 * Get the title for the form dialog based on form mode and active tab
 */
export const getFormTitle = (formMode: FormMode, activeTab: string) => {
  if (formMode !== 'view') {
    return formMode === 'add' ? 'Mới' : 'Sửa';
  }

  switch (activeTab) {
    case 'info':
      return 'Giấy báo nợ';
    case 'history':
      return '<PERSON>ịch sử';
    default:
      return 'Gi<PERSON>y báo nợ';
  }
};

/**
 * Get the action buttons for the form dialog based on form mode and active tab
 */
export const getFormActionButtons = (
  formMode: FormMode,
  activeTab: string,
  handlers: {
    onAdd?: () => void;
    onEdit?: () => void;
    onDelete?: () => void;
    onCopy?: () => void;
    handleClose: () => void;
  }
) => {
  const { onAdd, onEdit, onDelete, onCopy, handleClose } = handlers;

  if (formMode !== 'view') {
    return null;
  }

  switch (activeTab) {
    case 'info':
      return (
        <>
          <AritoActionButton title='Thêm' icon={Plus} onClick={onAdd} />
          <AritoActionButton title='Sửa' icon={Pencil} onClick={onEdit} />
          <AritoActionButton title='Xóa' icon={Trash} onClick={onDelete} />
          <AritoActionButton title='Sao chép' icon={Copy} onClick={onCopy} />
          <AritoActionButton title='Đóng' icon={LogOut} onClick={handleClose} />
        </>
      );
    case 'history':
      return (
        <>
          <AritoActionButton
            title='Refresh'
            variant='secondary'
            icon={RefreshCcw}
            onClick={() => console.log('refresh')}
          />
          <AritoActionButton title='Cố định cột' variant='secondary' icon={Pin} onClick={() => console.log('pin')} />
          <AritoActionButton title='Đóng' variant='secondary' icon={LogOut} onClick={handleClose} />
        </>
      );
    default:
      return (
        <>
          <AritoActionButton title='Lưu' icon={Save} type='submit' />
          <AritoActionButton title='Đóng' icon={LogOut} onClick={handleClose} />
        </>
      );
  }
};
