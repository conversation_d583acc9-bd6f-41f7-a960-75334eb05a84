'use client';

import { useState, useEffect, useMemo } from 'react';
import { getFormTitle, getFormActionButtons, calculateTotals } from '../../utils';
import { AritoHeaderTabs, AritoForm } from '@/components/custom/arito';
import { useDetailRows, useTaxRows } from './hooks';
import { PaymentInfoTab } from './PaymentInfoTab';
import { ConfirmDialog } from '../../components';
import { useFormFieldState } from '../../hooks';
import { BasicInfoTab } from './BasicInfoTab';
import { initialValues } from '../../schema';
import { HistoryTab } from './HistoryTab';
import { FormMode } from '@/types/form';
import { BottomBar } from './BottomBar';
import { DetailTab } from './DetailTab';
import { OtherTab } from './OtherTab';
import { RateTab } from './RateTab';
import { TaxTab } from './TaxTab';

interface FormDialogProps {
  formMode: FormMode;
  open: boolean;
  initialData?: any;
  onSubmit?: (data: any) => void;
  onClose: () => void;
  onAdd?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onCopy?: () => void;
}

const FormDialog = ({
  open,
  formMode,
  initialData,
  onClose,
  onSubmit,
  onAdd,
  onEdit,
  onDelete,
  onCopy
}: FormDialogProps) => {
  const [isConfirm, setIsConfirm] = useState<boolean>(false);
  const [isFormDirty, setIsFormDirty] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>('info');

  const {
    rows: detailRows,
    selectedRowUuid: detailSelectedRowUuid,
    handleRowClick: detailHandleRowClick,
    handleAddRow: detailHandleAddRow,
    handleDeleteRow: detailHandleDeleteRow,
    handleCopyRow: detailHandleCopyRow,
    handlePasteRow: detailHandlePasteRow,
    handleMoveRow: detailHandleMoveRow,
    handleCellValueChange: detailHandleCellValueChange
  } = useDetailRows(initialData?.chi_tiet || []);
  const {
    rows: taxRows,
    selectedRowUuid: taxSelectedRowUuid,
    handleRowClick: taxHandleRowClick,
    handleAddRow: taxHandleAddRow,
    handleDeleteRow: taxHandleDeleteRow,
    handleCopyRow: taxHandleCopyRow,
    handlePasteRow: taxHandlePasteRow,
    handleMoveRow: taxHandleMoveRow,
    handleCellValueChange: taxHandleCellValueChange
  } = useTaxRows(initialData?.thue || []);
  const { state, actions } = useFormFieldState(initialData);

  useEffect(() => {
    if (!open) {
      setIsFormDirty(false);
      setActiveTab('info');
    }
  }, [open]);

  const detail = useMemo(() => {
    return initialData?.chi_tiet || detailRows;
  }, [initialData, detailRows]);

  const { tongTien, tongThue, tongThanhToan } = useMemo(() => {
    return calculateTotals(detail);
  }, [detail]);

  const handleSubmit = (data: any) => {
    // Lấy ma_kh từ dòng đầu tiên trong chi_tiet (nếu có)
    const firstDetailRow = detailRows[0];
    const maKhFromDetail = firstDetailRow?.ma_kh_data?.uuid || '';

    // Tạo đối tượng requestBody map đúng với mẫu chuẩn
    const requestBody = {
      // ===== THÔNG TIN CƠ BẢN =====
      ma_ngv: data.ma_ngv || '1', // Label Loại phiếu chi
      dia_chi: data.dia_chi || '', // Địa chỉ
      ong_ba: data.ong_ba || '', // Người nhận tiền
      dien_giai: data.dien_giai || '', // Diễn giải
      tk: state.taiKhoan?.uuid || '', // Tài khoản có (uuid)
      unit_id: data.unit_id || '', // Đơn vị (uuid)

      // ===== THÔNG TIN CHỨNG TỪ =====
      i_so_ct: '', // Số chứng từ nội bộ
      ma_nk: state.chungTu?.uuid || '', // Mã quyển chứng từ (uuid)
      so_ct: data.so_ct || '', // Số chứng từ (uuid) backend auto generate
      so_ct0: data.so_ct0 || '', // Số chứng từ (uuid) xử lý sau
      so_ct_goc: data.so_ct_goc || 0, // Label Kèm theo (Tab Khác) xử lý sau
      dien_giai_ct_goc: data.dien_giai_ct_goc || '', // Diễn giải chứng từ gốc (max 255 chars)
      ngay_ct: data.ngay_ct || '', // Ngày chứng từ
      ngay_lct: data.ngay_lct || '', // Ngày lập chứng từ
      ngay_ct0: data.ngay_ct0 || '', // Ngày chứng từ (Tab Thông tin thanh toán)

      // ===== THÔNG TIN TIỀN TỆ =====
      ma_nt: data.ma_nt || '', // Ngoại tệ (uuid)
      ty_gia: parseFloat(data.ty_gia) || 1.0, // Tỷ giá
      t_tien_nt: parseFloat(data.t_tien_nt) || 0, // Tổng tiền
      t_tien: parseFloat(data.t_tien) || 0, // Bằng tổng tiền
      t_thue_nt: 0, // Tổng thuế - sẽ tính từ taxRows
      t_thue: 0, // Bằng Tổng thuế - sẽ tính từ taxRows

      // ===== TRẠNG THÁI VÀ CỜ =====
      status: data.status || 'draft', // Trạng thái
      transfer_yn: Boolean(data.transfer_yn), // Dữ liệu được nhận
      hd_yn: Boolean(data.hd_yn), // checkbox theo dõi thanh toán
      tg_dd: Boolean(data.tg_dd), // Sửa tỷ giá ghi sổ (Checkbox)
      cltg_yn: Boolean(data.cltg_yn), // Tạo chênh lệch tỷ giá ngay

      // ===== THÔNG TIN KHÁCH HÀNG VÀ THANH TOÁN =====
      ma_kh: maKhFromDetail || data.ma_kh || '', // Mã đối tượng (uuid) - lấy từ dòng đầu tiên chi_tiet
      ma_tt: data.ma_tt || '', // Mã thanh toán (uuid)

      // ===== THÔNG TIN TIẾN TRÌNH - CÁC TRƯỜNG BỊ THIẾU =====
      id: 0,
      id_progress: 0,
      xprogress: '',
      xdatetime2: '',

      // ===== THÔNG TIN KHÁC =====
      xfile: '', // File đính kèm (max 255 chars)
      created_by: 'admin', // Người tạo (max 50 chars, default: "admin")

      // ===== MẢNG CHI TIẾT =====
      chi_tiet: detailRows.map((row: any, index: number) => ({
        line: index + 1, // Số thứ tự hàng
        dien_giai: row.dien_giai || '', // Diễn giải
        ma_kh: row.ma_kh_data?.uuid || '', // Mã đối tượng (uuid)
        tk_no: row.tk_no_data?.uuid || '', // TK nợ (uuid)
        tien_nt: parseFloat(row.tien_nt) || 0, // Tiền VND
        tien: parseFloat(row.tien) || 0, // Bằng tiền VND
        ma_loai_hd: row.ma_loai_hd || '', // Cột loại hoá đơn (Chỉ áp dụng khi ma_ngv = 2)
        ma_thue: row.ma_thue_detail || row.ma_thue || '', // Mã thuế
        ten_thue: row.ten_thue || '', // Tên thuế
        thue_suat: parseFloat(row.thue_suat_detail || row.thue_suat) || 0, // Thuế suất
        thue_nt: parseFloat(row.thue_nt_detail || row.thue_nt) || 0, // Thuế ngoại tệ
        thue: parseFloat(row.thue_nt_detail || row.thue) || 0, // Thuế
        ghi_chu: row.ghi_chu || '', // Ghi chú chi tiết
        ma_bp: row.ma_bp_data?.uuid || '', // Mã bộ phận (uuid)
        ma_vv: row.ma_vv_data?.uuid || '', // Mã vụ việc (uuid)
        ma_sp: row.ma_sp_data?.uuid || '', // Mã sản phẩm (uuid)
        ma_lsx: row.ma_lsx_data?.ma_lsx || '', // Mã lệnh sản xuất
        // Thông tin hoá đơn chi tiết (khi ma_loai_hd khác '0')
        tk_thue: row.tk_thue_detail_data?.uuid || '', // Tài khoản thuế
        so_ct0: row.so_ct0_detail || '', // Số hoá đơn
        so_ct2: row.so_ct2_detail || '', // Ký hiệu
        ngay_ct0: row.ngay_ct0_detail || '', // Ngày hoá đơn
        ma_mau_ct: row.ma_mau_ct_detail_data?.uuid || '', // Mẫu HĐ
        ma_mau_bc: row.ma_mau_bc_detail || '', // Mẫu báo cáo
        ma_tc_thue: row.ma_tc_thue_detail || '', // Mã tính chất
        ma_kh_thue: row.ma_kh_thue_detail_data?.uuid || '', // Mã ncc
        ten_kh_thue: row.ten_kh_thue_detail || '', // Tên nhà cung cấp
        dia_chi: row.dia_chi_detail || '', // Địa chỉ
        ma_so_thue: row.ma_so_thue_detail || '', // Mã số thuế
        ten_vt_thue: row.ten_vt_thue_detail || '', // Tên hàng hoá- dịch vụ
        ma_kh9: row.ma_kh9_detail_data?.uuid || '' // Cục thuế
      })),

      // ===== MẢNG THUẾ =====
      thue: taxRows.map((row: any) => ({
        ma_thue: row.ma_thue || '', // Mã thuế
        ten_thue: row.ten_thue || '', // Tên thuế
        thue_suat: parseFloat(row.thue_suat) || 0, // Thuế suất
        tien_thue_nt: parseFloat(row.t_thue_nt) || 0, // Tiền thuế ngoại tệ
        tien_thue: parseFloat(row.t_thue_nt) || 0, // Tiền thuế (same as nt for VND)
        tk_thue: row.tk_thue_no_data?.uuid || '', // Tài khoản thuế (uuid)
        ghi_chu: row.ghi_chu || '' // Ghi chú thuế
      }))
    };

    // Tính tổng thuế từ taxRows
    const totalTaxNt = taxRows.reduce((sum: number, row: any) => sum + (parseFloat(row.t_thue_nt) || 0), 0);
    const totalTax = taxRows.reduce((sum: number, row: any) => sum + (parseFloat(row.t_thue_nt) || 0), 0);

    requestBody.t_thue_nt = totalTaxNt;
    requestBody.t_thue = totalTax;

    onSubmit?.(requestBody);
    setIsFormDirty(false);
    onClose();
  };

  const handleClose = () => {
    if (isFormDirty && formMode !== 'view') {
      setIsConfirm(true);
    } else {
      onClose();
    }
  };

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  const title = getFormTitle(formMode, activeTab);
  const actionButtons = getFormActionButtons(formMode, activeTab, {
    onAdd,
    onEdit,
    onDelete,
    onCopy,
    handleClose
  });

  return (
    <>
      <AritoForm
        mode={formMode}
        initialData={initialData || initialValues}
        title={title}
        actionButtons={actionButtons}
        subTitle='Phiếu chi tiền'
        onSubmit={handleSubmit}
        onClose={handleClose}
        headerFields={
          <div onChange={() => setIsFormDirty(true)}>
            <AritoHeaderTabs
              tabs={[
                {
                  id: 'info',
                  label: 'Thông tin',
                  component: <BasicInfoTab formMode={formMode} formState={{ state, actions }} />
                },
                ...(formMode === 'view'
                  ? [
                      {
                        id: 'history',
                        label: 'Lịch sử',
                        component: <HistoryTab />
                      }
                    ]
                  : [])
              ]}
              onTabChange={handleTabChange}
              defaultTabIndex={activeTab === 'info' ? 0 : activeTab === 'history' ? 1 : 0}
            />
          </div>
        }
        tabs={
          activeTab === 'info' && [
            {
              id: 'details',
              label: 'Chi tiết',
              component: (
                <DetailTab
                  formMode={formMode}
                  rows={detailRows}
                  selectedRowUuid={detailSelectedRowUuid}
                  onRowClick={detailHandleRowClick}
                  onAddRow={detailHandleAddRow}
                  onDeleteRow={detailHandleDeleteRow}
                  onCopyRow={detailHandleCopyRow}
                  onPasteRow={detailHandlePasteRow}
                  onMoveRow={detailHandleMoveRow}
                  onCellValueChange={detailHandleCellValueChange}
                />
              )
            },
            ...(state.loaiPhieuChi !== '4'
              ? [
                  {
                    id: 'tax',
                    label: 'Thuế',
                    component: (
                      <TaxTab
                        formMode={formMode}
                        rows={taxRows}
                        selectedRowUuid={taxSelectedRowUuid}
                        onRowClick={taxHandleRowClick}
                        onAddRow={taxHandleAddRow}
                        onDeleteRow={taxHandleDeleteRow}
                        onCopyRow={taxHandleCopyRow}
                        onPasteRow={taxHandlePasteRow}
                        onMoveRow={taxHandleMoveRow}
                        onCellValueChange={taxHandleCellValueChange}
                      />
                    )
                  },
                  {
                    id: 'payment',
                    label: 'Thanh toán',
                    component: <PaymentInfoTab formMode={formMode} formState={{ state, actions }} />
                  }
                ]
              : []),
            {
              id: 'rate',
              label: 'Tỷ giá',
              component: <RateTab formMode={formMode} />
            },
            {
              id: 'other',
              label: 'Khác',
              component: <OtherTab formMode={formMode} />
            }
          ]
        }
        bottomBar={
          activeTab === 'info' && <BottomBar tongTien={tongTien} tongThue={tongThue} tongThanhToan={tongThanhToan} />
        }
      />

      {isConfirm && (
        <ConfirmDialog
          open={isConfirm}
          onClose={() => setIsConfirm(false)}
          onConfirm={() => {
            setIsConfirm(false);
            setIsFormDirty(false);
            onClose();
          }}
          title='Cảnh báo'
          content='Bạn muốn kết thúc?'
        />
      )}
    </>
  );
};

export default FormDialog;
