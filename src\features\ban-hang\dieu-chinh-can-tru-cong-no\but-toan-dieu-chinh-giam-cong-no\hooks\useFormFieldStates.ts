import { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, QuyenChungTu } from '@/types/schemas';

export interface InfoTabFormFieldStates {
  taiKhoan: Tai<PERSON>hoan | null;
  setTaiKhoan: (taiKhoan: <PERSON><PERSON><PERSON><PERSON> | null) => void;
  quyenChungTu: QuyenChungTu | null;
  setQuyenChungTu: (quyenChungTu: QuyenChungTu | null) => void;
}

/**
 * Hook for managing search field states in the hoa-don-ban-hang form-dialog
 * Following the pattern from useSearchDialogState
 *
 * @returns Object with states and setters for search fields
 */
export const useFormFieldStates = (initialData?: any) => {
  const [taiKhoan, setTaiKhoan] = useState<TaiKhoan | null>(initialData?.tk_data || null);

  const [quyenChungTu, setQuyenChungTu] = useState<QuyenChungTu | null>(initialData?.ma_nk_data || null);

  // Reset all states
  const resetAllStates = () => {
    setTaiKhoan(null);
    setQuyenChungTu(null);
  };

  // Update multiple states at once
  const updateStates = (updates: { taiKhoan?: TaiKhoan | null; quyenChungTu?: QuyenChungTu | null }) => {
    if (updates.taiKhoan !== undefined) setTaiKhoan(updates.taiKhoan);
    if (updates.quyenChungTu !== undefined) setQuyenChungTu(updates.quyenChungTu);
  };

  return {
    // States
    taiKhoan,
    quyenChungTu,

    // Setters
    setTaiKhoan,
    setQuyenChungTu,

    // Utility functions
    resetAllStates,
    updateStates
  };
};
