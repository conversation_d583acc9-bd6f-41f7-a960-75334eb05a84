import React, { useState } from 'react';
import { ChiPhiMuaHang, ChiPhiMuaHangInput } from '@/types/schemas/chi-phi-mua-hang.type';
import { AritoDialog, BottomBar, AritoIcon } from '@/components/custom/arito';
import { chiPhiMuaHangSchema, initialValues } from '../schema';
import { AritoForm } from '@/components/custom/arito';
import { ChungTu } from '@/types/schemas';
import { BasicInfo } from './BasicInfo';

interface FormDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: ChiPhiMuaHangInput) => void;
  formMode: 'add' | 'edit' | 'view';
  initialData?: any;
  onAdd?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onCopy?: () => void;
}

export const FormDialog: React.FC<FormDialogProps> = ({
  open,
  onClose,
  onSubmit,
  formMode,
  initialData,
  onAdd,
  onEdit,
  onDelete,
  onCopy
}) => {
  const [chungTu, setChungTu] = useState<ChungTu | null>(initialData?.ma_ct_data || null);

  const handleSubmit = async (data: any) => {
    try {
      const input: ChiPhiMuaHangInput = {
        ...data,
        ma_ct: chungTu?.uuid
      };
      onSubmit(input);
    } catch (err: any) {
      console.error('Error in handleSubmit:', err);
    }
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title={'Chi phí mua hàng'}
      maxWidth='md'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
      titleIcon={<AritoIcon icon={699} />}
    >
      <AritoForm<ChiPhiMuaHang>
        onClose={onClose}
        mode={formMode}
        schema={chiPhiMuaHangSchema}
        title={formMode === 'add' ? 'Mới' : formMode === 'edit' ? 'Sửa chi phí mua hàng' : 'Xem chi phí mua hàng'}
        initialData={initialData || initialValues}
        onSubmit={handleSubmit}
        className='w-full min-w-[800px]'
        headerFields={<BasicInfo formMode={formMode} chungTu={chungTu} setMaCt={setChungTu} />}
        hasAritoActionBar={false}
        classNameBottomBar='relative w-full flex justify-end gap-2'
        bottomBar={
          <BottomBar
            mode={formMode}
            onAdd={onAdd}
            onEdit={onEdit}
            onDelete={onDelete}
            onCopy={onCopy}
            onClose={onClose}
          />
        }
      />
    </AritoDialog>
  );
};

export default FormDialog;
