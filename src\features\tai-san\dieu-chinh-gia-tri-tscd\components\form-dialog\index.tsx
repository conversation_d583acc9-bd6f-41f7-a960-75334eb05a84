import React, { useState } from 'react';
import { BottomBar, AritoIcon, AritoForm, AritoDialog } from '@/components/custom/arito';
import { DieuChinhGiaTriTSCD, DieuChinhGiaTriTSCDInput } from '@/types/schemas';
import { formSchema, FormValues, initialFormValues } from '../../schemas';
import { useSearchFieldStates } from '../../hooks';
import ConfirmDialog from './ConfirmDialog';
import { FormMode } from '@/types/form';
import BasicInfo from './BasicInfo';

interface FormDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: DieuChinhGiaTriTSCDInput) => void;
  mode: FormMode;
  initialData?: DieuChinhGiaTriTSCD;
}

function FormDialog({ open, onClose, onSubmit, mode, initialData }: FormDialogProps) {
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [error, setError] = useState<string>('');
  const searchFieldStates = useSearchFieldStates(initialData);

  const handleSubmit = (data: FormValues) => {
    // Validate required fields
    if (!searchFieldStates.asset?.uuid || !searchFieldStates.reason?.uuid) {
      setError('Mã tài sản và lý do không được để trống');
      return;
    }

    // Clear error if validation passes
    setError('');

    const combinedData: DieuChinhGiaTriTSCDInput = {
      ...data,
      ma_ts: searchFieldStates.asset.uuid,
      ma_tg_ts: searchFieldStates.reason.uuid
    };
    onSubmit(combinedData);
    onClose();
  };

  const handleCloseDialog = () => {
    setShowConfirmDialog(false);
    onClose();
  };

  const getDialogTitle = () => {
    switch (mode) {
      case 'add':
        return 'Mới';
      case 'edit':
        return 'Sửa';
      case 'view':
        return 'Xem';
      default:
        return 'Mới';
    }
  };

  return (
    <>
      <AritoDialog
        open={open}
        onClose={onClose}
        title={getDialogTitle()}
        maxWidth='lg'
        disableBackdropClose={true}
        disableEscapeKeyDown={true}
        titleIcon={<AritoIcon icon={12} />}
      >
        <AritoForm
          mode={mode}
          hasAritoActionBar={false}
          schema={formSchema}
          onSubmit={handleSubmit}
          initialData={initialData || initialFormValues}
          className='w-full md:min-w-[500px] lg:min-w-[600px]'
          headerFields={<BasicInfo formMode={mode} searchFieldStates={searchFieldStates} />}
          classNameBottomBar='relative w-full flex justify-end gap-2'
          bottomBar={
            <>
              {error && <div className='mb-4 mt-2 bg-red-100 p-2 text-red-700'>{error}</div>}
              <div>
                <BottomBar mode={mode} onClose={onClose} />
              </div>
            </>
          }
        />
      </AritoDialog>

      <ConfirmDialog
        onClose={handleCloseDialog}
        open={showConfirmDialog}
        onCloseConfirmDialog={() => setShowConfirmDialog(false)}
      />
    </>
  );
}

export default FormDialog;
