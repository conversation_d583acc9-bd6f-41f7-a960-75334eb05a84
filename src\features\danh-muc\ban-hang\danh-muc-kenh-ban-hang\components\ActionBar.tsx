import { <PERSON><PERSON>, Pencil, Plus, Trash, FileSearch } from 'lucide-react';
import { AritoActionButton, AritoMenuButton, AritoActionBar, AritoIcon } from '@/components/custom/arito';

interface ActionBarProps {
  onAddIconClick: () => void;
  onEditIconClick: () => void;
  onDeleteIconClick: () => void;
  onCopyIconClick: () => void;
  onWatchIconClick: () => void;
  hasRowSelected?: boolean;
}

export const ActionBar: React.FC<ActionBarProps> = ({
  onAddIconClick,
  onEditIconClick,
  onDeleteIconClick,
  onCopyIconClick,
  onWatchIconClick,
  hasRowSelected = false
}) => {
  return (
    <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Danh mục kênh bán hàng</h1>}>
      <AritoActionButton title='Thêm' icon={Plus} onClick={onAddIconClick} variant='primary' />
      <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditIconClick} disabled={!hasRowSelected} />
      <AritoActionButton title='Xóa' icon={Trash} onClick={onDeleteIconClick} disabled={!hasRowSelected} />
      <AritoActionButton title='Sao chép' icon={Copy} onClick={onCopyIconClick} disabled={!hasRowSelected} />
      <AritoActionButton title='Xem' icon={FileSearch} onClick={onWatchIconClick} disabled={!hasRowSelected} />

      <AritoMenuButton
        items={[
          {
            title: 'Refresh',
            icon: <AritoIcon icon={15} />,
            onClick: () => {},
            group: 1
          },
          {
            title: 'Cố định cột',
            icon: <AritoIcon icon={16} />,
            onClick: () => {},
            group: 1
          },
          {
            title: 'Kết xuất dữ liệu',
            icon: <AritoIcon icon={18} />,
            onClick: () => {},
            group: 2
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
