import { <PERSON><PERSON>, Pencil, Plus, RefreshCw, Trash, Search } from 'lucide-react';
import { AritoActionButton, AritoMenuButton, AritoActionBar, AritoIcon } from '@/components/custom/arito';

interface ActionBarProps {
  onAdd?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onCopy?: () => void;
  onSearch?: () => void;
  onRefresh?: () => void;
  isViewDisabled?: boolean;
}

export default function ActionBar({
  onAdd,
  onEdit,
  onDelete,
  onCopy,
  onSearch,
  onRefresh,
  isViewDisabled
}: ActionBarProps) {
  return (
    <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Bút toán điều chỉnh giảm công nợ</h1>}>
      <AritoActionButton title='Thêm' icon={Plus} onClick={onAdd} variant='primary' />
      <AritoActionButton title='Sửa' icon={Pencil} onClick={onEdit} disabled={isViewDisabled} />
      <AritoActionButton title='Xóa' icon={Trash} onClick={onDelete} disabled={isViewDisabled} />
      <AritoActionButton title='Sao chép' icon={Copy} onClick={onCopy} disabled={isViewDisabled} />
      <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearch} />

      <AritoMenuButton
        items={[
          {
            title: 'Refresh',
            icon: <AritoIcon icon={15} />,
            onClick: onRefresh,
            group: 0
          },
          {
            title: 'Cố định cột',
            icon: <AritoIcon icon={16} />,
            onClick: () => {},
            group: 0
          },
          {
            title: 'In nhiều',
            icon: <AritoIcon icon={17} />,
            onClick: () => {},
            group: 1
          },
          {
            title: 'Kết xuất dữ liệu',
            icon: <AritoIcon icon={555} />,
            onClick: () => {},
            group: 1
          },
          {
            title: 'Tải mẫu Excel',
            icon: <AritoIcon icon={28} />,
            onClick: () => {},
            group: 2
          },
          {
            title: 'Lấy dữ liệu từ Excel',
            icon: <AritoIcon icon={29} />,
            onClick: () => {},
            group: 2
          }
        ]}
      />
    </AritoActionBar>
  );
}
