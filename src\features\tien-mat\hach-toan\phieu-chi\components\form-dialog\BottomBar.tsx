import { FormField } from '@/components/custom/arito';
import { Label } from '@/components/ui/label';

interface BottomBarProps {
  tongTien: number;
  tongThue: number;
  tongThanhToan: number;
}

export function BottomBar({ tongTien, tongThue, tongThanhToan }: BottomBarProps) {
  return (
    <div className='w-full border-t bg-white px-4'>
      <div className='flex flex-col'>
        {/* Column 1 */}
        <div className='flex items-center'>
          <Label className='w-32 font-medium'>Tổng tiền</Label>
          <div className='flex w-[250px] gap-2'>
            <FormField name='t_tien_nt' type='text' disabled value={tongTien.toLocaleString('vi-VN')} />
            <FormField name='t_tien' type='text' disabled value={tongTien.toLocaleString('vi-VN')} className='hidden' />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-32 font-medium'>Tổng thuế</Label>
          <div className='flex w-[250px] gap-2'>
            <FormField name='t_thue_nt' type='text' disabled value={tongThue.toLocaleString('vi-VN')} />
            <FormField name='t_thue' type='text' disabled value={tongThue.toLocaleString('vi-VN')} className='hidden' />
          </div>
        </div>

        <div className='flex items-center pb-2'>
          <Label className='w-32 font-medium'>Tổng thanh toán</Label>
          <div className='flex w-[250px] gap-2'>
            <FormField name='t_tt_nt' type='text' disabled value={tongThanhToan.toLocaleString('vi-VN')} />
            <FormField
              name='t_tt'
              type='text'
              disabled
              value={tongThanhToan.toLocaleString('vi-VN')}
              className='hidden'
            />
          </div>
        </div>
      </div>
    </div>
  );
}
