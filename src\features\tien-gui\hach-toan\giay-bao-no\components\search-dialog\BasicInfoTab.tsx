import React from 'react';
import { QUERY_KEYS, accountSearchColumns, khachHangSearchColumns, nhanVienSearchColumns } from '@/constants';
import { DateRangeField, DocumentNumberRangeField } from '@/components/custom/arito/form/search-fields';
import { AccountModel, KhachHang, NhanVien } from '@/types/schemas';
import { SearchField, FormField } from '@/components/custom/arito';
import { Label } from '@/components/ui/label';

interface BasicInfoTabProps {
  formMode: 'add' | 'edit' | 'view';
}

const BasicInfoTab = ({ formMode }: BasicInfoTabProps) => {
  return (
    <div className='p-4'>
      {/* Ngày c/từ (từ/đến) */}
      <DateRangeField label='Ngày c/từ (từ/đến)' fromDateName='ngay_ct1' toDateName='ngay_ct2' />

      {/* <PERSON><PERSON> c/từ (từ/đến) */}
      <DocumentNumberRangeField formMode={formMode} label='Số c/từ (từ/đến)' fromName='so_ct1' toName='so_ct2' />

      {/* Loại hóa đơn */}
      <div className='flex items-center'>
        <Label className='w-[150px]'>Loại xuất</Label>
        <FormField
          name='ma_ngv'
          type='select'
          className='w-[200px]'
          label=''
          options={[
            { value: '', label: 'Tất cả' },
            { value: '3', label: '3. Hóa đơn kiêm phiếu xuất' },
            { value: '1', label: '1. Hóa đơn' }
          ]}
          disabled={formMode === 'view'}
        />
      </div>

      <div className='flex items-center'>
        <Label className='w-[150px]'>Giao dịch</Label>
        <FormField
          name='ma_gd'
          type='select'
          className='w-[200px]'
          label=''
          options={[
            { value: '', label: 'Tất cả' },
            { value: 'BH', label: 'BH. Bán hàng ngoài' },
            { value: 'NB', label: 'NB. Bán nội bộ' },
            { value: 'XK', label: 'XK. Khác' },
            { value: 'PO', label: 'PO. Bán hàng POS' }
          ]}
          disabled={formMode === 'view'}
        />
      </div>

      {/* Mã khách hàng */}
      <div className='flex items-center'>
        <Label className='w-[150px]'>Mã khách hàng</Label>
        <SearchField<KhachHang>
          type='text'
          name='ma_kh'
          searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
          searchColumns={khachHangSearchColumns}
          columnDisplay='customer_code'
          displayRelatedField='customer_name'
          dialogTitle='Danh mục đối tượng'
          className='w-[205px]'
        />
      </div>

      {/* Tài khoản nợ */}
      <div className='flex items-center'>
        <Label className='w-[150px]'>Tài khoản nợ</Label>
        <SearchField<AccountModel>
          type='text'
          name='tk'
          searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
          searchColumns={accountSearchColumns}
          columnDisplay='code'
          displayRelatedField='name'
          dialogTitle='Danh mục tài khoản'
          className='w-[205px]'
        />
      </div>

      {/* Mã nhân viên */}
      <div className='flex items-center'>
        <Label className='w-[150px]'>Mã nhân viên</Label>
        <SearchField<NhanVien>
          type='text'
          name='ma_nvbh'
          searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
          searchColumns={nhanVienSearchColumns}
          columnDisplay='ma_nhan_vien'
          displayRelatedField='ho_ten_nhan_vien'
          dialogTitle='Danh mục nhân viên bán hàng'
          className='w-[205px]'
        />
      </div>

      {/* Diễn giải */}
      <div className='flex items-center'>
        <Label className='w-[150px]'>Diễn giải</Label>
        <div className='w-full'>
          <FormField name='dien_giai' type='text' disabled={formMode === 'view'} />
        </div>
      </div>
    </div>
  );
};

export default BasicInfoTab;
