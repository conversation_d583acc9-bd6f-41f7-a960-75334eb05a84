import { z } from 'zod';

export const formSchema = z.object({
  ma_ptgh: z.string().nonempty('<PERSON><PERSON> phương thức là bắt buộc'),
  ten_ptgh: z.string().nonempty('<PERSON>ê<PERSON> phương thức là bắt buộc'),
  ten_ptgh2: z.string().optional(),
  status: z.string().optional()
});

export type FormValues = z.infer<typeof formSchema>;

export const initialValues: FormValues = {
  ma_ptgh: '',
  ten_ptgh: '',
  ten_ptgh2: '',
  status: '1'
};
