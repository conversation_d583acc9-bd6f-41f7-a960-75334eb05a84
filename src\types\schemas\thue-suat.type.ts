import type { ApiResponse } from '../api.type';

export interface TaxRate {
  /**
   * Unique identifier (UUID)
   */
  uuid: string;

  /**
   * Tax code
   */
  ma_thue: string;

  /**
   * Tax name
   */
  ten_thue: string;

  /**
   * Alternative tax name
   */
  ten_thue2: string;

  /**
   * Tax rate
   */
  thue_suat: number;

  /**
   * Tax rate used in electronic invoices
   */
  thue_suat_hddt: number;

  /**
   * Tax group
   */
  nhom_thue: string;
  nhom_thue_data?: string;

  /**
   * Output tax account
   */
  tk_thue_dau_ra: string;
  tk_thue_dau_ra_data?: string;

  /**
   * Input tax account
   */
  tk_thue_dau_vao: string;
  tk_thue_dau_vao_data?: string;

  /**
   * Input tax account for deductible tax
   */
  tk_thue_dau_vao_duoc_gia: string;

  tk_thue_dau_vao_duoc_gia_data?: string;

  tk_thue_dau_ra_duoc_gia: string;
  tk_thue_dau_ra_duoc_gia_data?: string;

  /**
   * Order number
   */
  stt: number;

  /**
   * Tax type
   */
  loai_thue: number;

  /**
   * Tax status
   */
  status: number;

  /**
   * Timestamp of creation
   */
  created: string;

  /**
   * Timestamp of last update
   */
  updated: string;
}

/**
 * Type for TaxRate API response
 */
export type TaxRateResponse = ApiResponse<TaxRate>;

/**
 * Type for creating or updating a TaxRate
 */
export interface TaxRateInput {
  ma_thue: string;
  ten_thue: string;
  ten_thue2: string;
  thue_suat: number;
  thue_suat_hddt: number;
  stt: number;
  loai_thue: number;
  nhom_thue: string;
  tk_thue_dau_ra: string;
  tk_thue_dau_vao: string;
  tk_thue_dau_vao_duoc_gia: string;
  tk_thue_dau_ra_duoc_gia: string;
  status: number;
}
