import { ChangeEvent, useRef, useState } from 'react';
import Image from 'next/image';
import { FormMode } from '@/types/form';

interface OtherTabProps {
  formMode: FormMode;
  onFileChange?: (file: File | null) => void;
}

export default function OtherTab({ formMode, onFileChange }: OtherTabProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setSelectedFile(file);
    onFileChange?.(file);
  };

  const handleClick = () => {
    if (formMode !== 'view' && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className='h-screen p-4'>
      <div className='flex items-center gap-4'>
        <label className='min-w-[100px] text-sm font-medium'>Chọn file</label>
        <div className='flex items-center gap-2'>
          <Image
            src='/images/ic_file_upload.png'
            alt='Upload'
            width={16}
            height={16}
            className='cursor-pointer object-contain'
            onClick={handleClick}
          />
          <div className='flex h-[30px] min-w-[200px] items-center border-b border-gray-200 px-2 py-1'>
            {selectedFile && <span className='truncate text-sm text-gray-600'>{selectedFile.name}</span>}
          </div>
          <input
            ref={fileInputRef}
            type='file'
            className='hidden'
            onChange={handleFileChange}
            disabled={formMode === 'view'}
          />
        </div>
      </div>
    </div>
  );
}
