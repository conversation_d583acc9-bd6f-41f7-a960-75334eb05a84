import { useState } from 'react';
import { AritoDialog, AritoForm, AritoIcon, BottomBar } from '@/components/custom/arito';
import ConfirmationDialog from '@/components/custom/arito/dialog/confirm-dialog';
import { Account<PERSON>odel, BoPhan, TaiSanCoDinh } from '@/types/schemas';
import { formSchema, initialValues } from '../schemas';
import BasicFormProps from './BasicForm';
import { FormMode } from '@/types/form';

interface FormDialogProps {
  mode: FormMode;
  open: boolean;
  onClose: () => void;
  onSubmit?: (data: any) => void;
  onAddButtonClick?: () => void;
  onEditButtonClick?: () => void;
  onDeleteButtonClick?: () => void;
  onCopyButtonClick?: () => void;
  onWatchButtonClick?: () => void;

  initialData: any;
}
function FormDialog({
  mode,
  open,
  onClose,
  onAddButtonClick,
  onCopyButtonClick,
  onDeleteButtonClick,
  onEditButtonClick,
  initialData,
  onSubmit
}: FormDialogProps) {
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  // State management for search fields
  const [assetCode, setAssetCode] = useState<TaiSanCoDinh | null>(initialData?.ma_tai_san || null);
  const [ky, setKy] = useState<number | null>(initialData?.ky || null);
  const [nam, setNam] = useState<number | null>(initialData?.nam || null);
  const [boPhan, setBoPhan] = useState<BoPhan | null>(initialData?.bo_phan || null);
  const [tkTs, setTkTs] = useState<AccountModel | null>(initialData?.tk_ts || null);
  const [tkKhauHao, setTkKhauHao] = useState<AccountModel | null>(initialData?.tk_khau_hao || null);
  const [tkChiPhi, setTkChiPhi] = useState<AccountModel | null>(initialData?.tk_chi_phi || null);

  const handleSubmit = async (data: any) => {
    try {
      const formData = {
        ...data,
        ma_ts: assetCode?.uuid || '',
        ky: ky || '',
        nam: nam || '',
        ma_bp: boPhan?.uuid || '',
        tk_ts: tkTs?.uuid || '',
        tk_kh: tkKhauHao?.uuid || '',
        tk_cp: tkChiPhi?.uuid || ''
      };
      onSubmit?.(formData);
    } catch (err: any) {
      console.error('Error submitting form:', err);
    }
  };

  const handleCloseDialog = () => {
    setShowConfirmDialog(false);
    onClose();
  };

  const handleClose = () => {
    setShowConfirmDialog(true);
    onClose();
  };
  return (
    <>
      <AritoDialog
        open={open}
        onClose={handleClose}
        title={mode === 'add' ? 'Mới' : mode === 'edit' ? 'Sửa' : 'Xem'}
        maxWidth='lg'
        disableBackdropClose={true}
        disableEscapeKeyDown={true}
        titleIcon={<AritoIcon icon={281} />}
      >
        <AritoForm
          mode={mode}
          hasAritoActionBar={false}
          schema={formSchema}
          onSubmit={handleSubmit}
          initialData={initialData || initialValues}
          className='w-full min-w-[800px]'
          headerFields={
            <BasicFormProps
              formMode={mode}
              assetCode={assetCode}
              setAssetCode={setAssetCode}
              ky={ky}
              setKy={setKy}
              nam={nam}
              setNam={setNam}
              boPhan={boPhan}
              setBoPhan={setBoPhan}
              tkTs={tkTs}
              setTkTs={setTkTs}
              tkKhauHao={tkKhauHao}
              setTkKhauHao={setTkKhauHao}
              tkChiPhi={tkChiPhi}
              setTkChiPhi={setTkChiPhi}
            />
          }
          classNameBottomBar='relative w-full flex justify-end gap-2'
          bottomBar={
            <BottomBar
              mode={mode}
              onAdd={onAddButtonClick}
              onEdit={onEditButtonClick}
              onDelete={onDeleteButtonClick}
              onCopy={onCopyButtonClick}
              onClose={() => setShowConfirmDialog(true)}
            />
          }
        />
      </AritoDialog>

      <ConfirmationDialog
        open={showConfirmDialog}
        onClose={() => setShowConfirmDialog(false)}
        onConfirm={handleCloseDialog}
        title='Cảnh báo'
        message='Bạn muốn kết thúc?'
      />
    </>
  );
}

export default FormDialog;
