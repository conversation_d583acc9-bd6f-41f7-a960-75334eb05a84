'use client';
import Split from 'react-split';
import { filter } from 'lodash';
import { exportPaymentRequestDetailColumns, getChangingValuePaymentRequestColumns } from './cols-definition';
import { useFormState, useSearchState, usePaymentRequestData } from './hooks';
import { AritoColoredDot } from '@/components/custom/arito/icon/colored-dot';
import { AritoInputTable } from '@/components/custom/arito/input-table';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { ActionBar, AddForm, SearchDialog } from './components';
import { LoadingOverlay } from '@/components/custom/arito';
import ConfirmDialog from './components/ConfirmDialog';
import { filterableCols } from './types/filterCols';
import { calculateTotals } from './utils/Calculate';
import { filterValues } from './types/filterTabs';

export default function CapNhatDeNghiThanhToanPage() {
  // Use custom hooks for state management
  const {
    showForm,
    formMode,
    selectedObj,
    currentObj,
    inputDetails,
    showDelete,
    setSelectedObj,
    setInputDetails,
    handleCloseForm,
    handleOpenEditForm,
    handleOpenViewForm,
    handleOpenAddForm,
    handleOpenCopyForm,
    handleOpenDeleteDialog,
    handleCloseDeleteDialog,
    clearSelection
  } = useFormState();

  const { openSearchDialog, searchParams, handleOpenSearchDialog, handleCloseSearchDialog, handleSearch } =
    useSearchState();

  const { rows, isLoading, error, createHandleRowClick, handleFormSubmit, handleDelete, handleRefresh } =
    usePaymentRequestData(handleCloseForm);

  // Create the handleRowClick function with the required dependencies
  const handleRowClick = createHandleRowClick(setSelectedObj, setInputDetails);
  const tables = [
    {
      name: 'Tất cả',
      rows: rows,
      columns: getChangingValuePaymentRequestColumns(handleOpenViewForm, handleOpenEditForm)
    },
    ...filterValues.map(filterValue => {
      const filteredRows = filter(rows, row => row.status === filterValue.value);
      return {
        name: filterValue.name,
        rows: filteredRows,
        columns: getChangingValuePaymentRequestColumns(handleOpenViewForm, handleOpenEditForm),
        icon: <AritoColoredDot color={filterValue.color} className='mr-2' />,
        tabProps: { className: 'whitespace-nowrap' }
      };
    }),
    ...(filterValues.length > 0
      ? [
          {
            name: 'Khác',
            rows: rows.filter((row: any) => !filterValues.some(filterValue => row.status === filterValue.value)),
            columns: getChangingValuePaymentRequestColumns(handleOpenViewForm, handleOpenEditForm),
            icon: <AritoColoredDot color='black' className='mr-2' />,
            tabProps: { className: 'whitespace-nowrap' }
          }
        ]
      : [])
  ];

  // Show error message if there's an error
  if (error) {
    console.error('Payment request data error:', error);
  }

  return (
    <div className='flex h-full min-h-screen w-screen flex-col'>
      {showForm ? (
        <AddForm
          formMode={formMode}
          currentObj={currentObj}
          inputDetails={inputDetails}
          setInputDetails={setInputDetails}
          handleFormSubmit={handleFormSubmit}
          handleCloseForm={handleCloseForm}
          calculateTotals={calculateTotals}
        />
      ) : (
        <>
          <ActionBar
            onAddClick={handleOpenAddForm}
            onEditClick={() => selectedObj && handleOpenEditForm(selectedObj)}
            onDeleteClick={() => selectedObj && handleDelete(selectedObj.uuid)}
            onCopyClick={() => selectedObj && handleOpenCopyForm(selectedObj)}
            onSearchClick={handleOpenSearchDialog}
            onRefreshClick={handleRefresh}
            onPrintClick={() => {}}
          />

          {isLoading && rows.length === 0 ? (
            <LoadingOverlay />
          ) : (
            <Split
              className='flex flex-1 flex-col'
              direction='vertical'
              sizes={[50, 50]}
              minSize={200}
              gutterSize={8}
              gutterAlign='center'
              snapOffset={30}
              dragInterval={1}
              cursor='row-resize'
            >
              <div className='w-full overflow-auto'>
                <AritoDataTables
                  tables={tables}
                  onRowClick={handleRowClick}
                  selectedRowId={selectedObj?.id || undefined}
                  filterableColumns={filterableCols}
                />
              </div>
              <div className='overflow-x-auto'>
                <AritoInputTable
                  value={inputDetails || []}
                  columns={exportPaymentRequestDetailColumns}
                  mode='view'
                  className='w-full'
                  tableActionButtons={['moveUp', 'moveDown', 'export', 'pin', 'copy', 'paste']}
                />
              </div>
            </Split>
          )}

          <SearchDialog
            openSearchDialog={openSearchDialog}
            onCloseSearchDialog={handleCloseSearchDialog}
            onSearch={handleSearch}
            initialData={searchParams || undefined}
          />

          <ConfirmDialog
            open={showDelete}
            selectedObj={selectedObj}
            onClose={handleCloseDeleteDialog}
            onDelete={handleDelete}
            clearSelection={clearSelection}
            title='Xóa đề nghị thanh toán'
            content='Bạn có chắc chắn muốn xóa đề nghị thanh toán này không?'
          />
        </>
      )}
    </div>
  );
}
