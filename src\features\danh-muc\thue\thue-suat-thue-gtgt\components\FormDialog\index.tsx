import { useEffect, useState } from 'react';
import { But<PERSON> } from '@mui/material';
import { searchSchema, TaxRateFormattedData } from '../../schemas';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoIcon } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito';
import { BasicInformationTab } from '../AddingTab';
import { ConfirmDialog } from '../ConfirmDialog';

type FormMode = 'add' | 'edit' | 'view';

interface FormDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: any) => Promise<void>;
  formMode: FormMode;
  initialData?: TaxRateFormattedData;
  onAdd?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onCopy?: () => void;
}

export const FormDialog = ({
  open,
  onClose,
  onSubmit,
  formMode,
  initialData,
  onAdd,
  onEdit,
  onDelete,
  onCopy
}: FormDialogProps) => {
  const [isConfirm, setIsConfirm] = useState<boolean>(false);
  const [isFormDirty, setIsFormDirty] = useState<boolean>(false);

  useEffect(() => {
    if (!open) {
      setIsFormDirty(false);
    }
  }, [open]);

  const handleSubmit = async (data: any) => {
    try {
      await onSubmit?.(data);
      setIsFormDirty(false);
    } catch (error) {
      console.error('Error submitting form:', error);
      // Keep the form open if there's an error
      return;
    }
  };

  const handleClose = () => {
    if (isFormDirty && formMode !== 'view') {
      setIsConfirm(true);
    } else {
      onClose();
    }
  };

  const viewActions = [
    { onClick: onAdd, icon: 571, text: 'Thêm' },
    { onClick: onEdit, icon: 9, text: 'Sửa' },
    { onClick: onDelete, icon: 8, text: 'Xoá' },
    { onClick: onCopy, icon: 11, text: 'Sao chép' },
    { onClick: onClose, icon: 885, text: 'Đóng' }
  ];

  return (
    <>
      <AritoDialog
        open={open}
        onClose={handleClose}
        title={`${formMode === 'add' ? 'Mới' : formMode === 'edit' ? 'Sửa' : 'Danh mục thuế suất'}`}
        maxWidth='xl'
        disableBackdropClose={false}
        disableEscapeKeyDown={true}
        titleIcon={<AritoIcon icon={281} />}
      >
        <AritoForm
          mode={formMode}
          hasAritoActionBar={false}
          schema={searchSchema}
          onSubmit={handleSubmit}
          initialData={initialData}
          className='w-[800px]'
          headerFields={
            <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
              <BasicInformationTab formMode={formMode} />
            </div>
          }
          classNameBottomBar='relative w-full flex justify-end gap-2 p-1'
          bottomBar={
            <>
              {formMode === 'view' && (
                <>
                  {viewActions.map(({ onClick, icon, text }) => (
                    <Button key={text} onClick={onClick} variant='outlined'>
                      <AritoIcon icon={icon} className='mr-2' />
                      {text}
                    </Button>
                  ))}
                </>
              )}
              {formMode !== 'view' && (
                <>
                  <Button
                    type='submit'
                    variant='contained'
                    className='bg-[rgba(15,118,110,0.9)] normal-case hover:bg-[rgba(15,118,110,1)]'
                  >
                    <AritoIcon icon={884} className='mr-2' />
                    Đồng ý
                  </Button>
                  <Button onClick={handleClose} variant='outlined'>
                    <AritoIcon icon={885} className='mr-2' />
                    Huỷ
                  </Button>
                </>
              )}
            </>
          }
        />
      </AritoDialog>

      {isConfirm && (
        <ConfirmDialog
          open={isConfirm}
          onClose={() => setIsConfirm(false)}
          onConfirm={() => {
            setIsConfirm(false);
            onClose();
          }}
          title='Cảnh báo'
          content='Bạn muốn kết thúc?'
        />
      )}
    </>
  );
};
