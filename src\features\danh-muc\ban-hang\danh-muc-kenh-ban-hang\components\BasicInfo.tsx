import { useFormContext } from 'react-hook-form';
import {
  nguonDonSearchColumns,
  hinhThucThanhToanSearchColumns,
  phuongThucThanhToanSearchColumns
} from '../cols-definition';
import { HinhThucThanhToan, NguonDon, PhuongThucThanhToan } from '@/types/schemas';
import { SearchField, FormField } from '@/components/custom/arito';
import QUERY_KEYS from '@/constants/query-keys';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface BasicInfoProps {
  mode: FormMode;
  nguonDon: NguonDon | null;
  hinhThucThanhToan: HinhThucThanhToan | null;
  phuongThucThanhToan: PhuongThucThanhToan | null;
  setNguonDon: (nguonDon: NguonDon) => void;
  setHinhThucThanhToan: (hinhThucThanhToan: HinhThucThanhToan) => void;
  setPhuongThucThanhToan: (phuongThucThanhToan: PhuongThucThanhToan) => void;
}

function BasicInfo({
  mode,
  nguonDon,
  hinhThucThanhToan,
  phuongThucThanhToan,
  setNguonDon,
  setHinhThucThanhToan,
  setPhuongThucThanhToan
}: BasicInfoProps) {
  const isViewMode = mode === 'view';
  const { setValue } = useFormContext();

  const handleNguonDonSelection = (selected: NguonDon) => {
    setNguonDon(selected);
    setValue('ma_nguondon', selected.uuid);
    setValue('ma_nguon_don', selected.ma_nguondon);
  };

  const handleHinhThucThanhToanSelection = (selected: HinhThucThanhToan) => {
    setHinhThucThanhToan(selected);
    setValue('ma_httt', selected.uuid);
    setValue('hinh_thuc_thanh_toan', selected.ma_httt);
  };

  const handlePhuongThucThanhToanSelection = (selected: PhuongThucThanhToan) => {
    setPhuongThucThanhToan(selected);
    setValue('ma_pttt', selected.uuid);
    setValue('phuong_thuc_thanh_toan', selected.ma_pttt);
  };

  return (
    <div className='space-y-3 p-6'>
      <div className='flex flex-col sm:flex-row sm:items-center'>
        <Label className='mt-2 min-w-48'>Mã kênh bán hàng</Label>
        <FormField type='text' name='ma_kenh_ban_hang' className='w-40' disabled={isViewMode || mode === 'edit'} />
      </div>

      <div className='flex flex-col sm:flex-row sm:items-center'>
        <Label className='mt-2 min-w-48'>Mã nguồn đơn</Label>
        <div>
          <SearchField<NguonDon>
            type='text'
            displayRelatedField={'ten_nguondon'}
            columnDisplay={'ma_nguondon'}
            searchEndpoint={`/${QUERY_KEYS.DANH_MUC_NGUON_DON}/`}
            searchColumns={nguonDonSearchColumns}
            dialogTitle='Danh mục nguồn đơn'
            value={nguonDon?.ma_nguondon || ''}
            onRowSelection={handleNguonDonSelection}
            disabled={isViewMode}
          />
          <FormField type='text' className='hidden' name='ma_nguondon' />
        </div>
      </div>

      <div className='flex flex-col sm:flex-row sm:items-center'>
        <Label className='mt-2 min-w-48'>Mã cửa hàng</Label>
        <FormField type='text' name='ma_cua_hang' className='w-40' disabled={isViewMode} />
      </div>

      <div className='flex flex-col sm:flex-row sm:items-center'>
        <Label className='mt-2 min-w-48'>Hình thức thanh toán</Label>
        <div>
          <SearchField<HinhThucThanhToan>
            type='text'
            displayRelatedField={'ten_httt'}
            columnDisplay={'ma_httt'}
            searchEndpoint={`/${QUERY_KEYS.HINH_THUC_THANH_TOAN}/`}
            searchColumns={hinhThucThanhToanSearchColumns}
            dialogTitle='Danh mục hình thức thanh toán'
            value={hinhThucThanhToan?.ma_httt || ''}
            onRowSelection={handleHinhThucThanhToanSelection}
            disabled={isViewMode}
          />
          <FormField type='text' className='hidden' name='ma_httt' />
        </div>
      </div>

      <div className='flex flex-col sm:flex-row sm:items-center'>
        <Label className='mt-2 min-w-48'>Phương thức thanh toán</Label>
        <div>
          <SearchField<PhuongThucThanhToan>
            type='text'
            displayRelatedField={'ten_pttt'}
            columnDisplay={'ma_pttt'}
            searchEndpoint={`/${QUERY_KEYS.PHUONG_THUC_THANH_TOAN}/`}
            searchColumns={phuongThucThanhToanSearchColumns}
            dialogTitle='Danh mục phương thức thanh toán'
            value={phuongThucThanhToan?.ma_pttt || ''}
            onRowSelection={handlePhuongThucThanhToanSelection}
            disabled={isViewMode}
          />
          <FormField type='text' className='hidden' name='ma_pttt' />
        </div>
      </div>

      <div className='flex flex-col sm:flex-row sm:items-center'>
        <Label className='mt-2 min-w-48'>Tỉ lệ hoa hồng</Label>
        <FormField type='number' name='ti_le_hoa_hong' className='w-32' defaultValue='0.00' disabled={isViewMode} />
      </div>

      <div className='flex flex-col sm:flex-row sm:items-center'>
        <Label className='mt-2 min-w-48'>Loại</Label>
        <FormField
          type='select'
          name='loai'
          className='w-44'
          defaultValue='1'
          options={[
            { label: '1. Online', value: '1' },
            { label: '2. Tại chỗ', value: '2' }
          ]}
          disabled={isViewMode}
        />
      </div>

      <div className='flex flex-col sm:flex-row sm:items-center'>
        <Label className='mt-2 min-w-48'>Trạng thái</Label>
        <FormField
          type='select'
          name='trang_thai'
          className='w-44'
          defaultValue={1}
          options={[
            { label: '1. Còn sử dụng', value: 1 },
            { label: '0. Không sử dụng', value: 0 }
          ]}
          disabled={isViewMode}
        />
      </div>
    </div>
  );
}

export default BasicInfo;
