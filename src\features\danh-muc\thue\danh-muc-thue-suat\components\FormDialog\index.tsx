import { useState } from 'react';
import ConfirmationDialog from '@/components/custom/arito/dialog/confirm-dialog';
import { AritoIcon, BottomBar } from '@/components/custom/arito';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { formSchema, initialValues } from '../../schemas';
import { AritoForm } from '@/components/custom/arito';
import { useSearchFieldStates } from '../../hooks';
import { AddingTab } from '../AddingTab';

type FormMode = 'add' | 'edit' | 'view';

interface FormDialogProps {
  onClose: () => void;
  onSubmit: (data: any) => Promise<void>;
  mode: FormMode;
  initialData?: any;
  onAdd: () => void;
  onEdit: () => void;
  onDelete: () => void;
  onCopy: () => void;
}

export const FormDialog = ({
  onClose,
  onSubmit,
  mode,
  initialData,
  onAdd,
  onEdit,
  onDelete,
  onCopy
}: FormDialogProps) => {
  const [showConfirmDialog, setShowConfirmDialog] = useState<boolean>(false);
  const [isFormDirty, setIsFormDirty] = useState<boolean>(false);
  const searchFields = useSearchFieldStates(initialData);
  const { nhomThue, tkThueDauRa, tkThueDauRaDuocGiam, tkThueDauVao, tkThueDauVaoDuocGiam } = searchFields;

  const handleSubmit = async (data: any) => {
    try {
      const payload = {
        ...data,
        nhom_thue: nhomThue?.uuid || null,
        tk_thue_dau_ra: tkThueDauRa?.uuid || null,
        tk_thue_dau_ra_duoc_gia: tkThueDauRaDuocGiam?.uuid || null,
        tk_thue_dau_vao: tkThueDauVao?.uuid || null,
        tk_thue_dau_vao_duoc_gia: tkThueDauVaoDuocGiam?.uuid || null
      };
      await onSubmit?.(payload);
      setIsFormDirty(false);
    } catch (error) {
      return;
    }
  };

  const handleClose = () => {
    if (isFormDirty) {
      setShowConfirmDialog(true);
    } else {
      onClose();
    }
  };

  const handleCloseDialog = () => {
    setShowConfirmDialog(false);
    onClose();
  };

  return (
    <>
      <AritoDialog
        onClose={handleClose}
        title={mode === 'add' ? 'Mới' : mode === 'edit' ? 'Sửa' : 'Xem'}
        maxWidth='lg'
        disableBackdropClose={true}
        disableEscapeKeyDown={true}
        titleIcon={<AritoIcon icon={281} />}
      >
        <AritoForm
          mode={mode}
          hasAritoActionBar={false}
          schema={formSchema}
          onSubmit={handleSubmit}
          initialData={initialData || initialValues}
          className='w-full md:min-w-[500px] lg:min-w-[600px]'
          headerFields={<AddingTab mode={mode} searchFields={searchFields} />}
          classNameBottomBar='relative w-full flex justify-end gap-2'
          bottomBar={
            <BottomBar
              mode={mode}
              onAdd={onAdd}
              onEdit={onEdit}
              onDelete={onDelete}
              onCopy={onCopy}
              onClose={() => setShowConfirmDialog(true)}
            />
          }
        />
      </AritoDialog>

      <ConfirmationDialog
        open={showConfirmDialog}
        onClose={() => setShowConfirmDialog(false)}
        onConfirm={handleCloseDialog}
        title='Cảnh báo'
        message='Bạn muốn kết thúc?'
      />
    </>
  );
};
