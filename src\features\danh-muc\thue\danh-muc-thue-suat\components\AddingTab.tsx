import { useFormContext } from 'react-hook-form';
import { FormField } from '@/components/custom/arito/form/form-field';
import { accountSearchColumns, nhomColumns } from '@/constants';
import type { AccountModel, Group } from '@/types/schemas';
import { SearchField } from '@/components/custom/arito';
import { Label } from '@/components/ui/label';
import type { FormMode } from '@/types/form';
import { QUERY_KEYS } from '@/constants';

interface BasicProps {
  mode: FormMode;
  searchFields: {
    nhomThue: any | null;
    setNhomThue: (nt: any) => void;
    tkThueDauRa: AccountModel | null;
    setTkThueDauRa: (tk: AccountModel) => void;
    tkThueDauRaDuocGiam: AccountModel | null;
    setTkThueDauRaDuocGiam: (tk: AccountModel) => void;
    tkThueDauVao: AccountModel | null;
    setTkThueDauVao: (tk: AccountModel) => void;
    tkThueDauVaoDuocGiam: AccountModel | null;
    setTkThueDauVaoDuocGiam: (tk: AccountModel) => void;
  };
}

export const AddingTab = ({ mode, searchFields }: BasicProps) => {
  const isDisabled = mode === 'view';
  const {
    nhomThue,
    setNhomThue,
    tkThueDauRa,
    setTkThueDauRa,
    tkThueDauRaDuocGiam,
    setTkThueDauRaDuocGiam,
    tkThueDauVao,
    setTkThueDauVao,
    tkThueDauVaoDuocGiam,
    setTkThueDauVaoDuocGiam
  } = searchFields;
  return (
    <div className='space-y-4 p-4'>
      <div className='flex items-center'>
        <Label className='w-44'>Mã thuế</Label>
        <FormField name='ma_thue' disabled={isDisabled} />
      </div>
      <div className='flex items-center'>
        <Label className='w-44'>Tên thuế</Label>
        <FormField name='ten_thue' disabled={isDisabled} />
      </div>
      <div className='flex items-center'>
        <Label className='w-44'>Tên khác</Label>
        <FormField name='ten_thue2' disabled={isDisabled} />
      </div>
      <div className='flex items-center'>
        <Label className='w-44'>Thuế suất (%)</Label>
        <FormField name='thue_suat' type='number' disabled={isDisabled} />
      </div>
      <div className='flex items-center'>
        <Label className='w-44'>Nhóm thuế</Label>
        <SearchField<Group>
          type='text'
          disabled={isDisabled}
          columnDisplay='ma_nhom'
          searchEndpoint={`/${QUERY_KEYS.NHOM}/?loai_nhom=TAX`}
          searchColumns={nhomColumns}
          dialogTitle='Danh mục nhóm thuế'
          displayRelatedField='ten_phan_nhom'
          relatedFieldValue={nhomThue?.ten_phan_nhom || ''}
          value={nhomThue?.ma_nhom || ''}
          onRowSelection={setNhomThue}
        />
      </div>

      <div className='flex items-center'>
        <Label className='w-44'>TK thuế đầu ra</Label>
        <SearchField<AccountModel>
          type='text'
          disabled={isDisabled}
          columnDisplay='code'
          searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
          searchColumns={accountSearchColumns}
          dialogTitle='Danh mục tài khoản'
          displayRelatedField={'name'}
          value={tkThueDauRa?.code || ''}
          relatedFieldValue={tkThueDauRa?.name || ''}
          onRowSelection={setTkThueDauRa}
        />
      </div>

      <div className='flex items-center'>
        <Label className='w-44'>TK thuế đầu ra được giảm</Label>
        <SearchField<AccountModel>
          type='text'
          disabled={isDisabled}
          columnDisplay='code'
          searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
          searchColumns={accountSearchColumns}
          dialogTitle='Danh mục tài khoản'
          displayRelatedField={'name'}
          value={tkThueDauRaDuocGiam?.code || ''}
          relatedFieldValue={tkThueDauRaDuocGiam?.name || ''}
          onRowSelection={setTkThueDauRaDuocGiam}
        />
      </div>

      <div className='flex items-center'>
        <Label className='w-44'>TK thuế đầu vào</Label>
        <SearchField<AccountModel>
          type='text'
          disabled={isDisabled}
          columnDisplay='code'
          searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
          searchColumns={accountSearchColumns}
          dialogTitle='Danh mục tài khoản'
          displayRelatedField={'name'}
          value={tkThueDauVao?.code || ''}
          relatedFieldValue={tkThueDauVao?.name || ''}
          onRowSelection={setTkThueDauVao}
        />
      </div>

      <div className='flex items-center'>
        <Label className='w-44'>TK thuế đầu vào được giảm</Label>
        <SearchField<AccountModel>
          type='text'
          disabled={isDisabled}
          columnDisplay='code'
          searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
          searchColumns={accountSearchColumns}
          dialogTitle='Danh mục tài khoản'
          displayRelatedField={'name'}
          value={tkThueDauVaoDuocGiam?.code || ''}
          relatedFieldValue={tkThueDauVaoDuocGiam?.name || ''}
          onRowSelection={setTkThueDauVaoDuocGiam}
        />
      </div>

      <div className='flex items-center'>
        <Label className='w-44'>STT</Label>
        <FormField name='stt' type='number' disabled={isDisabled} />
      </div>

      <div className='flex items-center'>
        <Label className='w-44'>Loại thuế</Label>
        <FormField
          name='loai_thue'
          type='select'
          options={[
            { value: '1', label: '1. Thuế GTGT' },
            { value: '2', label: '2. Thuế nhập khẩu' },
            { value: '3', label: '3. Thuế tiêu thụ đặc biệt' }
          ]}
          disabled={isDisabled}
        />
      </div>

      <div className='flex items-center'>
        <Label className='w-44'>Trạng thái</Label>
        <FormField
          name='status'
          type='select'
          options={[
            { value: '1', label: '1. Còn sử dụng' },
            { value: '0', label: '0. Không sử dụng' }
          ]}
          disabled={isDisabled}
        />
      </div>
    </div>
  );
};
