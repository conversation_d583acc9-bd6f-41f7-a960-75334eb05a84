import React, { useState } from 'react';
import { But<PERSON> } from '@mui/material';
import { SupplierGroupFormattedData, SearchFormValues, searchSchema, initialValues } from '../../schemas';
import { BottomBar } from '@/components/custom/arito/form/bottom-bar';
import { AritoDialog, AritoForm } from '@/components/custom/arito';
import { AritoIcon } from '@/components/custom/arito';
import SupplierGroupForm from './SupplierGroupForm';
import { Group, GroupType } from '@/types/schemas';
import ConfirmDialog from './ConfirmDialog';
import { FormMode } from '@/types/form';

interface SupplierGroupDialogProps {
  open: boolean;
  mode: FormMode;
  initialData?: any;
  onClose: () => void;
  onSubmit?: (data: SupplierGroupFormattedData) => void;
  selectedObj?: Group | null;
  updateSupplierGroup?: (uuid: string, data: SupplierGroupFormattedData) => Promise<Group | undefined>;
  addSupplierGroup?: (data: SupplierGroupFormattedData) => Promise<Group | undefined>;
  onAddButtonClick?: () => void;
  onEditButtonClick?: () => void;
  onDeleteButtonClick?: () => void;
  onCopyButtonClick?: () => void;
  activeGroupType?: string | null;
  groups?: Group[]; // Danh sách nhóm để kiểm tra trùng mã
}
const SupplierGroupDialog = ({
  open,
  mode,
  initialData,
  onClose,
  onSubmit,
  selectedObj,
  updateSupplierGroup,
  addSupplierGroup,
  onAddButtonClick,
  onEditButtonClick,
  onDeleteButtonClick,
  onCopyButtonClick,
  activeGroupType,
  groups = [] // Mặc định là mảng rỗng
}: SupplierGroupDialogProps) => {
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [showDuplicateCodeDialog, setShowDuplicateCodeDialog] = useState(false);
  const handleSubmit = async (data: SearchFormValues) => {
    const formattedData: SupplierGroupFormattedData = {
      ma_nhom: data.ma_nhom,
      ten_phan_nhom: data.ten_phan_nhom,
      ten2: data.ten2 || null,
      trang_thai: data.trang_thai.toString(),
      loai_nhom: (activeGroupType as any) || GroupType.SUPPLIER1
    };

    try {
      // Kiểm tra mã nhóm đã tồn tại chưa khi thêm mới hoặc sao chép
      if (mode === 'add' && addSupplierGroup) {
        // Lấy danh sách nhóm từ props
        const existingGroups = Array.isArray(groups) ? groups : [];
        // Kiểm tra xem mã nhóm đã tồn tại trong danh sách chưa
        const isDuplicate = existingGroups.some(
          (group: Group) => group.ma_nhom === data.ma_nhom && (!selectedObj || group.uuid !== selectedObj.uuid)
        );

        if (isDuplicate) {
          // Hiển thị thông báo lỗi
          setShowDuplicateCodeDialog(true);
          return;
        }

        await addSupplierGroup(formattedData);
        onClose();
      } else if (mode === 'edit' && selectedObj && updateSupplierGroup) {
        // Kiểm tra xem có nhóm nào khác có cùng mã không (trừ nhóm hiện tại)
        const existingGroups = Array.isArray(groups) ? groups : [];
        const isDuplicate = existingGroups.some(
          (group: Group) => group.ma_nhom === data.ma_nhom && group.uuid !== selectedObj.uuid
        );

        if (isDuplicate) {
          // Hiển thị thông báo lỗi
          setShowDuplicateCodeDialog(true);
          return;
        }

        // Gọi API cập nhật và đợi kết quả
        const result = await updateSupplierGroup(selectedObj.uuid, formattedData);

        // Chỉ đóng dialog khi cập nhật thành công
        if (result) {
          // Thêm một khoảng thời gian nhỏ để đảm bảo dữ liệu được cập nhật trước khi đóng dialog
          setTimeout(() => {
            onClose();
          }, 100);
        }
      } else if (onSubmit) {
        onSubmit(formattedData);
        onClose();
      }
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };
  const handleCloseDialog = () => {
    setShowConfirmDialog(false);
    onClose();
  };
  const title = mode === 'add' ? 'Mới' : mode === 'edit' ? 'Sửa' : 'Xem';

  return (
    <>
      <AritoDialog
        open={open}
        onClose={onClose}
        title={title}
        maxWidth='lg'
        disableBackdropClose={true}
        disableEscapeKeyDown={true}
        titleIcon={<AritoIcon icon={12} />}
      >
        <AritoForm
          mode={mode}
          hasAritoActionBar={false}
          schema={searchSchema}
          onSubmit={handleSubmit}
          initialData={initialData || initialValues}
          className='w-full md:min-w-[500px] lg:min-w-[600px]'
          headerFields={<SupplierGroupForm mode={mode} />}
          classNameBottomBar='relative w-full flex justify-end gap-2'
          bottomBar={
            <BottomBar
              mode={mode}
              onAdd={onAddButtonClick}
              onEdit={onEditButtonClick}
              onDelete={onDeleteButtonClick}
              onCopy={onCopyButtonClick}
              onSubmit={() => {}}
              onClose={onClose}
            />
          }
        />
      </AritoDialog>

      <ConfirmDialog
        onClose={handleCloseDialog}
        open={showConfirmDialog}
        onCloseConfirmDialog={() => setShowConfirmDialog(false)}
      />

      {/* Dialog hiển thị thông báo lỗi khi mã nhóm đã tồn tại */}
      <AritoDialog
        open={showDuplicateCodeDialog}
        onClose={() => setShowDuplicateCodeDialog(false)}
        title='Thông báo'
        maxWidth='sm'
        disableBackdropClose={false}
        disableEscapeKeyDown={false}
        titleIcon={<AritoIcon icon={260} />}
      >
        <div className='p-4'>
          <p className='mb-4 text-center'>Mã nhóm đã tồn tại trong danh mục nhóm</p>
          <div className='flex justify-end'>
            <Button
              className='bg-[rgba(15,118,110,0.9)] text-white hover:bg-[rgba(15,118,110,1)]'
              onClick={() => setShowDuplicateCodeDialog(false)}
              variant='contained'
            >
              <AritoIcon icon={884} marginX='4px' />
              Đồng ý
            </Button>
          </div>
        </div>
      </AritoDialog>
    </>
  );
};

export default SupplierGroupDialog;
