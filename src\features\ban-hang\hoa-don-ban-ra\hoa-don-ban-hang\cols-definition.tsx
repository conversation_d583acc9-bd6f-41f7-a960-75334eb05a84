import { GridColDef } from '@mui/x-data-grid';
import { format } from 'date-fns';
import { Checkbox } from '@/components/ui/checkbox';

export const getDataTableColumns = (handleViewClick: () => void): GridColDef[] => [
  {
    field: 'hdbh_yn',
    headerName: 'Hoá đơn',
    width: 120,
    type: 'boolean',
    renderCell: params => <Checkbox checked={params.row.hdbh_yn} />
  },
  {
    field: 'px_yn',
    headerName: 'Xuất kho',
    width: 120,
    type: 'boolean',
    renderCell: params => <Checkbox checked={params.row.px_yn} />
  },
  {
    field: 'status',
    headerName: 'Trạng thái',
    width: 120,
    renderCell: params => {
      const status = params.row.status;
      switch (status) {
        case '0':
          return 'Lập chứng từ';
        case '3':
          return 'Chờ duyệt';
        case '5':
          return 'Xuất hóa đơn';
        case '6':
          return 'Đã xuất hóa đơn điện tử';
        case '7':
          return 'Bỏ duyệt đơn hàng';
        default:
          return '';
      }
    }
  },
  {
    field: 'ma_tthddt',
    headerName: 'Trạng thái HĐĐT',
    width: 150,
    renderCell: params => {
      const status = params.value;
      switch (status) {
        case '0':
          return 'Không sử dụng';
        case '1':
          return 'Chờ phát hành';
        default:
          return '';
      }
    }
  },
  {
    field: 'so_ct',
    headerName: 'Số c/từ',
    width: 120,
    renderCell: params => (
      <button onClick={handleViewClick} className='hover:text-blue-500 hover:underline'>
        {params.row.so_ct}
      </button>
    )
  },
  {
    field: 'ngay_ct',
    headerName: 'Ngày c/từ',
    width: 120,
    renderCell: params => format(params.row.ngay_ct, 'dd/MM/yyyy')
  },
  {
    field: 'so_ct_hddt',
    headerName: 'Số hóa đơn',
    width: 120
  },
  {
    field: 'ma_kh',
    headerName: 'Mã khách hàng',
    width: 120,
    renderCell: params => params.row.ma_kh_data?.customer_code
  },
  {
    field: 'ten_kh_thue',
    headerName: 'Tên khách hàng',
    width: 200
  },
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 200
  },
  {
    field: 'tk',
    headerName: 'Tk nợ',
    width: 100,
    renderCell: params => params.row.tk_data?.code
  },
  {
    field: 't_tt_nt',
    headerName: 'Tổng tiền',
    width: 150
  },
  {
    field: 'ma_nt',
    headerName: 'Ngoại tệ',
    width: 120,
    renderCell: params => params.row.ma_nt_data?.ma_nt
  }
];

export const getInputTableColumns = (): GridColDef[] => [
  {
    field: 'ma_vt',
    headerName: 'Mã sản phẩm',
    width: 120,
    renderCell: params => params.row.ma_vt_data?.ma_vt
  },
  {
    field: 'ten_vt',
    headerName: 'Tên sản phẩm',
    width: 200,
    renderCell: params => params.row.ma_vt_data?.ten_vt
  },
  {
    field: 'dvt',
    headerName: 'Đvt',
    width: 100,
    renderCell: params => params.row.dvt_data?.dvt
  },
  {
    field: 'ma_kho',
    headerName: 'Mã kho',
    width: 120,
    renderCell: params => params.row.ma_kho_data?.ma_kho
  },
  {
    field: 'sl_ton',
    headerName: 'Tồn',
    width: 100
  },
  {
    field: 'ct_km',
    headerName: 'Loại hàng',
    width: 120,
    renderCell: params => {
      const ctKm = params.row.ct_km;
      switch (ctKm) {
        case false:
          return 'Hàng bán';
        case true:
          return 'Hàng KM';
      }
    }
  },
  {
    field: 'gia_nt2',
    headerName: 'Giá bán VND',
    width: 150
  },
  {
    field: 'tien_nt2',
    headerName: 'Thành tiền VND',
    width: 150
  },
  {
    field: 'tl_ck',
    headerName: 'Tl ck(%)',
    width: 150
  },
  {
    field: 'px_dd',
    headerName: 'Đích danh',
    width: 120,
    type: 'boolean',
    renderCell: params => <Checkbox checked={params.row.px_dd} />
  },
  {
    field: 'gia_nt',
    headerName: 'Giá tồn VND',
    width: 150
  },
  {
    field: 'tien_nt',
    headerName: 'Tiền VND',
    width: 150
  },
  {
    field: 'ma_thue',
    headerName: 'Thuế suất',
    width: 100
  },
  {
    field: 'tk_thue_co',
    headerName: 'Tk thuế có',
    width: 120,
    renderCell: params => params.row.tk_thue_co_data?.code
  },
  {
    field: 'tk_dt',
    headerName: 'Tk doanh thu',
    width: 120,
    renderCell: params => params.row.tk_dt_data?.code
  },
  {
    field: 'tk_gv',
    headerName: 'Tk giá vốn',
    width: 120,
    renderCell: params => params.row.tk_gv_data?.code
  },
  {
    field: 'tk_vt',
    headerName: 'Tk kho',
    width: 120,
    renderCell: params => params.row.tk_vt_data?.code
  },
  {
    field: 'tk_ck',
    headerName: 'Tk chiết khấu',
    width: 120,
    renderCell: params => params.row.tk_ck_data?.code
  },
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 120,
    renderCell: params => params.row.ma_bp_data?.ma_bp
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 120
  },
  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 120
  },
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 120
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 120
  },
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 120
  },
  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 120,
    renderCell: params => params.row.ma_sp_data?.ma_vt
  },
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 120
  }
];

export const historyColumns: GridColDef[] = [
  { field: 'username', headerName: 'Tài khoản', width: 100 },
  { field: 'nickname', headerName: 'Tên tài khoản', width: 200 },
  { field: 'action', headerName: 'Hành động', width: 100 },
  { field: 'datetime0', headerName: 'Thời gian', width: 150 }
];
