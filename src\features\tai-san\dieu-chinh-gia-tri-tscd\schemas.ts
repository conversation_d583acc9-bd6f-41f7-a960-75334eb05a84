import { z } from 'zod';

export const revenueExpenseReportSchema = z.object({});

export type EditPrintTemplateTableType = object;

export const editPrintTemPlateSchema = z.object({});

export type EditPrintTemplateFormValues = z.infer<typeof editPrintTemPlateSchema>;

// Schema for FormDialog
export const formSchema = z.object({
  ky: z.coerce.number().nullable().optional(),
  nam: z.coerce.number().nullable().optional(),
  ngay_ct: z.string().nonempty({ message: 'Ngày c/từ là bắt buộc' }),
  so_ct: z.string().nullable().optional(),
  ty_gia: z.string().nullable().optional(),
  nguyen_gia: z.string().nullable().optional(),
  gt_da_kh: z.string().nullable().optional(),
  gt_cl: z.coerce.number().nullable().optional(),
  so_ky_kh: z.coerce.number().nullable().optional(),
  gt_kh_ky: z.string().nullable().optional(),
  dien_giai: z.string().nullable().optional(),
  ma_nt: z.string().nullable().optional()
});

export type FormValues = z.infer<typeof formSchema>;

const today = new Date().toISOString().split('T')[0];

export const initialFormValues: FormValues = {
  ky: null,
  nam: null,
  ngay_ct: today,
  so_ct: null,
  ty_gia: null,
  nguyen_gia: null,
  gt_da_kh: null,
  gt_cl: null,
  so_ky_kh: null,
  gt_kh_ky: null,
  dien_giai: null,
  ma_nt: null
};

// Schema for SearchDialog
export const searchSchema = z.object({
  tu_ngay: z.string().optional(),
  den_ngay: z.string().optional()
});

export type SearchFormValues = z.infer<typeof searchSchema>;
