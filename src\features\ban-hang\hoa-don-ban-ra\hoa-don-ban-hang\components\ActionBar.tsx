import { <PERSON><PERSON>, Pencil, Plus, Trash, Search, Printer } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito/action-button';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import { AritoIcon } from '@/components/custom/arito';

interface ActionBarProps {
  onAdd?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onCopy?: () => void;
  onPrint?: () => void;
  onSearch?: () => void;
  onRefresh?: () => void;
  isEditDisabled?: boolean;
  className?: string;
}

export default function ActionBar({
  className,
  onAdd,
  onEdit,
  onDelete,
  onCopy,
  onPrint,
  onSearch,
  onRefresh,
  isEditDisabled = true
}: ActionBarProps) {
  return (
    <AritoActionBar titleComponent={<h1 className='text-xl font-bold'><PERSON><PERSON><PERSON> đơn bán hàng</h1>}>
      <AritoActionButton title='Thêm' icon={Plus} onClick={onAdd} />
      <AritoActionButton title='Sửa' icon={Pencil} onClick={onEdit} disabled={isEditDisabled} />
      <AritoActionButton title='Xóa' icon={Trash} onClick={onDelete} disabled={isEditDisabled} />
      <AritoActionButton title='Sao chép' icon={Copy} onClick={onCopy} disabled={isEditDisabled} />
      <AritoActionButton title='In ấn' icon={Printer} onClick={onPrint} />
      <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearch} />

      <AritoMenuButton
        items={[
          {
            title: 'Refresh',
            icon: <AritoIcon icon={15} />,
            onClick: onRefresh,
            group: 1
          },
          {
            title: 'Cố định cột',
            icon: <AritoIcon icon={16} />,
            onClick: () => {},
            group: 1
          },
          {
            title: 'In nhiều',
            icon: <AritoIcon icon={883} />,
            onClick: () => {},
            group: 2
          },
          {
            title: 'Kết xuất dữ liệu',
            icon: <AritoIcon icon={555} />,
            onClick: () => {},
            group: 2
          },
          {
            title: 'Tải mẫu Excel',
            icon: <AritoIcon icon={28} />,
            onClick: () => {},
            group: 3
          },
          {
            title: 'Lấy dữ liệu từ Excel',
            icon: <AritoIcon icon={29} />,
            onClick: () => {},
            group: 3
          }
        ]}
      />
    </AritoActionBar>
  );
}
