import { useState } from 'react';
import {
  Account<PERSON><PERSON><PERSON>,
  Quy<PERSON><PERSON>hung<PERSON>u,
  HanThanh<PERSON>oan,
  KhachHang,
  NhanV<PERSON>,
  <PERSON>oaiTe,
  DonViCoSo,
  DiaChi,
  PhuongTienVan<PERSON>huyen,
  PhuongThucGiaoHang
} from '@/types/schemas';

export interface FormFieldState {
  // Customer information
  khachHang: KhachHang | null;
  nhanVien: NhanVien | null;
  taiKhoan: AccountModel | null;
  hanThanhToan: HanThanhToan | null;

  // Document information
  quyenChungTu: QuyenChungTu | null;
  ngoaiTe: NgoaiTe | null;
  donViCoSo: DonViCoSo | null;
  soChungTuHienTai: string;

  // Other information
  diaChi: DiaChi | null;
  phuongTienVanChuyen: PhuongTienVanChuyen | null;
  phuongTienGiaoHang: PhuongThucGiaoHang | null;
  coQuanThue: KhachHang | null;

  // Checkbox states
  pt_tao_yn: boolean;
  transfer_yn: boolean;
}

export interface FormFieldActions {
  // Search field setters
  setKhachHang: (khachHang: KhachHang) => void;
  setNhanVien: (nhanVien: NhanVien) => void;
  setTaiKhoan: (taiKhoan: AccountModel) => void;
  setHanThanhToan: (hanThanhToan: HanThanhToan) => void;
  setQuyenChungTu: (quyenChungTu: QuyenChungTu) => void;
  setNgoaiTe: (ngoaiTe: NgoaiTe) => void;
  setDonViCoSo: (donViCoSo: DonViCoSo) => void;
  setSoChungTuHienTai: (soChungTuHienTai: string) => void;

  setDiaChi: (diaChi: DiaChi) => void;
  setPhuongTienVanChuyen: (phuongTienVanChuyen: PhuongTienVanChuyen) => void;
  setPhuongTienGiaoHang: (phuongTienGiaoHang: PhuongThucGiaoHang) => void;
  setCoQuanThue: (coQuanThue: KhachHang) => void;

  // Checkbox setters
  setPtTaoYn: (ptTaoYn: boolean) => void;
  setTransferYn: (transferYn: boolean) => void;

  // Utility functions
  resetState: () => void;
  updateState: (updates: Partial<FormFieldState>) => void;
}

export interface UseFormFieldStateReturn {
  state: FormFieldState;
  actions: FormFieldActions;
}

const initialState: FormFieldState = {
  // Customer information
  khachHang: null,
  nhanVien: null,
  taiKhoan: null,
  hanThanhToan: null,

  // Document information
  quyenChungTu: null,
  ngoaiTe: null,
  donViCoSo: null,
  soChungTuHienTai: '',

  // Other information
  diaChi: null,
  phuongTienVanChuyen: null,
  phuongTienGiaoHang: null,
  coQuanThue: null,

  // Checkbox states
  pt_tao_yn: false,
  transfer_yn: false
};

function transformInitialData(initialData?: any): Partial<FormFieldState> {
  if (!initialData) return {};

  return {
    // Customer information
    khachHang: initialData.ma_kh_data || null,
    nhanVien: initialData.ma_nv_data || null,
    taiKhoan: initialData.tk_data || null,
    hanThanhToan: initialData.han_tt_data || null,

    // Document information
    quyenChungTu: initialData.so_ct_data || null,
    ngoaiTe: initialData.ma_nt_data || null,
    donViCoSo: initialData.unit_id_data || null,

    // Other information
    diaChi: initialData.ma_dc_data || null,
    phuongTienVanChuyen: initialData.ma_ptvc_data || null,
    phuongTienGiaoHang: initialData.ma_ptgh_data || null,
    coQuanThue: initialData.ma_kh9_data || null,

    // Checkbox states
    pt_tao_yn: initialData.pt_tao_yn || false,
    transfer_yn: initialData.transfer_yn || false
  };
}

export function useFormFieldState(initialData?: any): UseFormFieldStateReturn {
  const [state, setState] = useState<FormFieldState>({
    ...initialState,
    ...transformInitialData(initialData)
  });

  const actions: FormFieldActions = {
    // Search field setters
    setKhachHang: (khachHang: KhachHang) => {
      setState(prev => ({
        ...prev,
        khachHang
      }));
    },

    setNhanVien: (nhanVien: NhanVien) => {
      setState(prev => ({
        ...prev,
        nhanVien
      }));
    },

    setTaiKhoan: (taiKhoan: AccountModel) => {
      setState(prev => ({
        ...prev,
        taiKhoan
      }));
    },

    setHanThanhToan: (hanThanhToan: HanThanhToan) => {
      setState(prev => ({
        ...prev,
        hanThanhToan
      }));
    },

    setQuyenChungTu: (quyenChungTu: QuyenChungTu) => {
      setState(prev => ({
        ...prev,
        quyenChungTu
      }));
    },

    setNgoaiTe: (ngoaiTe: NgoaiTe) => {
      setState(prev => ({
        ...prev,
        ngoaiTe
      }));
    },

    setDonViCoSo: (donViCoSo: DonViCoSo) => {
      setState(prev => ({
        ...prev,
        donViCoSo
      }));
    },

    setSoChungTuHienTai: (soChungTuHienTai: string) => {
      setState(prev => ({
        ...prev,
        soChungTuHienTai
      }));
    },

    setDiaChi: (diaChi: DiaChi) => {
      setState(prev => ({
        ...prev,
        diaChi
      }));
    },

    setPhuongTienVanChuyen: (phuongTienVanChuyen: PhuongTienVanChuyen) => {
      setState(prev => ({
        ...prev,
        phuongTienVanChuyen
      }));
    },

    setPhuongTienGiaoHang: (phuongTienGiaoHang: PhuongThucGiaoHang) => {
      setState(prev => ({
        ...prev,
        phuongTienGiaoHang
      }));
    },

    setCoQuanThue: (coQuanThue: KhachHang) => {
      setState(prev => ({
        ...prev,
        coQuanThue
      }));
    },

    setPtTaoYn: (ptTaoYn: boolean) => {
      setState(prev => ({ ...prev, pt_tao_yn: ptTaoYn }));
    },

    setTransferYn: (transferYn: boolean) => {
      setState(prev => ({ ...prev, transfer_yn: transferYn }));
    },

    resetState: () => {
      setState(initialState);
    },

    updateState: (updates: Partial<FormFieldState>) => {
      setState(prev => ({ ...prev, ...updates }));
    }
  };

  return { state, actions };
}
