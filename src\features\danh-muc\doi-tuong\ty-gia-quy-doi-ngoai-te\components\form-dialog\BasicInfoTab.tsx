'use client';

import { <PERSON>Field, SearchField } from '@/components/custom/arito';
import { ngoaiTeSearchColumns, QUERY_KEYS } from '@/constants';
import { Label } from '@/components/ui/label';
import { NgoaiTe } from '@/types/schemas';
import { FormMode } from '@/types/form';

interface BasicInfoTabProps {
  formMode: FormMode;
  ngoaiTe: NgoaiTe | null;
  setNgoaiTe: (ngoaiTe: NgoaiTe | null) => void;
}

export const BasicInfoTab: React.FC<BasicInfoTabProps> = ({ formMode, ngoaiTe, setNgoaiTe }) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-1'>
        <div className='flex items-center'>
          <Label className='w-36 min-w-36'>Ngoại tệ</Label>
          <div className='w-[220px]'>
            <SearchField<NgoaiTe>
              type='text'
              displayRelatedField='ten_nt'
              columnDisplay='ma_nt'
              searchEndpoint={`/${QUERY_KEYS.NGOAI_TE}/`}
              searchColumns={ngoaiTeSearchColumns}
              dialogTitle='Danh mục ngoại tệ'
              value={ngoaiTe?.ma_nt}
              onRowSelection={setNgoaiTe}
              disabled={formMode === 'view'}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-36 min-w-36'>Ngày hiệu lực</Label>
          <FormField type='date' name='ngay_hl' disabled={formMode === 'view'} />
        </div>

        <div className='flex items-center'>
          <Label className='w-36 min-w-36'>Tỷ giá</Label>
          <div className='w-[210px]'>
            <FormField type='number' name='ty_gia' disabled={formMode === 'view'} />
          </div>
        </div>
      </div>
    </div>
  );
};
