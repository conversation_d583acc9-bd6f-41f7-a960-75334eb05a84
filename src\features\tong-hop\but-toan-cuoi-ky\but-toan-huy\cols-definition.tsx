import { GridColDef } from '@mui/x-data-grid';
import dayjs from 'dayjs';

export const getCancelEntryTableColumns = (): GridColDef[] => [
  { field: 'entryNumber', headerName: 'S<PERSON> phiếu nhập', width: 120 },
  { field: 'entryDate', headerName: '<PERSON><PERSON>y c/từ', width: 120 },
  { field: 'entryType', headerName: 'Loại c/từ', width: 180 },
  { field: 'description', headerName: 'Diễn giải', width: 250 },
  { field: 'foreignCurrency', headerName: 'Ngoại tệ', width: 120 },
  { field: 'totalAmount', headerName: 'Tổng phát sinh', width: 150 },
  { field: 'status', headerName: 'Trạng thái', width: 120 }
];

export const getCancelEntryDetailColumns = (): GridColDef[] => [
  { field: 'account', headerName: 'T<PERSON><PERSON> khoản', width: 120 },
  { field: 'accountName', headerName: 'Tên tài khoản', width: 200 },
  { field: 'customerCode', headerName: '<PERSON><PERSON> khách hàng', width: 150 },
  { field: 'debitAmount', headerName: 'Ps nợ VND', width: 120 },
  { field: 'creditAmount', headerName: 'Ps có VND', width: 120 },
  { field: 'group', headerName: 'Nhóm', width: 100 },
  { field: 'description', headerName: 'Diễn giải', width: 200 },
  { field: 'department', headerName: 'Bộ phận', width: 120 },
  { field: 'job', headerName: 'Vụ việc', width: 120 },
  { field: 'contract', headerName: 'Hợp đồng', width: 120 },
  { field: 'paymentPhase', headerName: 'Đợt thanh toán', width: 150 },
  { field: 'agreement', headerName: 'Khế ước', width: 120 },
  { field: 'fee', headerName: 'Phí', width: 100 },
  { field: 'product', headerName: 'Sản phẩm', width: 120 },
  { field: 'lenh_sx', headerName: 'Lệnh sản xuất', width: 120 },
  { field: 'cp_khong_hl', headerName: 'C/p không h/lệ', width: 150 }
];

export const getCancelEntryFeeColumns = (): GridColDef[] => [
  { field: 'so_hd', headerName: 'Số hóa đơn', width: 120 },
  { field: 'ky_hieu', headerName: 'Ký hiệu', width: 120 },
  { field: 'ngay_hd', headerName: 'Ngày hóa đơn', width: 120 },
  { field: 'thue_suat', headerName: 'Thuế suất', width: 120 },
  { field: 'mau_hd', headerName: 'Mẫu hóa đơn', width: 120 },
  { field: 'mau_bc', headerName: 'Mẫu báo cáo', width: 120 },
  { field: 'mau_tc', headerName: 'Mẫu tính chất', width: 120 },
  { field: 'ma_ncc', headerName: 'Mã ncc', width: 120 },
  { field: 'ten_ncc', headerName: 'Tên NCC', width: 150 },
  { field: 'dia_chi', headerName: 'Địa chỉ', width: 200 },
  { field: 'ma_so_thue', headerName: 'Mã số thuế', width: 120 },
  { field: 'ten_hh_dv', headerName: 'Tên hàng hóa/dịch vụ', width: 200 },
  { field: 'tien_hang_vnd', headerName: 'Tiền hàng VND', width: 150 },
  { field: 'tk_thue', headerName: 'Tk thuế', width: 150 },
  { field: 'tk_doi_ung', headerName: 'Tk đối ứng', width: 150 },
  { field: 'thue_vnd', headerName: 'Thuế VND', width: 120 },
  { field: 'cuc_thue', headerName: 'Cục thuế', width: 120 },
  { field: 'ma_thanh_toan', headerName: 'Mã thanh toán', width: 150 },
  { field: 'ghi_chu', headerName: 'Ghi chú', width: 200 },
  { field: 'department', headerName: 'Bộ phận', width: 120 },
  { field: 'job', headerName: 'Vụ việc', width: 120 },
  { field: 'contract', headerName: 'Hợp đồng', width: 120 },
  { field: 'paymentPhase', headerName: 'Đợt thanh toán', width: 150 },
  { field: 'agreement', headerName: 'Khế ước', width: 120 },
  { field: 'fee', headerName: 'Phí', width: 100 },
  { field: 'product', headerName: 'Sản phẩm', width: 120 },
  { field: 'lenh_sx', headerName: 'Lệnh sản xuất', width: 120 },
  { field: 'cp_khong_hl', headerName: 'C/p không h/lệ', width: 150 }
];

export const ChungTuSearchColumns = (): GridColDef[] => [
  { field: 'stt', headerName: 'ID', width: 100 },
  { field: 'i_so_ct', headerName: 'Số c/từ', width: 120 },
  {
    field: 'created',
    headerName: 'Ngày c/từ',
    width: 120,
    renderCell: ({ value }) => (value ? dayjs(value).format('DD/MM/YYYY') : '')
  },
  { field: 'ngoai_te', headerName: 'Ngoại tệ', width: 120 },
  { field: 'ma_ct', headerName: 'Mã chứng từ', width: 180 },
  { field: 'ten_ct', headerName: 'Tên chứng từ', width: 200 },
  { field: 'tong_ps', headerName: 'Tổng phát sinh', width: 150 }
];

export const SoChungTuSearchColumns = (): GridColDef[] => [
  { field: 'ma_quyen', headerName: 'Mã quyển', width: 120 },
  { field: 'ten_quyen', headerName: 'Tên quyển', width: 180 },
  { field: 'so_khai_bao', headerName: 'Số khai báo', width: 150 }
];
