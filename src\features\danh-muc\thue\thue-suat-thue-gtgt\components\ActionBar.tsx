import { Eye, FileDown, FileText, Pencil, Plus, RefreshCw, Table, Trash } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito/action-button';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';

export interface ActionBarProps {
  onAddClick: () => void;
  onEditClick: () => void;
  onDeleteClick: () => void;
  onCopyClick: () => void;
  onViewClick: () => void;
  onRefreshClick?: () => void;
  isEditDisabled?: boolean;
  isViewDisabled?: boolean;
  isDeleteDisabled?: boolean;
  isCopyDisabled?: boolean;
}

export const ActionBar = ({
  onAddClick,
  onEditClick,
  onDeleteClick,
  onCopyClick,
  onViewClick,
  onRefreshClick,
  isEditDisabled = true,
  isViewDisabled = true,
  isDeleteDisabled = true,
  isCopyDisabled = true
}: ActionBarProps) => (
  <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Thuế suất thuế GTGT</h1>}>
    <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} variant='primary' />
    <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} disabled={isEditDisabled} />
    <AritoActionButton
      title='Xoá'
      variant='destructive'
      icon={Trash}
      onClick={onDeleteClick}
      disabled={isDeleteDisabled}
    />
    <AritoActionButton title='Sao chép' icon={FileText} onClick={onCopyClick} disabled={isCopyDisabled} />
    <AritoActionButton title='Xem' icon={Eye} onClick={onViewClick} disabled={isViewDisabled} />
    <AritoMenuButton
      title='Khác'
      items={[
        {
          title: 'Refresh',
          icon: RefreshCw,
          onClick: onRefreshClick || (() => {}),
          group: 0
        },
        {
          title: 'Cố định cột',
          icon: Table,
          onClick: () => {},
          group: 0
        },
        {
          title: 'Kết xuất dữ liệu',
          icon: FileDown,
          onClick: () => {},
          group: 1
        }
      ]}
    />
  </AritoActionBar>
);
