'use client';

import Split from 'react-split';
import { AritoDataTables, LoadingOverlay } from '@/components/custom/arito';
import { SearchDialog, FormDialog, ActionBar } from './components';
import { useButToanDieuChinhGiamCongNo } from '@/hooks';
import { useTableData, useSearchState } from './hooks';
import { useFormState, useRows } from '@/hooks';

export default function ButToanDieuChinhCongNo() {
  const {
    butToanDieuChinhGiamCongNos,
    isLoading,
    isLoadingDetail,
    addButToanDieuChinhGiamCongNo,
    updateButToanDieuChinhGiamCongNo,
    deleteButToanDieuChinhGiamCongNo,
    refreshButToanDieuChinhGiamCongNos
  } = useButToanDieuChinhGiamCongNo();
  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRows();
  const {
    showForm,
    showDelete,
    formMode,
    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick
  } = useFormState();
  const { showSearchDialog, setShowSearchDialog, handleSearch, handleSearchSubmit } = useSearchState();
  const { detailRows, tables, setDetailRows } = useTableData();

  const handleSubmit = async (data: any) => {
    try {
      if (formMode === 'add') {
        await addButToanDieuChinhGiamCongNo(data);
      } else if (formMode === 'edit') {
        await updateButToanDieuChinhGiamCongNo(selectedObj.uuid, data);
      }
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  return (
    <div className='flex h-full min-h-[calc(100vh-120px)] w-full flex-col overflow-hidden'>
      {showSearchDialog && (
        <SearchDialog
          open={showSearchDialog}
          onClose={() => setShowSearchDialog(false)}
          onSearch={handleSearchSubmit}
        />
      )}

      {showForm && (
        <FormDialog
          formMode={formMode}
          initialData={
            selectedObj && (formMode === 'edit' || formMode === 'view' || (formMode === 'add' && isCopyMode))
              ? selectedObj
              : formMode === 'add' && !isCopyMode
                ? undefined
                : undefined
          }
          detailRows={detailRows}
          setDetailRows={setDetailRows}
          onClose={handleCloseForm}
          onSubmit={handleSubmit}
        />
      )}

      {!showForm && (
        <>
          <ActionBar
            onAdd={handleAddClick}
            onEdit={handleEditClick}
            onDelete={handleDeleteClick}
            onCopy={handleCopyClick}
            onSearch={handleSearch}
            onRefresh={refreshButToanDieuChinhGiamCongNos}
            isViewDisabled={!selectedObj}
          />

          <Split
            className='flex flex-1 flex-col overflow-hidden'
            direction='vertical'
            sizes={[50, 50]}
            minSize={200}
            gutterSize={4}
            gutterAlign='center'
            snapOffset={30}
            dragInterval={1}
            cursor='row-resize'
          >
            <div className='w-full overflow-hidden'>
              {isLoading && <LoadingOverlay />}
              {!isLoading && (
                <AritoDataTables
                  tables={tables}
                  onRowClick={handleRowClick}
                  selectedRowId={selectedRowIndex || undefined}
                />
              )}
            </div>
          </Split>
        </>
      )}
    </div>
  );
}
