import { Co<PERSON>, Pencil, Plus, Trash, FileSearch } from 'lucide-react';
import { AritoIcon, AritoActionButton, AritoActionBar, AritoMenuButton } from '@/components/custom/arito';

interface ActionBarProps {
  selectedObj?: any;
  onAddIconClick: () => void;
  onEditIconClick: () => void;
  onDeleteIconClick: () => void;
  onCopyIconClick: () => void;
  onWatchIconClick: () => void;
  onRefreshClick?: () => void;
}

export const ActionBar: React.FC<ActionBarProps> = ({
  selectedObj,
  onAddIconClick,
  onEditIconClick,
  onDeleteIconClick,
  onCopyIconClick,
  onWatchIconClick,
  onRefreshClick
}) => {
  return (
    <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Danh mục bộ phận</h1>}>
      <AritoActionButton title='Thêm' icon={Plus} onClick={onAddIconClick} />
      <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditIconClick} disabled={!selectedObj} />
      <AritoActionButton title='Xóa' icon={Trash} onClick={onDeleteIconClick} disabled={!selectedObj} />
      <AritoActionButton title='Sao chép' icon={Copy} onClick={onCopyIconClick} disabled={!selectedObj} />
      <AritoActionButton title='Xem' icon={FileSearch} onClick={onWatchIconClick} disabled={!selectedObj} />

      <AritoMenuButton
        items={[
          {
            title: 'Refresh',
            icon: <AritoIcon icon={15} />,
            onClick: onRefreshClick || (() => {}),
            group: 0
          },
          {
            title: 'Cố định cột',
            icon: <AritoIcon icon={16} />,
            onClick: () => {},
            group: 0
          },
          {
            title: 'Kết xuất dữ liệu',
            icon: <AritoIcon icon={555} />,
            onClick: () => {},
            group: 1
          },
          {
            title: 'Tải mẫu Excel',
            icon: <AritoIcon icon={28} />,
            onClick: () => {},
            group: 1
          },
          {
            title: 'Lấy dữ liệu từ tập tin',
            icon: <AritoIcon icon={29} />,
            onClick: () => {},
            group: 1
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
