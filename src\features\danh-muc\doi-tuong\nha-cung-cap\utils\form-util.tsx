import { <PERSON><PERSON>, Log<PERSON>ut, <PERSON><PERSON><PERSON>, Pin, Plus, Refresh<PERSON><PERSON><PERSON>, Trash, Save, LucideIcon } from 'lucide-react';
import React from 'react';
import { AritoActionButton } from '@/components/custom/arito';
import { FormMode } from '@/types/form';

/**
 * Get the title for the form dialog based on form mode and active tab
 */
export const getFormTitle = (formMode: FormMode, activeTab: string, ma_ncc?: string) => {
  if (formMode !== 'view') {
    return formMode === 'add' ? 'Mới' : 'Sửa';
  }

  switch (activeTab) {
    case 'info':
      return `${ma_ncc}`;
    case 'price':
      return `Giá mua - ${ma_ncc}`;
    case 'review':
      return `Đánh giá - ${ma_ncc}`;
    default:
      return 'Phiếu chi tiền';
  }
};

// Types for flexible button configuration
export interface ActionButtonConfig {
  title: string;
  icon: LucideIcon;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  disabled?: boolean;
  variant?: 'primary' | 'secondary' | 'destructive';
}

export interface TabButtonConfig {
  [tabId: string]: ActionButtonConfig[];
}

export interface FlexibleHandlers {
  [key: string]: (() => void) | undefined;
  handleClose: () => void;
}

/**
 * Helper function to create button configurations easily
 */
export const createButtonConfig = (
  title: string,
  icon: LucideIcon,
  onClick?: () => void,
  options?: {
    type?: 'button' | 'submit' | 'reset';
    disabled?: boolean;
    variant?: 'primary' | 'secondary' | 'destructive';
  }
): ActionButtonConfig => ({
  title,
  icon,
  onClick,
  type: options?.type || 'button',
  disabled: options?.disabled || false,
  variant: options?.variant || 'secondary'
});

/**
 * Get the action buttons for the form dialog based on form mode and active tab (Flexible version)
 */
export const getFormActionButtons = (
  formMode: FormMode,
  activeTab: string,
  handlers: FlexibleHandlers,
  customConfig?: TabButtonConfig
) => {
  // Default configuration for backward compatibility
  const defaultConfig: TabButtonConfig = {
    info: [
      { title: 'Thêm', icon: Plus, onClick: handlers.onAdd },
      { title: 'Sửa', icon: Pencil, onClick: handlers.onEdit },
      { title: 'Xóa', icon: Trash, onClick: handlers.onDelete, variant: 'destructive' },
      { title: 'Sao chép', icon: Copy, onClick: handlers.onCopy },
      { title: 'Đóng', icon: LogOut, onClick: handlers.handleClose }
    ],
    price: [
      { title: 'Thêm', icon: Plus, onClick: handlers.onAdd },
      { title: 'Sửa', icon: Pencil, onClick: handlers.onEdit },
      { title: 'Xóa', icon: Trash, onClick: handlers.onDelete, variant: 'destructive' },
      { title: 'Sao chép', icon: Copy, onClick: handlers.onCopy },
      { title: 'Đóng', icon: LogOut, onClick: handlers.handleClose }
    ],
    review: [
      { title: 'Thêm', icon: Plus, onClick: handlers.onAdd },
      { title: 'Sửa', icon: Pencil, onClick: handlers.onEdit },
      { title: 'Đóng', icon: LogOut, onClick: handlers.handleClose }
    ],
    default: [
      { title: 'Lưu', icon: Save, type: 'submit', onClick: () => console.log('🔘 Save button clicked from form-util') },
      { title: 'Đóng', icon: LogOut, onClick: handlers.handleClose }
    ]
  };

  // Use custom config if provided, otherwise use default
  const config = customConfig || defaultConfig;

  // For non-view modes, show default buttons (Save/Close)
  if (formMode !== 'view') {
    const defaultButtons = config.default || defaultConfig.default;
    return (
      <>
        {defaultButtons.map((button, index) => (
          <AritoActionButton
            key={`${button.title}-${index}`}
            title={button.title}
            icon={button.icon}
            onClick={button.onClick}
            type={button.type}
            disabled={button.disabled}
            variant={button.variant}
          />
        ))}
      </>
    );
  }

  // For view mode, show tab-specific buttons
  const tabButtons = config[activeTab] || config.default || defaultConfig.default;

  return (
    <>
      {tabButtons.map((button, index) => (
        <AritoActionButton
          key={`${button.title}-${index}`}
          title={button.title}
          icon={button.icon}
          onClick={button.onClick}
          type={button.type}
          disabled={button.disabled}
          variant={button.variant}
        />
      ))}
    </>
  );
};

/**
 * Example usage of flexible button configuration:
 *
 * // Custom handlers for different tabs
 * const customHandlers = {
 *   handleClose: () => console.log('Close'),
 *   onAdd: () => console.log('Add'),
 *   onEdit: () => console.log('Edit'),
 *   onDelete: () => console.log('Delete'),
 *   onCustomAction: () => console.log('Custom action'),
 *   onSpecialSave: () => console.log('Special save')
 * };
 *
 * // Custom button configuration for different tabs
 * const customButtonConfig: TabButtonConfig = {
 *   'basic-info': [
 *     createButtonConfig('Thêm', Plus, customHandlers.onAdd, { variant: 'primary' }),
 *     createButtonConfig('Sửa', Pencil, customHandlers.onEdit),
 *     createButtonConfig('Xóa', Trash, customHandlers.onDelete, { variant: 'destructive' }),
 *     createButtonConfig('Đóng', LogOut, customHandlers.handleClose)
 *   ],
 *   'other': [
 *     createButtonConfig('Hành động đặc biệt', RefreshCcw, customHandlers.onCustomAction),
 *     createButtonConfig('Đóng', LogOut, customHandlers.handleClose)
 *   ],
 *   'default': [
 *     createButtonConfig('Lưu đặc biệt', Save, customHandlers.onSpecialSave, { type: 'submit', variant: 'primary' }),
 *     createButtonConfig('Đóng', LogOut, customHandlers.handleClose)
 *   ]
 * };
 *
 * // Usage in component:
 * const buttons = getFormActionButtons(formMode, activeTab, customHandlers, customButtonConfig);
 */
