import { Button } from '@mui/material';
import { useState } from 'react';
import { AritoDialog, AritoIcon } from '@/components/custom/arito';

interface DeleteDialogProps {
  open: boolean;
  onClose: () => void;
  selectedObj: any | null;
  onConfirmDelete: () => Promise<void>;
}

function DeleteDialog({ onClose, open, selectedObj, onConfirmDelete }: DeleteDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleDelete = async () => {
    if (!selectedObj) return;

    setIsDeleting(true);
    setError(null);

    try {
      await onConfirmDelete();
    } catch (err: any) {
      setError(err.message || 'Có lỗi xảy ra khi xóa dữ liệu');
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='<PERSON><PERSON><PERSON> kênh bán hàng'
      maxWidth='lg'
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={260} />}
      actions={
        <>
          <Button
            className='bg-[rgba(15,118,110,0.9)] text-white hover:bg-[rgba(15,118,110,1)]'
            onClick={handleDelete}
            type='button'
            variant='contained'
            disabled={isDeleting}
          >
            <AritoIcon icon={884} marginX='4px' />
            {isDeleting ? 'Đang xử lý...' : 'Đồng ý'}
          </Button>
          <Button onClick={onClose} variant='outlined' disabled={isDeleting}>
            <AritoIcon icon={885} marginX='4px' />
            Huỷ
          </Button>
        </>
      }
    >
      <div className='min-w-96 p-4'>
        <p className='text-base font-medium'>Bạn có chắc chắn muốn xóa?</p>
        {error && <p className='mt-2 text-red-600'>{error}</p>}
      </div>
    </AritoDialog>
  );
}

export default DeleteDialog;
