import { GridCellParams } from '@mui/x-data-grid';
import React from 'react';
import { InputTable } from '@/components/custom/arito';
import { SelectedCellInfo } from '../../../hooks';
import { getDataTableColumns } from './columns';
import { FormMode } from '@/types/form';
import { useTaxRate } from '@/hooks';
import ActionBar from './ActionBar';

interface ThueTabProps {
  formMode: FormMode;
  rows: { uuid?: string | null }[];
  selectedRowUuid?: string | null;
  selectedCell?: SelectedCellInfo | null;
  onRowClick?: (params: any) => void;
  onCellClick?: (params: GridCellParams) => void;
  onAddRow: () => void;
  onDeleteRow: () => void;
  onCopyRow: () => void;
  onPasteRow: () => void;
  onMoveRow: (direction: 'up' | 'down') => void;
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void;
}

export const ThueTab: React.FC<ThueTabProps> = ({
  formMode,
  rows,
  selectedRowUuid,
  onRowClick,
  onAddRow,
  onDeleteRow,
  onCopyRow,
  onPasteRow,
  onMoveRow,
  onCellValueChange
}) => {
  const { taxRates } = useTaxRate();
  return (
    <InputTable
      rows={rows}
      onRowClick={onRowClick}
      selectedRowId={selectedRowUuid || undefined}
      columns={getDataTableColumns(taxRates, onCellValueChange)}
      getRowId={row => row?.uuid || ''}
      actionButtons={
        <ActionBar
          formMode={formMode}
          handleAddRow={onAddRow}
          handleDeleteRow={onDeleteRow}
          handleCopyRow={onCopyRow}
          handlePasteRow={onPasteRow}
          handleMoveRow={onMoveRow}
          handleExport={() => console.log('Export clicked')}
          handlePin={() => console.log('Pin clicked')}
        />
      }
    />
  );
};
