import { useState, useEffect, useCallback } from 'react';
import { QuyenChungTu, QuyenChungTuResponse } from '@/types/schemas';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/api';

interface UseQuyenChungTuByChungTuReturn {
  quyenChungTus: QuyenChungTu[];
  isLoading: boolean;
  error: Error | null;
  refreshQuyenChungTus: () => Promise<void>;
}

interface UseQuyenChungTuByChungTuParams {
  chung_tu_uuid?: string;
  ma_ct?: string;
  ngay_hl?: string;
  fetchAll?: boolean; // New option to fetch all records when no filters are provided
}

/**
 * Hook for fetching document books (QuyenChungTu) filtered by ChungTu UUID or document code
 *
 * This hook calls the endpoint: erp/document-books/by-chung-tu/?chung_tu_uuid={uuid}&ma_ct={code}&ngay_hl={date}
 * to fetch document books that are specifically associated with a given ChungTu document.
 *
 * @param params - Object containing chung_tu_uuid, ma_ct, and ngay_hl parameters
 * @param params.chung_tu_uuid - Optional UUID of the ChungTu document to filter by
 * @param params.ma_ct - Optional document code to filter by
 * @param params.ngay_hl - Optional effective date parameter (defaults to current date)
 * @returns Object containing quyenChungTus array, loading state, error state, and refresh function
 */
export const useQuyenChungTuByChungTu = (params: UseQuyenChungTuByChungTuParams): UseQuyenChungTuByChungTuReturn => {
  const [quyenChungTus, setQuyenChungTus] = useState<QuyenChungTu[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  const { entity } = useAuth();

  const fetchQuyenChungTuByChungTu = useCallback(async (): Promise<void> => {
    if (!entity?.slug) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Build query parameters
      const queryParams = new URLSearchParams();

      // Add chung_tu_uuid if provided
      if (params.chung_tu_uuid) {
        queryParams.append('chung_tu_uuid', params.chung_tu_uuid);
      }

      // Add ma_ct if provided
      if (params.ma_ct) {
        queryParams.append('ma_ct', params.ma_ct);
      }

      // Use provided ngay_hl or default to current date
      // Format date as DD-MM-YYYY instead of YYYY-MM-DD
      let effectiveDate: string;
      if (params.ngay_hl) {
        // If ngay_hl is provided, convert from YYYY-MM-DD to DD-MM-YYYY
        const date = new Date(params.ngay_hl);
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        effectiveDate = `${year}-${month}-${day}`;
      } else {
        // Default to current date in DD-MM-YYYY format
        const now = new Date();
        const day = String(now.getDate()).padStart(2, '0');
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const year = now.getFullYear();
        effectiveDate = `${year}-${month}-${day}`;
      }
      queryParams.append('ngay_hl', effectiveDate);

      const response = await api.get<QuyenChungTuResponse>(
        `/entities/${entity.slug}/erp/document-books/by-chung-tu/?${queryParams.toString()}`
      );

      const mappedData: QuyenChungTu[] = response.data.results || [];
      setQuyenChungTus(mappedData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error fetching document books by ChungTu';
      setError(new Error(errorMessage));
      console.error('Error fetching document books by ChungTu:', err);
      setQuyenChungTus([]);
    } finally {
      setIsLoading(false);
    }
  }, [entity?.slug, params.chung_tu_uuid, params.ma_ct, params.ngay_hl]);

  const refreshQuyenChungTus = useCallback(async (): Promise<void> => {
    await fetchQuyenChungTuByChungTu();
  }, [fetchQuyenChungTuByChungTu]);

  useEffect(() => {
    fetchQuyenChungTuByChungTu();
  }, [fetchQuyenChungTuByChungTu]);

  return {
    quyenChungTus,
    isLoading,
    error,
    refreshQuyenChungTus
  };
};
