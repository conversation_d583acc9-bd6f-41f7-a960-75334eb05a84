import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FileSelectField } from '@/components/custom/arito/form/search-fields';
import { FormFieldActions, FormFieldState } from '../../hooks/useFormState';
import { FormField } from '@/components/custom/arito/form/form-field';
import { QUERY_KEYS, khachHangSearchColumns } from '@/constants';
import { Label } from '@/components/ui/label';
import { KhachHang } from '@/types/schemas';
import { FormMode } from '@/types/form';

interface OtherTabProps {
  formMode: FormMode;
  formState: {
    state: FormFieldState;
    actions: FormFieldActions;
  };
}

export default function OtherTab({ formMode, formState: { state, actions } }: OtherTabProps) {
  return (
    <div className='px-4'>
      <div className='flex flex-col justify-between lg:flex-row'>
        {/* Left column */}
        <div className='w-full space-y-1'>
          <div className='flex items-center'>
            <Label className='w-32 min-w-32 text-sm font-medium'><PERSON><PERSON><PERSON> thuế</Label>
            <SearchField<KhachHang>
              type='text'
              columnDisplay='customer_code'
              displayRelatedField='customer_name'
              searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}`}
              searchColumns={khachHangSearchColumns}
              value={state.coQuanThue?.customer_code || ''}
              relatedFieldValue={state.coQuanThue?.customer_name || ''}
              onRowSelection={actions.setCoQuanThue}
              disabled={formMode === 'view'}
              dialogTitle='Danh mục đối tượng'
            />
          </div>

          <div className='flex items-center'>
            <Label className='w-32 min-w-32 text-sm font-medium'>Lý do hủy</Label>
            <div className='flex-1'>
              <FormField
                name='ly_do_huy'
                type='text'
                disabled={formMode === 'view'}
                placeholder='Nhập lý do hủy lá đơn'
              />
            </div>
          </div>

          <div className='flex items-center'>
            <Label className='w-32 min-w-32 text-sm font-medium'>Lý do điều chỉnh</Label>
            <div className='flex-1'>
              <FormField
                name='ly_do'
                type='text'
                disabled={formMode === 'view'}
                placeholder='Nhập lý do thay thế hoặc điều chỉnh hóa đơn điện tử'
              />
            </div>
          </div>

          <div className='flex items-center'>
            <Label className='w-32 min-w-32 text-sm font-medium'>Nhóm sản phẩm</Label>
            <div className='flex-1'>
              <FormField name='ten_vt_thue' type='text' disabled={formMode === 'view'} />
            </div>
          </div>

          <div className='flex items-center'>
            <FileSelectField
              formMode={formMode}
              className='flex items-center'
              labelClassName='w-32'
              label='Chọn files'
            />
          </div>

          <div className='flex items-center'>
            <Label className='w-32 min-w-32 text-sm font-medium'>Ghi chú</Label>
            <div className='flex-1'>
              <FormField name='ghi_chu' type='text' disabled={formMode === 'view'} placeholder='Nhập ghi chú' />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
