'use client';
import { useState } from 'react';
import { useDieuChuyenBoPhanSuDungTSCD } from '@/hooks/queries/useDieuChuyenBoPhanSuDungTSCD';
import { AritoDataTables, DeleteDialog, LoadingOverlay } from '@/components/custom/arito';
import { DieuChuyenBoPhanSuDungTSCDInput } from '@/types/schemas';
import { getDataTableColumns } from './cols-definition';
import { ActionBar, FormDialog } from './components';
import { useFormState, useRows } from '@/hooks';

export default function DieuChuyenBoPhanSuDungTSCD() {
  const [showSearchDialog, setShowSearchDialog] = useState(true);
  const [showTable, setShowTable] = useState(true);
  const [showDownloadDialog, setShowDownloadDialog] = useState(false);

  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRows();
  const {
    showForm,
    showDelete,
    formMode,
    isCopyMode,
    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleDeleteClick,
    handleCopyClick
  } = useFormState();

  const {
    dieuChuyenBoPhanSuDungTSCDs,
    isLoading,
    refreshDieuChuyenBoPhanSuDungTSCDs,
    deleteDieuChuyenBoPhanSuDungTSCD,
    addDieuChuyenBoPhanSuDungTSCD,
    updateDieuChuyenBoPhanSuDungTSCD
  } = useDieuChuyenBoPhanSuDungTSCD();

  const handleSearch = () => {
    setShowSearchDialog(false);
    setShowTable(true);
  };

  const handleFormSubmit = async (data: DieuChuyenBoPhanSuDungTSCDInput) => {
    try {
      if (formMode === 'add' && isCopyMode) {
        await addDieuChuyenBoPhanSuDungTSCD(data);
      } else if (formMode === 'edit') {
        await updateDieuChuyenBoPhanSuDungTSCD(selectedObj.uuid, data);
      } else if (formMode === 'add') {
        await addDieuChuyenBoPhanSuDungTSCD(data);
      }
      await refreshDieuChuyenBoPhanSuDungTSCDs();
      handleCloseForm();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  const tables = [
    {
      name: '',
      rows: dieuChuyenBoPhanSuDungTSCDs,
      columns: getDataTableColumns()
    }
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {showTable && (
        <div className='w-full'>
          <ActionBar
            onAddIconClick={handleAddClick}
            onEditIconClick={handleEditClick}
            onDeleteIconClick={handleDeleteClick}
            onCopyIconClick={handleCopyClick}
            onWatchIconClick={() => setShowSearchDialog(true)}
            onRefreshClick={() => setShowDownloadDialog(true)}
          />
          {isLoading && <LoadingOverlay />}
          {!isLoading && (
            <AritoDataTables
              tables={tables}
              selectedRowId={selectedRowIndex || undefined}
              onRowClick={handleRowClick}
            />
          )}
        </div>
      )}

      {showForm && (
        <FormDialog
          open={showForm}
          onClose={handleCloseForm}
          onSubmit={handleFormSubmit}
          mode={formMode}
          initialData={
            selectedObj && (formMode === 'edit' || formMode === 'view' || (formMode === 'add' && isCopyMode))
              ? selectedObj
              : formMode === 'add' && !isCopyMode
                ? undefined
                : undefined
          }
        />
      )}

      {showDelete && (
        <DeleteDialog
          open={showDelete}
          onClose={handleCloseDelete}
          selectedObj={selectedObj}
          deleteObj={() => deleteDieuChuyenBoPhanSuDungTSCD(selectedObj?.uuid || '')}
          clearSelection={clearSelection}
        />
      )}
    </div>
  );
}
