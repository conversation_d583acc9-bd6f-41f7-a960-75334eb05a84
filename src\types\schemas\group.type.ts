/**
 * TypeScript interface for Group model
 *
 * This interface represents the structure of the Group model from the backend.
 * It defines groups used for categorizing various entities in the system.
 */

import { ApiResponse } from '../api.type';

/**
 * Enum for main group types (for backward compatibility)
 */
export enum GroupTypeMain {
  CUSTOMER = 'KH',
  SUPPLIER = 'NCC',
  EMPLOYEE = 'NV',
  MATERIAL = 'VT',
  FEE = 'PHI',
  REGION = 'RG',
  TOOL = 'CC',
  OTHER = '#'
}

/**
 * Enum for main group type labels
 */
export enum GroupTypeMainLabel {
  CUSTOMER = 'Customer',
  SUPPLIER = 'Supplier',
  EMPLOYEE = 'Employee',
  MATERIAL = 'Material',
  FEE = 'Fee',
  REGION = 'Region',
  TOOL = 'Tool',
  OTHER = 'Other'
}

/**
 * Enum for specific group types
 *
 * This enum directly maps to the backend GROUP_TYPE_CHOICES in GroupAbstractModel
 */
export enum GroupType {
  // Customer types
  CUSTOMER1 = 'KH1',
  CUSTOMER2 = 'KH2',
  CUSTOMER3 = 'KH3',

  // Supplier types
  SUPPLIER1 = 'NCC1',
  SUPPLIER2 = 'NCC2',
  SUPPLIER3 = 'NCC3',

  // Employee types
  EMPLOYEE1 = 'NV1',
  EMPLOYEE2 = 'NV2',
  EMPLOYEE3 = 'NV3',

  // Material types
  MATERIAL1 = 'VT1',
  MATERIAL2 = 'VT2',
  MATERIAL3 = 'VT3',

  // Fee types
  FEE1 = 'PHI1',
  FEE2 = 'PHI2',
  FEE3 = 'PHI3',

  // Region types
  REGION1 = 'RG1',
  REGION2 = 'RG2',
  REGION3 = 'RG3',

  // Asset types
  KHO_HANG1 = 'KHOHANG1',
  KHO_HANG2 = 'KHOHANG2',
  KHO_HANG3 = 'KHOHANG3',

  // Tool types
  TOOL1 = 'CC1',
  TOOL2 = 'CC2',
  TOOL3 = 'CC3',

  // Factor types
  FACTOR1 = 'YT1',

  // Tax types
  TAX = 'TAX',

  // Other
  OTHER = '#'
}

/**
 * Enum for specific group type labels
 *
 * This maps directly to the backend GROUP_TYPES in GroupModelService
 */
export enum GroupTypeLabel {
  // Customer types
  CUSTOMER1 = 'Customer 1',
  CUSTOMER2 = 'Customer 2',
  CUSTOMER3 = 'Customer 3',

  // Supplier types
  SUPPLIER1 = 'Supplier 1',
  SUPPLIER2 = 'Supplier 2',
  SUPPLIER3 = 'Supplier 3',

  // Employee types
  EMPLOYEE1 = 'Employee 1',
  EMPLOYEE2 = 'Employee 2',
  EMPLOYEE3 = 'Employee 3',

  // Material types
  MATERIAL1 = 'Material 1',
  MATERIAL2 = 'Material 2',
  MATERIAL3 = 'Material 3',

  // Fee types
  FEE1 = 'Fee1',
  FEE2 = 'Fee2',
  FEE3 = 'Fee3',

  // Region types
  REGION1 = 'Region 1',
  REGION2 = 'Region 2',
  REGION3 = 'Region 3',

  // Tool types
  TOOL1 = 'Tool 1',
  TOOL2 = 'Tool 2',
  TOOL3 = 'Tool 3',
  // Factor types
  FACTOR1 = 'Factor 1',

  // Other
  OTHER = 'Other'
}

/**
 * Full mapping of group type codes to their human-readable labels
 */
export const GROUP_TYPE_MAPPING: Record<string, string> = {
  // Main types
  KH: 'Customer',
  NCC: 'Supplier',
  NV: 'Employee',
  VT: 'Material',
  PHI: 'Fee',
  RG: 'Region',
  CC: 'Tool',
  DV: 'Unit',
  '#': 'Other',
  TAX: 'Tax Group',

  // Specific types
  KH1: 'Customer 1',
  KH2: 'Customer 2',
  KH3: 'Customer 3',
  NCC1: 'Supplier 1',
  NCC2: 'Supplier 2',
  NCC3: 'Supplier 3',
  NV1: 'Employee 1',
  NV2: 'Employee 2',
  NV3: 'Employee 3',
  VT1: 'Material 1',
  VT2: 'Material 2',
  VT3: 'Material 3',
  PHI1: 'Fee1',
  PHI2: 'Fee2',
  PHI3: 'Fee3',
  RG1: 'Region 1',
  RG2: 'Region 2',
  RG3: 'Region 3',
  CC1: 'Tool 1',
  CC2: 'Tool 2',
  CC3: 'Tool 3',
  DV1: 'Unit 1',
  YT1: 'Factor 1'
};

export interface Group {
  /**
   * Unique identifier (UUID)
   */
  uuid: string;

  /**
   * Reference to the entity model
   */
  entity_model: string;

  /**
   * Group code
   */
  ma_nhom: string;

  /**
   * Group name
   */
  ten_phan_nhom: string;

  /**
   * Alternative group name
   */
  ten2?: string | null;

  /**
   * Group status
   */
  trang_thai: string;

  /**
   * Group type (KH1=Customer1, NCC1=Supplier1, NV1=Employee1, VT1=Material1, PHI1=Fee1, RG1=Region1, #=Other)
   */
  loai_nhom: string;

  /**
   * Timestamp of creation
   */
  created: string;

  /**
   * Timestamp of last update
   */
  updated: string;
}

/**
 * Type for Group API response
 */
export type GroupResponse = ApiResponse<Group>;

/**
 * Type for creating or updating a Group
 */
export interface GroupInput {
  entity_model: string;
  ma_nhom: string;
  ten_phan_nhom: string;
  ten2?: string | null;
  trang_thai: string;
  loai_nhom: string;
}
