/**
 * TypeScript interfaces for CapNhatDeNghiThanhToan (Payment Request Update) models
 *
 * These interfaces represent the structure of the CapNhatDeNghiThanhToan and ChiTietCapNhatDeNghiThanhToan
 * models from the backend, used for managing payment request updates in the system.
 */

import { ApiResponse } from '../api.type';

/**
 * Interface for CapNhatDeNghiThanhToan (Payment Request Update) model
 */
export interface CapNhatDeNghiThanhToan {
  /** Unique identifier */
  uuid: string;

  /** Reference to the entity model (foreign key) */
  entity_model?: string;

  /** Employee code */
  ma_ngv?: string;

  /** Budget flag (Y/N) */
  ns_yn?: string;

  /** Person name */
  ong_ba?: string;

  /** Address */
  dia_chi?: string;

  /** Description */
  dien_giai?: string;

  /** Document number sequence */
  i_so_ct?: number;

  /** Document date */
  ngay_ct?: string;

  /** Last document date */
  ngay_lct?: string;

  /** Exchange rate */
  ty_gia?: number;

  /** Foreign currency code (UUID) */
  ma_nt?: string;

  /** Status */
  status?: string;

  /** Original document number */
  so_ct_goc?: string;

  /** Original document description */
  dien_giai_ct_goc?: string;

  /** Email notification */
  email_tq?: string;

  /** Total amount in foreign currency */
  t_tien_nt?: number;

  /** Total amount in base currency */
  t_tien?: number;

  /** Total tax in foreign currency */
  t_thue_nt?: number;

  /** Total tax in base currency */
  t_thue?: number;

  /** Created by user */
  created_by?: string;

  /** Timestamp when record was created */
  created?: string;

  /** Timestamp when record was last updated */
  updated?: string;

  // Additional fields for UI
  _selected?: boolean;
  _loading?: boolean;

  // Child details
  chi_tiet?: ChiTietCapNhatDeNghiThanhToan[];
  chi_tiet_data?: ChiTietCapNhatDeNghiThanhToan[];
}

/**
 * Type for CapNhatDeNghiThanhToan API response
 */
export type CapNhatDeNghiThanhToanResponse = ApiResponse<CapNhatDeNghiThanhToan>;

/**
 * Type for paginated CapNhatDeNghiThanhToan API response
 */
export type CapNhatDeNghiThanhToanListResponse = ApiResponse<CapNhatDeNghiThanhToan>;

/**
 * Type for creating or updating a CapNhatDeNghiThanhToan
 */
export interface CapNhatDeNghiThanhToanInput {
  /** Employee code */
  ma_ngv?: string;

  /** Budget flag (Y/N) */
  ns_yn?: string;

  /** Person name */
  ong_ba?: string;

  /** Address */
  dia_chi?: string;

  /** Description */
  dien_giai?: string;

  /** Document number sequence */
  i_so_ct?: number;

  /** Document date */
  ngay_ct?: string;

  /** Last document date */
  ngay_lct?: string;

  /** Exchange rate */
  ty_gia?: number;

  /** Foreign currency code (UUID) */
  ma_nt?: string;

  /** Status */
  status?: string;

  /** Original document number */
  so_ct_goc?: string;

  /** Original document description */
  dien_giai_ct_goc?: string;

  /** Email notification */
  email_tq?: string;

  /** Total amount in foreign currency */
  t_tien_nt?: number;

  /** Total amount in base currency */
  t_tien?: number;

  /** Total tax in foreign currency */
  t_thue_nt?: number;

  /** Total tax in base currency */
  t_thue?: number;

  /** Created by user */
  created_by?: string;

  /** Detail items */
  chi_tiet?: ChiTietCapNhatDeNghiThanhToanInput[];
}

/**
 * Interface for ChiTietCapNhatDeNghiThanhToan (Payment Request Update Detail) model
 */
export interface ChiTietCapNhatDeNghiThanhToan {
  /** Unique identifier */
  uuid: string;

  /** Reference to the parent payment request update (foreign key to CapNhatDeNghiThanhToanModel) */
  bao_cao: string;

  /** Line number */
  line?: number;

  /** Budget item code */
  ma_ctns?: string;

  /** Budget business flag */
  ns_kd?: string;

  /** Exchange rate 2 */
  ty_gia2?: number;

  /** Amount in foreign currency */
  tien_nt?: number;

  /** Amount in base currency */
  tien?: number;

  /** Description */
  dien_giai?: string;

  /** Invoice type code */
  ma_loai_hd?: string;

  /** Tax code */
  ma_thue?: string;

  /** Tax name */
  ten_thue?: string;

  /** Tax rate */
  thue_suat?: number;

  /** Tax amount in foreign currency */
  thue_nt?: number;

  /** Tax amount in base currency */
  thue?: number;

  /** Notes */
  ghi_chu?: string;

  /** Created by user */
  created_by?: string;

  /** Timestamp when record was created */
  created?: string;

  /** Timestamp when record was last updated */
  updated?: string;

  // Additional fields for UI
  _selected?: boolean;
  _editing?: boolean;
}

/**
 * Type for ChiTietCapNhatDeNghiThanhToan API response
 */
export type ChiTietCapNhatDeNghiThanhToanResponse = ApiResponse<ChiTietCapNhatDeNghiThanhToan>;

/**
 * Type for paginated ChiTietCapNhatDeNghiThanhToan API response
 */
export type ChiTietCapNhatDeNghiThanhToanListResponse = ApiResponse<ChiTietCapNhatDeNghiThanhToan>;

/**
 * Type for creating or updating a ChiTietCapNhatDeNghiThanhToan
 */
export interface ChiTietCapNhatDeNghiThanhToanInput {
  /** UUID */
  uuid?: string;

  /** Reference to the parent payment request update */
  bao_cao?: string;

  /** Line number */
  line?: number;

  /** Budget item code */
  ma_ctns?: string;

  /** Budget business flag */
  ns_kd?: string;

  /** Exchange rate 2 */
  ty_gia2?: number;

  /** Amount in foreign currency */
  tien_nt?: number;

  /** Amount in base currency */
  tien?: number;

  /** Description */
  dien_giai?: string;

  /** Invoice type code */
  ma_loai_hd?: string;

  /** Tax code */
  ma_thue?: string;

  /** Tax name */
  ten_thue?: string;

  /** Tax rate */
  thue_suat?: number;

  /** Tax amount in foreign currency */
  thue_nt?: number;

  /** Tax amount in base currency */
  thue?: number;

  /** Notes */
  ghi_chu?: string;

  /** Created by user */
  created_by?: string;
}
