import { GridColDef } from '@mui/x-data-grid';
import {
  QUERY_KEYS,
  accountSearchColumns,
  boPhanSearchColumns,
  chiPhiSearchColumns,
  dotThanhToanSearchColumns,
  hopDongSearchColumns,
  kheUocSearchColumns,
  phiSearchColumns,
  vatTuSearchColumns,
  vuViecSearchColumns,
  thueSearchColumns,
  dichVuSearchColumns,
  lenhSanXuatSearchColumns
} from '@/constants';
import {
  AccountModel,
  BoPhan,
  ChiPhi,
  DichVu,
  DotThanhToan,
  HopDong,
  KheUoc,
  Phi,
  VatTu,
  VuViec
} from '@/types/schemas';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { SearchField } from '@/components/custom/arito';

export const getDataTableColumns = (handleViewClick: () => void): GridColDef[] => [
  {
    field: 'status',
    headerName: 'Trạng thái',
    width: 120
  },
  {
    field: 'ma_tthddt',
    headerName: 'Trạng thái HĐĐT',
    width: 150
  },
  {
    field: 'so_ct',
    headerName: 'Số c/từ',
    width: 120,
    renderCell: params => (
      <p onClick={handleViewClick} className='cursor-pointer hover:text-blue-500 hover:underline'>
        {params.row.so_ct}
      </p>
    )
  },
  {
    field: 'so_ct_hddt',
    headerName: 'Số hóa đơn',
    width: 120
  },
  {
    field: 'ngay_ct',
    headerName: 'Ngày c/từ',
    width: 120
  },
  {
    field: 'ma_kh',
    headerName: 'Mã khách hàng',
    width: 150
  },
  {
    field: 'ten_kh',
    headerName: 'Tên khách hàng',
    width: 200
  },
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 250
  },
  {
    field: 'tk',
    headerName: 'Tk nợ',
    width: 120
  },
  {
    field: 't_tt_nt',
    headerName: 'Tổng tiền',
    width: 150
  },
  {
    field: 'ma_nt',
    headerName: 'Ngoại tệ',
    width: 120
  }
];

export const getDetailTableColumns = (
  taxRates?: any[],
  onCellValueChange?: (rowUuid: string, field: string, newValue: any) => void
): GridColDef[] => [
  {
    field: 'ma_dv',
    headerName: 'Mã dịch vụ',
    width: 120,
    renderCell: params => (
      <SearchField<DichVu>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.DICH_VU}/`}
        searchColumns={dichVuSearchColumns}
        columnDisplay='ma_dv'
        dialogTitle='Danh mục dịch vụ'
        value={params.row.ma_dv_data?.ma_dv || ''}
        onRowSelection={(row: any) => {
          onCellValueChange?.(params.row.uuid, 'ma_dv_data', row);
        }}
      />
    )
  },
  {
    field: 'ten_dv',
    headerName: 'Tên dịch vụ',
    width: 200,
    renderCell: params => <CellField name='ten_dv' type='text' value={params.row.ma_dv_data?.ten_dv || ''} />
  },
  {
    field: 'tk_dt',
    headerName: 'Tk doanh thu',
    width: 120,
    renderCell: params => (
      <SearchField<AccountModel>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
        searchColumns={accountSearchColumns}
        columnDisplay='code'
        dialogTitle='Danh mục tài khoản'
        value={params.row.ma_dv_data?.tk_dt_data?.code || params.row.tk_dt_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'tk_dt_data', row)}
      />
    )
  },
  {
    field: 'ten_tk',
    headerName: 'Tên tài khoản',
    width: 200,
    renderCell: params => <CellField name='ten_tk' type='text' value={params.row.tk_dt_data?.name || ''} />
  },
  {
    field: 'dvt',
    headerName: 'Đvt',
    width: 80,
    renderCell: params => (
      <CellField
        name='dvt'
        type='text'
        value={params.row.ma_dv_data?.dvt || params.row.dvt || ''}
        onValueChange={newValue => onCellValueChange?.(params.row.uuid, 'dvt', newValue)}
      />
    )
  },
  {
    field: 'so_luong',
    headerName: 'Số lượng',
    width: 100,
    renderCell: params => (
      <CellField
        name='so_luong'
        type='number'
        value={params.row.so_luong || 0.0}
        onValueChange={newValue => onCellValueChange?.(params.row.uuid, 'so_luong', newValue)}
      />
    )
  },
  {
    field: 'gia_nt2',
    headerName: 'Giá VND',
    width: 120,
    renderCell: params => (
      <CellField
        name='gia_nt2'
        type='number'
        value={params.row.gia_nt2 || 0.0}
        onValueChange={newValue => onCellValueChange?.(params.row.uuid, 'gia_nt2', newValue)}
      />
    )
  },
  {
    field: 'tien_nt2',
    headerName: 'Thành tiền VND',
    width: 150,
    renderCell: params => (
      <CellField
        name='tien_nt2'
        type='number'
        value={params.row.tien_nt2 || 0.0}
        onValueChange={newValue => onCellValueChange?.(params.row.uuid, 'tien_nt2', newValue)}
      />
    )
  },
  {
    field: 'dien_giai',
    headerName: 'Diễn giải/Tên hàng hóa dịch vụ',
    width: 250,
    renderCell: params => (
      <CellField
        name='dien_giai'
        type='text'
        value={params.row.dien_giai || ''}
        onValueChange={newValue => onCellValueChange?.(params.row.uuid, 'dien_giai', newValue)}
      />
    )
  },
  {
    field: 'tl_ck',
    headerName: 'Tl ck(%)',
    width: 100,
    renderCell: params => (
      <CellField
        name='tl_ck'
        type='number'
        value={params.row.tl_ck || 0.0}
        onValueChange={newValue => onCellValueChange?.(params.row.uuid, 'tl_ck', newValue)}
      />
    )
  },
  {
    field: 'ck_nt',
    headerName: 'Ch.khấu VND',
    width: 120,
    renderCell: params => (
      <CellField
        name='ck_nt'
        type='number'
        value={params.row.ck_nt || 0.0}
        onValueChange={newValue => onCellValueChange?.(params.row.uuid, 'ck_nt', newValue)}
      />
    )
  },
  {
    field: 'tk_ck',
    headerName: 'Tk chiết khấu',
    width: 120,
    renderCell: params => (
      <SearchField<AccountModel>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
        searchColumns={accountSearchColumns}
        columnDisplay='code'
        dialogTitle='Danh mục tài khoản'
        value={params.row.ma_dv_data?.tk_ck_data?.code || params.row.tk_ck_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'tk_ck_data', row)}
      />
    )
  },
  {
    field: 'ma_thue',
    headerName: 'Mã thuế',
    width: 120,
    renderCell: params => (
      <CellField
        name='ma_thue'
        type='select'
        value={params.row.ma_dv_data?.ma_thue || ''}
        options={taxRates?.map(tax => ({ value: tax.uuid, label: tax.ten_thue })) || []}
      />
    )
  },
  {
    field: 'tk_thue_co',
    headerName: 'Tk thuế có',
    width: 120,
    renderCell: params => (
      <SearchField<AccountModel>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
        searchColumns={accountSearchColumns}
        columnDisplay='code'
        dialogTitle='Danh mục tài khoản'
        value={params.row.tk_thue_co_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'tk_thue_co_data', row)}
      />
    )
  },
  {
    field: 'thue_nt',
    headerName: 'Thuế VND',
    width: 120,
    renderCell: params => (
      <CellField
        name='thue_vnd'
        type='number'
        value={params.row.thue_vnd || 0.0}
        onValueChange={newValue => onCellValueChange?.(params.row.uuid, 'thue_vnd', newValue)}
      />
    )
  },
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 120,
    renderCell: params => (
      <SearchField<BoPhan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
        searchColumns={boPhanSearchColumns}
        columnDisplay='ma_bp'
        dialogTitle='Danh mục bộ phận'
        value={params.row.ma_bp_data?.ma_bp || ''}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'ma_bp_data', row)}
      />
    )
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 120,
    renderCell: params => (
      <SearchField<VuViec>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VU_VIEC}/`}
        searchColumns={vuViecSearchColumns}
        columnDisplay='ma_vu_viec'
        dialogTitle='Danh mục vụ việc'
        value={params.row.ma_vv_data?.ma_vu_viec || ''}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'ma_vv_data', row)}
      />
    )
  },
  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 120,
    renderCell: params => (
      <SearchField<HopDong>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.HOP_DONG}/`}
        searchColumns={hopDongSearchColumns}
        columnDisplay='ma_hd'
        dialogTitle='Danh mục hợp đồng'
        value={params.row.ma_hd_data?.ma_hd || ''}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'ma_hd_data', row)}
      />
    )
  },
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 150,
    renderCell: params => (
      <SearchField<DotThanhToan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}/`}
        searchColumns={dotThanhToanSearchColumns}
        columnDisplay='ma_dtt'
        dialogTitle='Danh mục đợt thanh toán'
        value={params.row.ma_dtt_data?.ma_dtt || ''}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'ma_dtt_data', row)}
      />
    )
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 120,
    renderCell: params => (
      <SearchField<KheUoc>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHE_UOC}/`}
        searchColumns={kheUocSearchColumns}
        columnDisplay='ma_ku'
        dialogTitle='Danh mục khế ước'
        value={params.row.ma_ku_data?.ma_ku || ''}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'ma_ku_data', row)}
      />
    )
  },
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 100,
    renderCell: params => (
      <SearchField<Phi>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.PHI}/`}
        searchColumns={phiSearchColumns}
        columnDisplay='ma_phi'
        dialogTitle='Danh mục phí'
        value={params.row.ma_phi_data?.ma_phi || ''}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'ma_phi_data', row)}
      />
    )
  },
  {
    field: 'ma_vt',
    headerName: 'Sản phẩm',
    width: 120,
    renderCell: params => (
      <SearchField<VatTu>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
        searchColumns={vatTuSearchColumns}
        columnDisplay='ma_vt'
        dialogTitle='Danh mục sản phẩm'
        value={params.row.ma_sp_data?.ma_vt || ''}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'ma_sp_data', row)}
      />
    )
  },
  {
    field: 'ma_lsx',
    headerName: 'Lệnh sản xuất',
    width: 150,
    renderCell: params => (
      <SearchField<any>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.LENH_SAN_XUAT}/`}
        searchColumns={lenhSanXuatSearchColumns}
        columnDisplay='ma_lsx'
        dialogTitle='Danh mục lệnh sản xuất'
        value={params.row.ma_lsx_data?.ma_lsx || ''}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'ma_lsx_data', row)}
      />
    )
  },
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 150,
    renderCell: params => (
      <SearchField<ChiPhi>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.CHI_PHI}/`}
        searchColumns={chiPhiSearchColumns}
        columnDisplay='ma_cp'
        dialogTitle='Danh mục chi phí không hợp lệ'
        value={params.row.ma_cp0_data?.ma_cp || ''}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'ma_cp0_data', row)}
      />
    )
  }
];

export const getInputTableColumns = (): GridColDef[] => [
  {
    field: 'ma_dv',
    headerName: 'Mã dịch vụ',
    width: 120,
    renderCell: params => params.row.ma_dv_data?.ma_dv
  },
  {
    field: 'ten_dv',
    headerName: 'Tên dịch vụ',
    width: 200,
    renderCell: params => params.row.ma_dv_data?.ten_dv
  },
  {
    field: 'dvt',
    headerName: 'Đvt',
    width: 100,
    renderCell: params => params.row.dvt_data?.dvt
  },
  {
    field: 'so_luong',
    headerName: 'Số lượng',
    width: 100
  },
  {
    field: 'gia_vnd',
    headerName: 'Giá VND',
    width: 120
  },
  {
    field: 'thanh_tien_vnd',
    headerName: 'Thành tiền VND',
    width: 150
  },
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 250
  },
  {
    field: 'tl_ck',
    headerName: 'Tl ck(%)',
    width: 100
  },
  {
    field: 'ck_vnd',
    headerName: 'Ch.khấu VND',
    width: 120
  },
  {
    field: 'thue_suat',
    headerName: 'Thuế suất',
    width: 100
  },
  {
    field: 'tk_thue_co',
    headerName: 'Tk thuế có',
    width: 120,
    renderCell: params => params.row.tk_thue_co_data?.code
  },
  {
    field: 'tk_dt',
    headerName: 'Tk doanh thu',
    width: 120,
    renderCell: params => params.row.tk_dt_data?.code
  },
  {
    field: 'tk_ck',
    headerName: 'Tk chiết khấu',
    width: 120,
    renderCell: params => params.row.tk_ck_data?.code
  },
  {
    field: 'thue_vnd',
    headerName: 'Thuế VND',
    width: 120
  },
  {
    field: 'gia_ban',
    headerName: 'Giá bán',
    width: 120
  },
  {
    field: 'thanh_tien',
    headerName: 'Thành tiền',
    width: 120
  },
  {
    field: 'tong_ck',
    headerName: 'Chiết khấu',
    width: 120
  },
  {
    field: 'tong_thue',
    headerName: 'Thuế',
    width: 100
  },
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 120,
    renderCell: params => params.row.ma_bp_data?.ma_bp
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 120,
    renderCell: params => params.row.ma_vv_data?.ma_vu_viec
  },
  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 120,
    renderCell: params => params.row.ma_hd_data?.ma_hd
  },
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 150,
    renderCell: params => params.row.ma_dtt_data?.ma_dtt
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 120,
    renderCell: params => params.row.ma_ku_data?.ma_ku
  },
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 100,
    renderCell: params => params.row.ma_phi_data?.ma_phi
  },
  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 120,
    renderCell: params => params.row.ma_sp_data?.ma_vt
  },
  {
    field: 'lenh_sx',
    headerName: 'Lệnh sản xuất',
    width: 150
  },
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 150,
    renderCell: params => params.row.ma_cp0_data?.ma_cp
  }
];
