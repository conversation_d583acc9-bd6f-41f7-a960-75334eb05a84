import { z } from 'zod';

// Zod schema for validation
export const phiNganHangSchema = z.object({
  uuid: z.string().optional(),
  ma_cpnh: z.string().min(1, '<PERSON><PERSON> phí ngân hàng không được để trống'),
  ten_cpnh: z.string().min(1, 'Tên phí ngân hàng không được để trống'),
  ten_cpnh2: z.string().nullable().optional(),
  tk_cpnh: z.string().min(1, 'Tà<PERSON> khoản phí không được để trống'),
  ma_thue: z.string().optional().nullable(),
  tk_thue: z.string().min(1, 'Tà<PERSON> khoản thuế không được để trống'),
  cuc_thue: z.string().optional(),
  ma_mau_bc: z.string().min(1, 'Mã mẫu báo cáo không được để trống'),
  ma_tc_thue: z.string().min(1, '<PERSON><PERSON> t<PERSON>h chất thuế không được để trống'),
  status: z.enum(['0', '1']).default('1')
});

export const searchSchema = phiNganHangSchema;

// TypeScript interface based on the PhiNganHang model
export interface PhiNganHangModel {
  uuid?: string;
  ma_cpnh: string;
  ten_cpnh: string;
  ten_cpnh2?: string | null;
  tk_cpnh: string;
  ma_thue?: string | null;
  tk_thue: string;
  cuc_thue?: string;
  ma_mau_bc: string;
  ma_tc_thue: string;
  status: '0' | '1';
  created?: string;
  updated?: string;
}

// Legacy interface name - kept for backward compatibility
export type BankFeeFormattedData = PhiNganHangModel;

export type SearchFormValues = z.infer<typeof searchSchema>;

// Thêm initialValues cho form
export const initialValues: SearchFormValues = {
  ma_cpnh: '',
  ten_cpnh: '',
  ten_cpnh2: null,
  tk_cpnh: '',
  ma_thue: '',
  tk_thue: '',
  cuc_thue: '',
  ma_mau_bc: '3',
  ma_tc_thue: '3',
  status: '1'
};
