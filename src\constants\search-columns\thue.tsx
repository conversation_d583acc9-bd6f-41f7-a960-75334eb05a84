import { ExtendedGridColDef } from '@/components/custom/arito';

export const coQuanThueSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_cqt', headerName: 'Mã đối tượng', width: 150 },
  { field: 'ten_cqt', headerName: 'Tên đối tượng', width: 250 },
  { field: 'cong_no_phai_thu', headerName: 'Công nợ p/thu', width: 150, type: 'number' },
  { field: 'cong_no_phai_tra', headerName: 'Công nợ p/trả', width: 150, type: 'number' },
  { field: 'ma_so_thue', headerName: 'Mã số thuế', width: 150 },
  { field: 'email', headerName: 'Email', width: 200 },
  { field: 'so_dien_thoai', headerName: 'Số điện thoại', width: 150 }
];

export const thueSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_thue', headerName: 'Mã thuế', width: 120 },
  { field: 'ten_thue', headerName: 'Tên thuế', width: 200 }
];

export const tinhChatThueSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_tc_thue', headerName: 'Mã tính chất', width: 120 },
  { field: 'ten_tc_thue', headerName: 'Tên tính chất', width: 200 }
];
