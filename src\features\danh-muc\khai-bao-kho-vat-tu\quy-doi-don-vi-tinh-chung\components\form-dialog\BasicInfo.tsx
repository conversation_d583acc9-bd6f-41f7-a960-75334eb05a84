import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { DonViTinh } from '@/types/schemas/don-vi-tinh.type';
import { unitColumns } from '../../cols-definition';
import QUERY_KEYS from '@/constants/query-keys';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface BasicInfoProps {
  formMode: FormMode;
  dvt: DonViTinh | null;
  setDvt: (dvt: DonViTinh | null) => void;
  dvt0: DonViTinh | null;
  setDvt0: (dvt0: DonViTinh | null) => void;
  errors?: {
    dvt?: string;
    dvt0?: string;
    he_so?: string;
  };
}

function BasicInfo({ formMode, dvt, setDvt, dvt0, setDvt0, errors = {} }: BasicInfoProps) {
  return (
    <div className='p-4 md:p-6'>
      <div className='flex flex-col gap-y-4 md:gap-y-6'>
        <div className='space-y-4 md:space-y-6'>
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Đvt</Label>
            <div className='flex flex-col'>
              <SearchField<DonViTinh>
                type='text'
                className='w-[11.25rem]'
                searchEndpoint={`/${QUERY_KEYS.DON_VI_TINH}/`}
                searchColumns={unitColumns}
                dialogTitle='Danh mục đơn vị tính'
                columnDisplay='dvt'
                value={dvt?.dvt || ''}
                displayRelatedField='ten_dvt'
                onRowSelection={row => setDvt(row as DonViTinh)}
                disabled={formMode === 'view'}
              />
              {errors.dvt && <p className='mt-1 text-sm text-red-500'>{errors.dvt}</p>}
            </div>
          </div>

          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Hệ số</Label>
            <div className='flex flex-col'>
              <FormField
                type='text'
                name='he_so'
                disabled={formMode === 'view'}
                defaultValue='0.00'
                className='w-[11.25rem]'
              />
              {errors.he_so && <p className='mt-1 text-sm text-red-500'>{errors.he_so}</p>}
            </div>
          </div>

          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label htmlFor='dvt_quy_doi' className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>
              Đvt quy đổi
            </Label>
            <div className='flex flex-col'>
              <SearchField<DonViTinh>
                type='text'
                className='w-[11.25rem]'
                searchEndpoint={`/${QUERY_KEYS.DON_VI_TINH}/`}
                searchColumns={unitColumns}
                dialogTitle='Danh mục đơn vị tính'
                value={dvt0?.dvt || ''}
                columnDisplay='dvt'
                displayRelatedField='ten_dvt'
                onRowSelection={row => setDvt0(row as DonViTinh)}
                disabled={formMode === 'view'}
              />
              {errors.dvt0 && <p className='mt-1 text-sm text-red-500'>{errors.dvt0}</p>}
            </div>
          </div>

          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label htmlFor='trang_thai' className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>
              Trạng thái
            </Label>
            <FormField
              id='trang_thai'
              type='select'
              name='trang_thai'
              options={[
                { label: '1. Còn sử dụng', value: '1' },
                { label: '0. Không sử dụng', value: '0' }
              ]}
              defaultValue='1'
              disabled={formMode === 'view'}
              className='w-[11.25rem]'
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default BasicInfo;
