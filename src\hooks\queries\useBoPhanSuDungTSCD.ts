import { useState, useEffect } from 'react';
import {
  BoPhanSuDungTSCD,
  DepartmentFormattedData
} from '@/features/danh-muc/tai-san-cong-cu/bo-phan-su-dung-tscd/schema';
import { useAuth } from '@/contexts/auth-context';
import QUERY_KEYS from '@/constants/query-keys';
import api from '@/lib/api';

export type BoPhanSuDungTSCDFormattedData = DepartmentFormattedData;

interface BoPhanSuDungTSCDResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: BoPhanSuDungTSCD[];
}

interface UseBoPhanSuDungTSCDReturn {
  departments: BoPhanSuDungTSCD[];
  isLoading: boolean;
  addDepartment: (newDepartment: BoPhanSuDungTSCDFormattedData) => Promise<BoPhanSuDungTSCD>;
  updateDepartment: (uuid: string, updatedDepartment: BoPhanSuDungTSCDFormattedData) => Promise<BoPhanSuDungTSCD>;
  deleteDepartment: (uuid: string) => Promise<void>;
  refreshDepartments: () => Promise<void>;
}

export const useBoPhanSuDungTSCD = (initialDepartments: BoPhanSuDungTSCD[] = []): UseBoPhanSuDungTSCDReturn => {
  const [departments, setDepartments] = useState<BoPhanSuDungTSCD[]>(initialDepartments);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const { entity } = useAuth();

  const fetchDepartments = async () => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.get<BoPhanSuDungTSCDResponse>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.BO_PHAN_SU_DUNG_TSCD}/`
      );
      const departmentsData = response.data.results || response.data;
      console.log('Asset usage departments data:', departmentsData);
      setDepartments(departmentsData);
    } catch (error) {
      console.error('Error fetching asset usage departments:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const addDepartment = async (newDepartment: BoPhanSuDungTSCDFormattedData): Promise<BoPhanSuDungTSCD> => {
    if (!entity?.slug) throw new Error('Entity slug is required');
    setIsLoading(true);
    try {
      const entityModel = newDepartment.entity_model || entity.uuid;
      const requestData: any = {
        ma_bp: newDepartment.ma_bp,
        ten_bp: newDepartment.ten_bp,
        ten_bp2: newDepartment.ten_bp2 || null,
        ma_bp_phi: newDepartment.ma_bp_phi || null,
        status: newDepartment.status || '1',
        entity_model: entityModel
      };

      const response = await api.post(`/entities/${entity.slug}/erp/${QUERY_KEYS.BO_PHAN_SU_DUNG_TSCD}/`, requestData);

      const addedDepartment: BoPhanSuDungTSCD = response.data;
      setDepartments(prev => [...prev, addedDepartment]);
      return addedDepartment;
    } catch (error) {
      console.error('Error adding asset usage department:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateDepartment = async (
    uuid: string,
    updatedDepartment: BoPhanSuDungTSCDFormattedData
  ): Promise<BoPhanSuDungTSCD> => {
    if (!entity?.slug) throw new Error('Entity slug is required');
    setIsLoading(true);
    try {
      const requestData: any = {
        ma_bp: updatedDepartment.ma_bp,
        ten_bp: updatedDepartment.ten_bp,
        ten_bp2: updatedDepartment.ten_bp2 || null,
        ma_bp_phi: updatedDepartment.ma_bp_phi || null,
        status: updatedDepartment.status || '1'
      };

      const response = await api.put(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.BO_PHAN_SU_DUNG_TSCD}/${uuid}/`,
        requestData
      );

      const updatedDept: BoPhanSuDungTSCD = response.data;
      setDepartments(prev => prev.map(dept => (dept.uuid === uuid ? updatedDept : dept)));
      return updatedDept;
    } catch (error) {
      console.error('Error updating asset usage department:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteDepartment = async (uuid: string) => {
    if (!entity?.slug) throw new Error('Entity slug is required');
    setIsLoading(true);
    try {
      await api.delete(`/entities/${entity.slug}/erp/${QUERY_KEYS.BO_PHAN_SU_DUNG_TSCD}/${uuid}/`);
      setDepartments(prev => prev.filter(dept => dept.uuid !== uuid));
    } catch (error) {
      console.error('Error deleting asset usage department:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchDepartments();
  }, [entity?.slug]);

  return {
    departments,
    isLoading,
    addDepartment,
    updateDepartment,
    deleteDepartment,
    refreshDepartments: fetchDepartments
  };
};
