import { GridColDef } from '@mui/x-data-grid';
import { ExtendedGridColDef } from '@/components/custom/arito';

export const getDataTableColumns = (): GridColDef[] => {
  return [
    {
      field: 'dvt_data',
      headerName: 'Đvt',
      width: 150,
      renderCell: (params: any) => {
        return params.row.dvt_data?.dvt || params.row.dvt || '';
      }
    },
    {
      field: 'he_so',
      headerName: 'Hệ số',
      width: 200,
      renderCell: (params: any) => {
        return params.value;
      }
    },
    {
      field: 'dvt0_data',
      headerName: 'Đvt quy đổi',
      width: 150,
      renderCell: (params: any) => {
        return params.row.dvt0_data?.dvt || params.row.dvt0 || '';
      }
    }
  ];
};

export const unitColumns: ExtendedGridColDef[] = [
  {
    field: 'dvt',
    headerName: 'Đvt',
    width: 150
  },

  {
    field: 'ten_dvt',
    headerName: 'Tên đơn vị tính',
    width: 200
  }
];
