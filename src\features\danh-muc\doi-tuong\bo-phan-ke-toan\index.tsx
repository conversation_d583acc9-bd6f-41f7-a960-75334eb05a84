'use client';

import { LoadingOverlay, AritoDataTables } from '@/components/custom/arito';
import { ActionBar, ConfirmDialog, FormDialog } from './components';
import { useDepartment, useFormState, useRows } from '@/hooks';
import { getDataTableColumns } from './cols-definition';
import { BoPhanInput } from '@/types/schemas';

export default function DanhMucBoPhanKeToan() {
  const { departments, isLoading, addDepartment, updateDepartment, deleteDepartment, refreshDepartments } =
    useDepartment();
  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRows();
  const {
    showForm,
    showDelete,
    formMode,
    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick
  } = useFormState();

  const tables = [
    {
      name: 'T<PERSON>t cả',
      rows: departments,
      columns: getDataTableColumns()
    }
  ];

  const handleSubmit = async (data: BoPhanInput) => {
    console.log('Submitting data:', data);
    try {
      if (formMode === 'add') {
        await addDepartment(data);
      } else if (formMode === 'edit' && selectedObj) {
        await updateDepartment(selectedObj.uuid, data);
      }

      handleCloseForm();
      clearSelection();
      await refreshDepartments();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {showForm && (
        <FormDialog
          open={showForm}
          formMode={formMode}
          initialData={
            selectedObj && (formMode === 'edit' || formMode === 'view' || (formMode === 'add' && isCopyMode))
              ? selectedObj
              : formMode === 'add' && !isCopyMode
                ? undefined
                : undefined
          }
          onClose={handleCloseForm}
          onSubmit={handleSubmit}
          onAdd={() => {
            handleCloseForm();
            clearSelection();
            setTimeout(() => {
              handleAddClick();
            }, 100);
          }}
          onEdit={() => {
            if (selectedObj) {
              handleCloseForm();
              handleEditClick();
            }
          }}
          onDelete={() => {
            if (selectedObj) {
              handleCloseForm();
              handleDeleteClick();
            }
          }}
          onCopy={() => {
            if (selectedObj) {
              handleCloseForm();
              handleCopyClick();
            }
          }}
        />
      )}

      {showDelete && (
        <ConfirmDialog
          open={showDelete}
          onClose={handleCloseDelete}
          selectedObj={selectedObj}
          onDelete={deleteDepartment}
          clearSelection={clearSelection}
        />
      )}

      <div className='w-full'>
        <ActionBar
          selectedObj={selectedObj}
          onAddIconClick={handleAddClick}
          onEditIconClick={() => selectedObj && handleEditClick()}
          onDeleteIconClick={() => selectedObj && handleDeleteClick()}
          onCopyIconClick={() => selectedObj && handleCopyClick()}
          onWatchIconClick={() => selectedObj && handleViewClick()}
          onRefreshClick={refreshDepartments}
        />

        {isLoading && <LoadingOverlay />}

        {!isLoading && (
          <AritoDataTables tables={tables} selectedRowId={selectedRowIndex || undefined} onRowClick={handleRowClick} />
        )}
      </div>
    </div>
  );
}
