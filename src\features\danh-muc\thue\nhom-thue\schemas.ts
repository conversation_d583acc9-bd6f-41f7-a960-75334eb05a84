import { z } from 'zod';

export const FormSchema = z.object({
  ma_nhom: z.string().nonempty('Mã nhóm là bắt buộc'),
  ten_phan_nhom: z.string().nonempty('Tên nhóm là bắt buộc'),
  ten2: z.string().optional(),
  trang_thai: z.string().optional(),
  loai_nhom: z.string().optional()
});

export type FormValues = z.infer<typeof FormSchema>;

export const initialValues = {
  ma_nhom: '',
  ten_phan_nhom: '',
  ten2: '',
  trang_thai: '1',
  loai_nhom: 'TAX'
};
