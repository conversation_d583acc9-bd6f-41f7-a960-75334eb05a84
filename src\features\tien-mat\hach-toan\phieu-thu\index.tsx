'use client';

import Split from 'react-split';
import { useFormState, useSearchState, useDataTables, useSearchFieldStates } from './hooks';
import { SearchDialog, ActionBar, PhieuThuForm, InputTableActionBar } from './components';
import { AritoDataTables, LoadingOverlay, InputTable } from '@/components/custom/arito';
import { PhieuThu } from '@/types/schemas';
import { useRows } from '@/hooks';

export default function PhieuThuPage() {
  const { selectedObj, handleRowClick, clearSelection } = useRows<PhieuThu>();

  const { addPhieuThu, updatePhieuThu, deletePhieuThu, refreshPhieuThus, tables, isLoading } = useDataTables();

  const { searchDialogOpen, setSearchDialogOpen, setSearchFilters } = useSearchState();

  const searchFieldStates = useSearchFieldStates();

  const {
    showForm,
    setShowForm,
    formMode,
    inputDetails,
    setInputDetails,
    handleFormSubmit,
    handleAddClick,
    handleEditClick,
    handleCopyClick,
    handleDeleteClick,
    handleSearchClick,
    handleSearchClose,
    handleSearchSubmit,
    handleRefreshClick,
    handlePrintClick,
    handleFixedColumnsClick,
    handleMultiPrintClick,
    handleExportDataClick,
    handleDownloadExcelTemplateClick,
    handleImportFromExcelClick
  } = useFormState({
    selectedObj,
    clearSelection,
    addPhieuThu,
    updatePhieuThu,
    deletePhieuThu,
    refreshPhieuThus,
    setSearchDialogOpen,
    setSearchFilters
  });

  return (
    <>
      <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
        {showForm && (
          <PhieuThuForm
            formMode={formMode}
            selectedObj={selectedObj}
            inputDetails={inputDetails}
            setInputDetails={setInputDetails}
            searchFieldStates={searchFieldStates}
            onSubmit={handleFormSubmit}
            onClose={() => setShowForm(false)}
          />
        )}

        {!showForm && (
          <>
            <SearchDialog open={searchDialogOpen} onClose={handleSearchClose} onSearch={handleSearchSubmit} />

            <ActionBar
              onAddClick={handleAddClick}
              onEditClick={() => selectedObj && handleEditClick()}
              onDeleteClick={() => selectedObj && handleDeleteClick()}
              onCopyClick={() => selectedObj && handleCopyClick()}
              onPrintClick={handlePrintClick}
              onSearchClick={handleSearchClick}
              onRefreshClick={handleRefreshClick}
              onFixedColumnsClick={handleFixedColumnsClick}
              onMultiPrintClick={handleMultiPrintClick}
              onExportDataClick={handleExportDataClick}
              onDownloadExcelTemplateClick={handleDownloadExcelTemplateClick}
              onImportFromExcelClick={handleImportFromExcelClick}
              isEditDisabled={!selectedObj}
              isDeleteDisabled={!selectedObj}
              isCopyDisabled={!selectedObj}
            />
            <Split
              className='flex flex-1 flex-col'
              direction='vertical'
              sizes={[50, 50]}
              minSize={200}
              gutterSize={8}
              gutterAlign='center'
              snapOffset={30}
              dragInterval={1}
              cursor='row-resize'
            >
              <div className='w-full overflow-hidden'>
                {isLoading && <LoadingOverlay />}
                {!isLoading && (
                  <AritoDataTables
                    tables={tables}
                    onRowClick={handleRowClick}
                    selectedRowId={selectedObj?.uuid || undefined}
                  />
                )}
              </div>

              <div className='w-full overflow-hidden'>
                <InputTable
                  rows={selectedObj?.child_items || []}
                  columns={[]}
                  mode={formMode}
                  getRowId={row => row?.uuid || ''}
                  className='w-full'
                  actionButtons={<InputTableActionBar mode={formMode} />}
                />
              </div>
            </Split>
          </>
        )}
      </div>
    </>
  );
}
