import React from 'react';
import { FormField } from '@/components/custom/arito';
import { Label } from '@/components/ui/label';
import type { FormMode } from '@/types/form';

interface BasicInfoProps {
  formMode: FormMode;
}

const BasicInfo: React.FC<BasicInfoProps> = ({ formMode }) => {
  const isViewMode = formMode === 'view';

  return (
    <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
      <div className='p-4 md:p-6'>
        <div className='flex flex-col gap-y-4 md:gap-y-6'>
          <div className='space-y-4 md:space-y-6'>
            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Mã nguồn đơn</Label>
              <FormField
                type='text'
                name='ma_nguondon'
                className='w-full'
                disabled={isViewMode || formMode === 'edit'}
              />
            </div>

            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tên nguồn đơn</Label>
              <FormField type='text' name='ten_nguondon' className='w-96' disabled={isViewMode} />
            </div>

            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Ghi chú</Label>
              <FormField type='text' name='ghi_chu' className='w-96' disabled={isViewMode} />
            </div>

            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label htmlFor='status' className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>
                Trạng thái
              </Label>
              <FormField
                id='status'
                type='select'
                name='status'
                className='w-full'
                disabled={isViewMode}
                options={[
                  { value: 1, label: '1. Còn sử dụng' },
                  { value: 0, label: '0. Không sử dụng' }
                ]}
                defaultValue={1}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
