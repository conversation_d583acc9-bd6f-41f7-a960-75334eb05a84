'use client';

import Split from 'react-split';
import { InputTable, AritoDataTables, DeleteDialog, LoadingOverlay } from '@/components/custom/arito';
import { getDataTableColumns, getInputTableColumns } from './cols-definition';
import { ActionBar, FormDialog, InputTableAction } from './components';
import { useFormState, useRows, useToast } from '@/hooks';
import { usePhieuChi } from '@/hooks/queries';

export default function PhieuChiPage() {
  const { toast } = useToast();
  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRows();
  const {
    showForm,
    showDelete,
    formMode,
    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick
  } = useFormState();

  const { phieuChis, isLoading, refreshPhieuChis, createPhieuChi } = usePhieuChi();

  const handleSubmit = async (data: any) => {
    try {
      await create<PERSON>hieu<PERSON>hi(data);
      toast({ title: 'Thành công', description: 'Tạo phiếu chi thành công' });
      handleCloseForm();
      refreshPhieuChis();
    } catch (error) {
      toast({ title: 'Lỗi', description: 'Có lỗi xảy ra khi tạo phiếu chi', variant: 'destructive' });
    }
  };

  const tables = [
    {
      name: 'Tất cả',
      rows: phieuChis,
      columns: getDataTableColumns(handleViewClick)
    },
    {
      name: 'Chưa ghi sổ',
      rows: phieuChis.filter(item => item.status === '0' || item.status === '1'),
      columns: getDataTableColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-[#EAD1DC]' />
    },
    {
      name: 'Chờ duyệt',
      rows: phieuChis.filter(item => item.status === '2'),
      columns: getDataTableColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-red-500' />
    },
    {
      name: 'Đã ghi sổ',
      rows: phieuChis.filter(item => item.status === '3'),
      columns: getDataTableColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-[#3D85C6]' />
    },
    {
      name: 'Khác',
      rows: phieuChis.filter(item => !['0', '1', '2', '3'].includes(item.status)),
      columns: getDataTableColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-black' />
    }
  ];

  return (
    <div className='flex h-full min-h-[calc(100vh-120px)] w-full flex-col overflow-hidden'>
      {isLoading && <LoadingOverlay />}

      {showForm && (
        <FormDialog
          formMode={formMode}
          open={showForm}
          onClose={handleCloseForm}
          initialData={
            selectedObj && (formMode === 'edit' || formMode === 'view' || (formMode === 'add' && isCopyMode))
              ? selectedObj
              : formMode === 'add' && !isCopyMode
                ? undefined
                : undefined
          }
          onSubmit={handleSubmit}
          onAdd={handleAddClick}
          onEdit={handleEditClick}
          onDelete={handleDeleteClick}
          onCopy={handleCopyClick}
        />
      )}

      {showDelete && (
        <DeleteDialog
          open={showDelete}
          onClose={handleCloseDelete}
          selectedObj={selectedObj}
          deleteObj={async () => {}}
          clearSelection={clearSelection}
        />
      )}

      {!showForm && (
        <>
          <ActionBar
            onAdd={handleAddClick}
            onEdit={handleEditClick}
            onDelete={handleDeleteClick}
            onCopy={handleCopyClick}
            onRefresh={refreshPhieuChis}
            isEditDisabled={!selectedObj}
          />

          <Split
            className='flex flex-1 flex-col overflow-hidden'
            direction='vertical'
            sizes={[50, 50]}
            minSize={200}
            gutterSize={4}
            gutterAlign='center'
            snapOffset={30}
            dragInterval={1}
            cursor='row-resize'
          >
            <div className='w-full overflow-hidden'>
              <AritoDataTables
                tables={tables}
                onRowClick={handleRowClick}
                selectedRowId={selectedRowIndex || undefined}
              />
            </div>

            <div className='min-h-[300px] overflow-hidden'>
              <InputTable
                rows={[]}
                columns={getInputTableColumns()}
                mode={formMode}
                actionButtons={<InputTableAction formMode={formMode} handleExport={() => {}} handlePin={() => {}} />}
              />
            </div>
          </Split>
        </>
      )}
    </div>
  );
}
