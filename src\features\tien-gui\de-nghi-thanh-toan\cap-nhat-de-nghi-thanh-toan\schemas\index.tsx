import { z } from 'zod';

export const exportPaymentRequestSchema = z.object({
  // API fields matching CapNhatDeNghiThanhToanInput - use exact API field names
  uuid: z.string().optional(),
  ma_ngv: z.string().optional(),
  ns_yn: z.string().optional(),
  ong_ba: z.string().optional(),
  dia_chi: z.string().optional(),
  dien_giai: z.string().optional(),
  i_so_ct: z.coerce.number().optional(),
  ngay_ct: z.string().optional(),
  ngay_lct: z.string().optional(),
  ty_gia: z.coerce.number().optional(),
  status: z.string().optional(),
  so_ct_goc: z.string().optional(),
  dien_giai_ct_goc: z.string().optional(),
  email_tq: z.string().optional(),
  t_tien_nt: z.coerce.number().optional(),
  t_tien: z.coerce.number().optional(),
  t_thue_nt: z.coerce.number().optional(),
  t_thue: z.coerce.number().optional(),
  created_by: z.string().optional(),
  chi_tiet: z.array(z.any()).optional(),

  // Additional form fields that map to API fields
  doc_type: z.string().optional(), // Maps to docType
  has_budget: z.boolean().optional(), // Maps to hasBudget (ns_yn)
  ma_nt: z.string().uuid('Foreign currency must be a valid UUID').optional(), // Maps to foreignCurrency UUID
  data_received: z.boolean().optional(), // Maps to dataReceived

  // Hidden fields for SearchField values
  boPhanCode: z.string().optional(),
  chungTuCode: z.string().optional(),

  // Form mode for handling submission
  formMode: z.string().optional()
});

export const exportPaymentRequestDetailSchema = z.object({
  // Empty object schema for now, add fields as needed
});

// Payment Request Item Schema
const paymentRequestItemSchema = z.object({
  uuid: z.string().optional(),
  id: z.string().optional(),
  invoice_number: z.string(),
  invoice_date: z.string(),
  invoice_amount: z.coerce.number(),
  paid_amount: z.coerce.number(),
  remaining_amount: z.coerce.number(),
  request_amount: z.coerce.number(),
  description: z.string(),
  department: z.string(),
  project: z.string(),
  contract: z.string(),
  payment_phase: z.string().optional(),
  agreement: z.string().optional(),
  product: z.string().optional(),
  production_order: z.string().optional(),
  invalid_document: z.string().optional(),
  created: z.string().optional(),
  updated: z.string().optional()
});

// Export the PaymentRequestItem type
export type PaymentRequestItem = z.infer<typeof paymentRequestItemSchema>;

// Bank Fee Item Schema for payment requests
const bankFeeSchema = z.object({
  uuid: z.string().optional(),
  id: z.string().optional(),
  supplier_code: z.string(),
  supplier_name: z.string(),
  fee_code: z.string(),
  bank_fee_name: z.string(),
  fee_vnd: z.coerce.number(),
  invoice_number: z.string(),
  symbol: z.string(),
  invoice_date: z.string(),
  description: z.string(),
  fee_account: z.string(),
  counter_account: z.string(),
  tax_account: z.string(),
  tax_rate: z.coerce.number(),
  tax_amount_vnd: z.coerce.number(),
  department: z.string(),
  project: z.string(),
  contract: z.string(),
  payment_phase: z.string(),
  agreement: z.string(),
  fee_note: z.string(),
  product: z.string(),
  production_order: z.string(),
  invalid_document: z.string(),
  created: z.string(),
  updated: z.string()
});

// Export the BankFeeItem type
export type BankFeeItem = z.infer<typeof bankFeeSchema>;

// Search dialog schema for payment requests
export const searchSchema = z.object({
  // Basic info fields
  from_date: z.string().optional(),
  to_date: z.string().optional(),
  from_number: z.string().optional(),
  to_number: z.string().optional(),
  request_type: z.string().default('Tất cả'),

  // Detail fields
  supplier_code: z.string().optional(),
  supplier_name: z.string().optional(),
  tai_khoan_no: z.string().optional(),
  tai_khoan_co: z.string().optional(),
  don_vi: z.string().optional(),
  dien_giai: z.string().optional(),
  trang_thai: z.string().default('Tất cả'),
  payment_method: z.string().default('Tất cả'),
  currency: z.string().default('Tất cả'),
  priority: z.string().default('Tất cả'),
  loc_theo_nguoi_sd: z.string().default('Tất cả')
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialSearchValues: SearchFormValues = {
  from_date: '2025-01-01',
  to_date: '2025-01-31',
  from_number: '',
  to_number: '',
  request_type: 'Tất cả',
  supplier_code: '',
  supplier_name: '',
  tai_khoan_no: '',
  tai_khoan_co: '',
  don_vi: '',
  dien_giai: '',
  trang_thai: 'Tất cả',
  payment_method: 'Tất cả',
  currency: 'Tất cả',
  priority: 'Tất cả',
  loc_theo_nguoi_sd: 'Tất cả'
};
