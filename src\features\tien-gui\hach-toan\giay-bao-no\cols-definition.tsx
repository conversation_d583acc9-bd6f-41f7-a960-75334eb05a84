import { GridColDef } from '@mui/x-data-grid';
import { format } from 'date-fns';

export const getDataTableColumns = (handleViewClick: () => void): GridColDef[] => [
  {
    field: 'status',
    headerName: 'Trạng thái',
    width: 120,
    renderCell: params => {
      const status = params.value;
      switch (status) {
        case '0':
          return 'Chưa ghi sổ';
        case '3':
          return 'Chờ duyệt';
        case '5':
          return 'Đã ghi sổ';
        case '6':
          return 'Khác';
        default:
          return '';
      }
    }
  },
  {
    field: 'so_ct',
    headerName: 'Số c/từ',
    width: 120,
    renderCell: params => (
      <button onClick={handleViewClick} className='hover:text-blue-500 hover:underline'>
        {params.row.so_ct_data?.ma_ct}
      </button>
    )
  },
  {
    field: 'ngay_ct',
    headerName: 'Ngày c/từ',
    width: 120,
    renderCell: params => format(params.row.ngay_ct, 'dd/MM/yyyy')
  },
  {
    field: 'ma_kh',
    headerName: 'Mã đối tượng',
    width: 120,
    renderCell: params => params.row.ma_kh_data?.customer_code
  },
  {
    field: 'ten_kh',
    headerName: 'Tên đối tượng',
    width: 200
  },
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 200
  },
  {
    field: 'sten_tknh',
    headerName: 'Ngân hàng',
    width: 150
  },
  {
    field: 'tk',
    headerName: 'Tk có',
    width: 100,
    renderCell: params => params.row.tk_data?.code
  },
  {
    field: 't_tt_nt',
    headerName: 'Tổng thanh toán',
    width: 150
  },
  {
    field: 'ma_nt',
    headerName: 'Ngoại tệ',
    width: 120,
    renderCell: params => params.row.ma_nt_data?.ma_nt
  },
  {
    field: 'ma_ngv',
    headerName: 'Loại chứng từ',
    width: 120
  }
];

export const getInputTableColumns = (): GridColDef[] => [
  {
    field: 'ma_kh',
    headerName: 'Mã đối tượng',
    width: 120,
    renderCell: params => params.row.ma_kh_data?.customer_code
  },
  {
    field: 'ten_kh',
    headerName: 'Tên đối tượng',
    width: 200,
    renderCell: params => params.row.ma_kh_data?.customer_name
  },
  {
    field: 'ma_loai_hd',
    headerName: 'Loại hóa đơn',
    width: 120
  },
  {
    field: 'ma_thue',
    headerName: 'Thuế suất',
    width: 100
  },
  {
    field: 'ma_mau_bc',
    headerName: 'Mẫu báo cáo',
    width: 120
  },
  {
    field: 'ma_tc_thue',
    headerName: 'Mã tính chất',
    width: 120
  }
];

export const historyColumns: GridColDef[] = [
  { field: 'username', headerName: 'Tài khoản', width: 100 },
  { field: 'nickname', headerName: 'Tên tài khoản', width: 200 },
  { field: 'action', headerName: 'Hành động', width: 100 },
  { field: 'datetime0', headerName: 'Thời gian', width: 150 }
];
