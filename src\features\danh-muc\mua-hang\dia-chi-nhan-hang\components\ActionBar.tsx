import { Co<PERSON>, Pencil, Plus, Trash, FileSearch } from 'lucide-react';
import { AritoActionBar, AritoActionButton, AritoIcon, AritoMenuButton } from '@/components/custom/arito';

interface ActionBarProps {
  onAddIconClick: () => void;
  onEditIconClick: () => void;
  onDeleteIconClick: () => void;
  onCopyIconClick: () => void;
  onWatchIconClick: () => void;
}

export const ActionBar: React.FC<ActionBarProps> = ({
  onAddIconClick,
  onEditIconClick,
  onDeleteIconClick,
  onCopyIconClick,
  onWatchIconClick
}) => {
  return (
    <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Danh mục địa chỉ nhận hàng</h1>}>
      <AritoActionButton title='Thêm' icon={Plus} onClick={onAddIconClick} variant='primary' />
      <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditIconClick} />
      <AritoActionButton title='Xóa' icon={Trash} onClick={onDeleteIconClick} />
      <AritoActionButton title='Sao chép' icon={Copy} onClick={onCopyIconClick} />
      <AritoActionButton title='Xem' icon={FileSearch} onClick={onWatchIconClick} />

      <AritoMenuButton
        items={[
          {
            title: 'Refresh',
            icon: <AritoIcon icon={15} />,
            onClick: () => {},
            group: 0
          },
          {
            title: 'Cố định cột',
            icon: <AritoIcon icon={16} />,
            onClick: () => {},
            group: 0
          },
          {
            title: 'Kết xuất dữ liệu',
            icon: <AritoIcon icon={18} />,
            onClick: () => {},
            group: 1
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
