import { Button } from '@mui/material';
import { AritoIcon, AritoDialog } from '@/components/custom/arito';

interface ConfirmDialogProps {
  open: boolean;
  selectedObj?: any | null;
  onClose: () => void;
  onConfirm?: () => void;
  onDelete?: (uuid: string) => Promise<any>;
  clearSelection?: () => void;
  title?: string;
  content?: string;
}

const ConfirmDialog = ({
  open,
  selectedObj,
  onClose,
  onConfirm,
  onDelete,
  clearSelection,
  title = 'Xác nhận',
  content = 'Bạn có chắc chắn muốn...?'
}: ConfirmDialogProps) => {
  const handleConfirm = async () => {
    if (selectedObj) {
      try {
        await onDelete?.(selectedObj?.uuid);
        onClose();
        clearSelection?.();
      } catch (error: any) {
        console.error(error);
      }
    }

    onConfirm?.();
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title={title}
      maxWidth='sm'
      disableBackdropClose={false}
      disableEscapeKeyDown={true}
      showFullscreenToggle={false}
      titleIcon={<AritoIcon icon={260} />}
      actions={
        <>
          <Button
            onClick={handleConfirm}
            type='submit'
            variant='contained'
            className='bg-[rgba(15,118,110,0.9)] normal-case hover:bg-[rgba(15,118,110,1)]'
          >
            <AritoIcon icon={884} className='mr-2' />
            Đồng ý
          </Button>
          <Button onClick={onClose} variant='outlined'>
            <AritoIcon icon={885} className='mr-2' />
            Huỷ
          </Button>
        </>
      }
    >
      <p className='min-w-[40vw] p-4 text-base font-medium'>{content}</p>
    </AritoDialog>
  );
};

export default ConfirmDialog;
