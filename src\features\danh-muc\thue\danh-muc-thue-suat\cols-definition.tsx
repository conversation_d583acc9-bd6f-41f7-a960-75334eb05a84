import { GridColDef } from '@mui/x-data-grid';

export const getDataTableColumns = (): GridColDef[] => [
  { field: 'ma_thue', headerName: 'Mã thuế', width: 120 },
  { field: 'ten_thue', headerName: 'Tên thuế', width: 200 },
  { field: 'ten_thue2', headerName: 'Tên khác', width: 200 },
  {
    field: 'thue_suat',
    headerName: 'Thuế suất',
    width: 120,
    renderCell: (params: any) => {
      return params.row.thue_suat + '%';
    }
  },
  {
    field: 'tk_thue_dau_ra',
    headerName: 'TK thuế đầu ra',
    width: 150,
    renderCell: (params: any) => {
      return params.row.tk_thue_dau_ra_data?.code || '';
    }
  },
  {
    field: 'tk_thue_dau_ra_duoc_gia',
    headerName: 'TK thuế đầu ra được giảm',
    width: 180,
    renderCell: (params: any) => {
      return params.row.tk_thue_dau_ra_duoc_gia_data?.code || '';
    }
  },
  {
    field: 'tk_thue_dau_vao',
    headerName: 'TK thuế đầu vào',
    width: 150,
    renderCell: (params: any) => {
      return params.row.tk_thue_dau_vao_data?.code || '';
    }
  },
  {
    field: 'tk_thue_dau_vao_duoc_gia',
    headerName: 'TK thuế đầu vào được giảm',
    width: 180,
    renderCell: (params: any) => {
      return params.row.tk_thue_dau_vao_duoc_gia_data?.code || '';
    }
  },
  {
    field: 'nhom_thue',
    headerName: 'Nhóm thuế',
    width: 150,
    renderCell: (params: any) => {
      return params.row.nhom_thue_data?.ma_nhom || '';
    }
  }
];

// For search columns in dialogs
export const taxGroupSearchColumns = [
  { field: 'ma_nhom', headerName: 'Mã nhóm', width: 120 },
  { field: 'ten_phan_nhom', headerName: 'Tên nhóm', width: 200 }
];

export const accountSearchColumns = [
  { field: 'code', headerName: 'Mã tài khoản', width: 120 },
  { field: 'account_name', headerName: 'Tên tài khoản', width: 200 }
];
