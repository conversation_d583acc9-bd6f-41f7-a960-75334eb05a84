import { FileSelectField } from '@/components/custom/arito/form/search-fields';
import { FormField } from '@/components/custom/arito';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';
interface Props {
  formMode: FormMode;
}

export default function OtherTab({ formMode }: Props) {
  const isViewMode = formMode === 'view';

  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-1'>
        {/* Mã khách hàng */}
        <div className='flex flex-col sm:flex-row sm:items-center'>
          <Label className='sm:mb-0 sm:w-40 sm:min-w-40'>Mã khách hàng</Label>
          <FormField name='ma_kh' type='text' disabled={true} />
        </div>

        {/* Kèm theo */}
        <div className='flex flex-col sm:flex-row sm:items-center'>
          <Label className='sm:mb-0 sm:w-40 sm:min-w-40'>Kèm theo</Label>
          <FormField name='so_ct_goc' type='number' disabled={isViewMode} />
        </div>

        {/* Chọn file */}
        <div className='flex flex-col sm:flex-row sm:items-center'>
          <FileSelectField
            label='Chọn files'
            formMode={formMode}
            onFileChange={file => console.log(file)}
            labelClassName='w-[7.5rem]'
          />
        </div>

        {/* Chứng từ gốc */}
        <div className='flex flex-col sm:flex-row sm:items-center'>
          <Label className='sm:mb-0 sm:w-40 sm:min-w-40'>Chứng từ gốc</Label>
          <div className='flex-1'>
            <FormField name='dien_giai_ct_goc' type='text' disabled={isViewMode} />
          </div>
        </div>
      </div>
    </div>
  );
}
