import { z } from 'zod';

export const FormSchema = z.object({
  ma_bp: z
    .string()
    .min(1, 'Mã bộ phận là bắt buộc')
    .refine(val => !val.includes(' '), {
      message: 'Mã bộ phận không được chứa dấu cách'
    }),
  ten_bp: z.string().min(1, 'Tên bộ phận là bắt buộc'),
  ten_bp2: z.string().optional(),
  ghi_chu: z.string().optional(),
  status: z.string().min(1, 'Trạng thái là bắt buộc')
});

export type FormValues = z.infer<typeof FormSchema>;

export const initialFormValues: FormValues = {
  ma_bp: '',
  ten_bp: '',
  ten_bp2: '',
  ghi_chu: '',
  status: '1'
};
