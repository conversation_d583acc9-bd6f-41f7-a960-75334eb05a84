'use client';

import React from 'react';
import ConfirmationDialog from '@/components/custom/arito/dialog/confirm-dialog';
import { AritoDataTables, LoadingOverlay } from '@/components/custom/arito';
import { getDataTableColumns } from './cols-definition';
import { FormDialog, ActionBar } from './components';
import { Tax, TaxInput } from '@/types/schemas';
import { useCRUD, useFormState } from '@/hooks';
import { QUERY_KEYS } from '@/constants';

export default function DanhMucThueSuatPage() {
  const { addItem, updateItem, deleteItem, refreshData, isLoading, data } = useCRUD<Tax, TaxInput>({
    endpoint: QUERY_KEYS.THUE
  });

  const {
    showForm,
    showDelete,
    selectedObj,
    selectedRowIndex,
    formMode: mode,
    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick,
    handleRowClick,
    clearSelection
  } = useFormState<Tax>();

  const handleSubmit = async (data: TaxInput) => {
    try {
      if (mode === 'add') {
        await addItem(data);
      } else if (mode === 'edit' && selectedObj) {
        await updateItem(selectedObj.uuid, data);
      }
      handleCloseForm();
      clearSelection();
    } catch (err: any) {
      return;
    }
  };

  const tables = [
    {
      name: '',
      rows: data,
      columns: getDataTableColumns()
    }
  ];
  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {showForm && (
        <FormDialog
          mode={mode}
          onClose={handleCloseForm}
          onAdd={handleAddClick}
          onEdit={handleEditClick}
          onCopy={handleCopyClick}
          onDelete={handleDeleteClick}
          onSubmit={handleSubmit}
          initialData={mode === 'add' && !isCopyMode ? undefined : selectedObj}
        />
      )}

      <div className='w-full'>
        <ActionBar
          onAddClick={handleAddClick}
          onEditClick={handleEditClick}
          onDeleteClick={handleDeleteClick}
          onCopyClick={handleCopyClick}
          onViewClick={handleViewClick}
          onRefreshClick={refreshData}
        />
        {isLoading && <LoadingOverlay />}
        {!isLoading && (
          <AritoDataTables tables={tables} selectedRowId={selectedRowIndex || undefined} onRowClick={handleRowClick} />
        )}
      </div>

      {showDelete && (
        <ConfirmationDialog
          onClose={handleCloseDelete}
          onConfirm={() => {
            deleteItem(selectedObj!.uuid);
            handleCloseDelete();
            clearSelection();
            handleCloseForm();
          }}
        />
      )}
    </div>
  );
}
