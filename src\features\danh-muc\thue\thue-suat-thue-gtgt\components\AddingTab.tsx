import { useFormContext } from 'react-hook-form';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { accountSearchColumns, nhomColumns } from '@/constants/search-columns';
import { FormField } from '@/components/custom/arito/form/form-field';
import { QUERY_KEYS } from '@/constants/query-keys';
import { AccountModel } from '@/types/schemas';
import { Label } from '@/components/ui/label';

export const BasicInformationTab = ({ formMode }: { formMode: 'add' | 'edit' | 'view' }) => {
  const isDisabled = formMode === 'view';
  const { getValues, setValue } = useFormContext();

  return (
    <div className='p-4'>
      <div className='space-y-4'>
        <FormField labelClassName='w-32' label='Mã thuế' name='ma_thue' disabled={isDisabled} />
        <FormField labelClassName='w-32' label='Tên thuế' name='ten_thue' disabled={isDisabled} />
        <FormField labelClassName='w-32' label='<PERSON>hu<PERSON> suất (%)' name='thue_suat' type='number' disabled={isDisabled} />

        <div className='flex items-center'>
          <Label className='w-32'>Nhóm thuế</Label>
          <SearchField<any>
            type='text'
            disabled={isDisabled}
            columnDisplay='ma_nhom'
            searchEndpoint={`/groups/`}
            displayRelatedField='ten_phan_nhom'
            searchColumns={nhomColumns}
            dialogTitle='Danh mục nhóm thuế'
            value={getValues('nhom_thue')}
            onRowSelection={(row: any) => {
              setValue('nhom_thue', row.uuid);
              setValue('nhom_thue_data', row);
            }}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-32'>TK thuế đầu ra</Label>
          <SearchField<AccountModel>
            type='text'
            disabled={isDisabled}
            columnDisplay='code'
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
            displayRelatedField='name'
            searchColumns={accountSearchColumns}
            dialogTitle='Danh mục tài khoản'
            value={getValues('tk_thue_dau_ra')}
            onRowSelection={(row: AccountModel) => {
              setValue('tk_thue_dau_ra', row.uuid);
              setValue('tk_thue_dau_ra_data', row);
            }}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-32'>TK thuế đầu ra được giảm trừ</Label>
          <SearchField<AccountModel>
            type='text'
            disabled={isDisabled}
            columnDisplay='code'
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
            displayRelatedField='name'
            searchColumns={accountSearchColumns}
            dialogTitle='Danh mục tài khoản'
            value={getValues('tk_thue_dau_ra_duoc_gia')}
            onRowSelection={(row: AccountModel) => {
              setValue('tk_thue_dau_ra_duoc_gia', row.uuid);
              setValue('tk_thue_dau_ra_duoc_gia_data', row);
            }}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-32'>TK thuế đầu vào</Label>
          <SearchField<AccountModel>
            type='text'
            disabled={isDisabled}
            columnDisplay='code'
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
            displayRelatedField='name'
            searchColumns={accountSearchColumns}
            dialogTitle='Danh mục tài khoản'
            value={getValues('tk_thue_dau_vao')}
            onRowSelection={(row: AccountModel) => {
              setValue('tk_thue_dau_vao', row.uuid);
              setValue('tk_thue_dau_vao_data', row);
            }}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-32'>TK thuế đầu vào được giảm trừ</Label>
          <SearchField<AccountModel>
            type='text'
            disabled={isDisabled}
            columnDisplay='code'
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
            displayRelatedField='name'
            searchColumns={accountSearchColumns}
            dialogTitle='Danh mục tài khoản'
            value={getValues('tk_thue_dau_vao_duoc_gia')}
            onRowSelection={(row: AccountModel) => {
              setValue('tk_thue_dau_vao_duoc_gia', row.uuid);
              setValue('tk_thue_dau_vao_duoc_gia_data', row);
            }}
          />
        </div>
      </div>
    </div>
  );
};
