import { format } from 'date-fns';
import { ChiTietHoaDonBanHangDichVu } from '@/types/schemas';

/**
 * Transform detail rows for API submission
 * @param detailRows - Array of detail row data from the form
 * @returns Transformed detail rows ready for API submission
 */
export const transformDetailRows = (detailRows: ChiTietHoaDonBanHangDichVu[]) => {
  return detailRows.map((row: any, index: number) => ({
    line: index + 1,
    ma_kh: row.ma_kh_data?.uuid || '',
    id_hd: row.id_hd || '',
    tk_co: row.tk_co_data?.uuid || '',
    ty_gia_hd: row.ty_gia_hd || 1,
    ty_gia2: row.ty_gia2 || 1,
    tien_nt: row.tien_nt || 0,
    tien: row.tien || 0,
    dien_giai: row.dien_giai || '',

    // Additional reference fields
    ma_bp: row.ma_bp_data?.uuid || '',
    ma_vv: row.ma_vv_data?.uuid || '',
    ma_hd: row.ma_hd_data?.uuid || '',
    ma_dtt: row.ma_dtt_data?.uuid || '',
    ma_ku: row.ma_ku_data?.uuid || '',
    ma_phi: row.ma_phi_data?.uuid || '',
    ma_sp: row.ma_sp_data?.uuid || '',
    ma_lsx: row.ma_lsx || '',
    ma_cp0: row.ma_cp0_data?.uuid || '',
    id_tt: row.id_tt || ''
  }));
};

/**
 * Transform all form data for submission
 * @param data - Form data from the form submission
 * @param state - Form field state containing references to selected entities
 * @param detailRows - Array of detail row data
 * @param totalAmount - Total amount calculated
 * @param totalAmountNT - Total amount in foreign currency
 * @param entity - Current entity information
 * @returns Transformed data ready for API submission
 */
export const transformFormData = (
  data: any,
  state: any,
  detailRows: ChiTietHoaDonBanHangDichVu[],
  totalAmount: number = 0,
  entity: any = null
) => {
  // Transform detail rows
  const chi_tiet = transformDetailRows(detailRows);

  // Build the final form data object
  return {
    unit_id: entity?.uuid || '',

    // Basic form fields
    ma_ngv: data.ma_ngv || '',
    dien_giai: data.dien_giai || '',

    // Account and document references
    tk: state.taiKhoan?.uuid || '',
    ma_nk: state.quyenChungTu?.uuid || '',
    so_ct: state.quyenChungTu?.so_ct_mau || '',
    i_so_ct: state.quyenChungTu?.i_so_ct_ht || '',

    // Document dates
    ngay_ct: format(data.ngay_ct, 'yyyy-MM-dd') || '',
    ngay_lct: format(data.ngay_lct, 'yyyy-MM-dd') || '',

    // Currency and exchange rate
    ma_nt: data.ma_nt || '',
    ty_gia: data.exchangeRate || 1,

    // Status and flags
    status: data.status || '0',
    transfer_yn: data.transfer_yn || false,
    hd_yn: false, // Contract flag - default to false

    // Exchange rate settings
    tg_dd: data.tg_dd || false,
    cltg_yn: false, // Exchange rate calculation flag - default to false

    // Customer and reference document info
    ma_kh: data.ma_kh || '',
    so_ct_goc: data.so_ct_goc || '',
    dien_giai_ct_goc: data.dien_giai_ct_goc || '',

    // Financial totals
    t_tien_nt: totalAmount,
    t_tien: totalAmount,

    // Detail arrays
    chi_tiet
  };
};
