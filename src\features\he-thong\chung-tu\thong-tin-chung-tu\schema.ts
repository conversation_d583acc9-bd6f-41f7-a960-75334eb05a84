import { z } from 'zod';

export const FormSchema = z.object({
  // Basic Info fields
  ma_ct: z.string(),
  ten_ct: z.string(),
  ten_ct2: z.string().nullable().optional(),
  ngay_ks: z.string(),
  stt: z.number(),
  i_so_ct: z.number(),

  // General Info fields
  d_page_count: z.number().optional(),
  order_type: z.string().optional(),
  df_status: z.string().optional(),
  user_id0_yn: z.boolean().optional(),
  user_id2_yn: z.boolean().optional(),
  ngay_lct_yn: z.boolean().optional(),
  vc_link: z.string().nullable().optional(),
  ct_save_log: z.string().optional()
});

export type FormValues = z.infer<typeof FormSchema>;
