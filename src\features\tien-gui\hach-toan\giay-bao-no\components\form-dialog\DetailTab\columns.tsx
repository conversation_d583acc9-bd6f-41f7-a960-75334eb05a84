import { GridColDef, type GridCellParams } from '@mui/x-data-grid';
import {
  QUERY_KEYS,
  accountSearchColumns,
  ngoaiTeSearchColumns,
  taiKhoanNganHangSearchColumns,
  customerSearchColumns,
  thueSearchColumns
} from '@/constants';
import { AccountModel, KhachHang, NgoaiTe, TaiKhoanNganHang, Tax } from '@/types/schemas';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { headColumns, tailColumns } from './headDetailCols';
import { SearchField } from '@/components/custom/arito';

export const getDetailTableColumns = (
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void,
  status?: string,
  loaiHD?: string,
  setLoaiHD?: (value: string) => void
): GridColDef[] => {
  const middle1Columns = [
    {
      field: 'du_cn',
      headerName: 'Dư công nợ',
      width: 120,
      renderCell: (params: any) => (
        <CellField
          name='du_cn'
          type='number'
          value={params.row.du_cn || 0.0}
          onValueChange={newValue => onCellValueChange(params.row.uuid, 'du_cn', newValue)}
        />
      )
    },
    {
      field: 'id_hd',
      headerName: 'Hóa đơn',
      width: 120,
      renderCell: (params: any) => (
        <CellField
          name='id_hd'
          type='text'
          value={params.row.id_hd || ''}
          onValueChange={newValue => onCellValueChange(params.row.uuid, 'id_hd', newValue)}
        />
      )
    },
    {
      field: 'so_ct0_hd',
      headerName: 'Số hóa đơn',
      width: 120,
      renderCell: (params: any) => (
        <CellField
          name='so_ct0_hd'
          value={params.row.so_ct0_hd || ''}
          onValueChange={newValue => onCellValueChange(params.row.uuid, 'so_ct0_hd', newValue)}
        />
      )
    },
    {
      field: 'ngay_ct_hd',
      headerName: 'Ngày hóa đơn',
      width: 120,
      renderCell: (params: any) => (
        <CellField
          name='ngay_ct_hd'
          type='date'
          value={params.row.ngay_ct_hd || ''}
          onValueChange={newValue => onCellValueChange(params.row.uuid, 'ngay_ct_hd', newValue)}
        />
      )
    },
    {
      field: 'tk_no',
      headerName: 'Tài khoản nợ',
      width: 120,
      renderCell: (params: any) => (
        <SearchField<AccountModel>
          searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
          searchColumns={accountSearchColumns}
          columnDisplay='code'
          dialogTitle='Danh mục tài khoản'
          value={params.row.tk_no_data?.code || ''}
          onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_no_data', row)}
        />
      )
    },
    {
      field: 'ma_nt_hd',
      headerName: 'Ngoại tệ',
      width: 100,
      renderCell: (params: any) => (
        <SearchField<NgoaiTe>
          searchEndpoint={`/${QUERY_KEYS.NGOAI_TE}/`}
          searchColumns={ngoaiTeSearchColumns}
          columnDisplay='ma_nt'
          dialogTitle='Danh mục ngoại tệ'
          value={params.row.ma_nt_hd_data?.ma_nt || ''}
          onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_nt_hd_data', row)}
        />
      )
    },
    {
      field: 'ty_gia_hd',
      headerName: 'Tỷ giá hđ',
      width: 120,
      renderCell: (params: any) => (
        <CellField
          name='ty_gia_hd'
          type='number'
          value={params.row.ty_gia_hd || 0.0}
          onValueChange={newValue => onCellValueChange(params.row.uuid, 'ty_gia_hd', newValue)}
        />
      )
    },
    {
      field: 'tien_hd_nt',
      headerName: 'Tiền trên hóa đơn',
      width: 150,
      renderCell: (params: any) => (
        <CellField
          name='tien_hd_nt'
          type='number'
          value={params.row.tien_hd_nt || 0.0}
          onValueChange={newValue => onCellValueChange(params.row.uuid, 'tien_hd_nt', newValue)}
        />
      )
    },
    {
      field: 'da_pb_nt',
      headerName: 'Đã phân bổ',
      width: 120,
      renderCell: (params: any) => (
        <CellField
          name='da_pb_nt'
          type='number'
          value={params.row.da_pb_nt || 0.0}
          onValueChange={newValue => onCellValueChange(params.row.uuid, 'da_pb_nt', newValue)}
        />
      )
    },
    {
      field: 'cl_nt',
      headerName: 'Còn lại',
      width: 100,
      renderCell: (params: any) => (
        <CellField
          name='cl_nt'
          type='number'
          value={params.row.cl_nt || 0.0}
          onValueChange={newValue => onCellValueChange(params.row.uuid, 'cl_nt', newValue)}
        />
      )
    },
    {
      field: 'tien_nt',
      headerName: 'Tiền VND',
      width: 120,
      renderCell: (params: any) => (
        <CellField
          name='tien_nt'
          type='number'
          value={params.row.tien_nt || 0.0}
          onValueChange={newValue => onCellValueChange(params.row.uuid, 'tien_nt', newValue)}
        />
      )
    }
  ];
  const middle2Columns = [
    {
      field: 'du_cn',
      headerName: 'Dư công nợ',
      width: 120,
      renderCell: (params: any) => (
        <CellField
          name='du_cn'
          type='number'
          value={params.row.du_cn || 0.0}
          onValueChange={newValue => onCellValueChange(params.row.uuid, 'du_cn', newValue)}
        />
      )
    },
    {
      field: 'id_hd',
      headerName: 'Hóa đơn',
      width: 120,
      renderCell: (params: any) => (
        <CellField
          name='id_hd'
          type='text'
          value={params.row.id_hd || ''}
          onValueChange={newValue => onCellValueChange(params.row.uuid, 'id_hd', newValue)}
        />
      )
    },
    {
      field: 'so_ct0_hd',
      headerName: 'Số hóa đơn',
      width: 120,
      renderCell: (params: any) => (
        <CellField
          name='so_ct0_hd'
          type='text'
          value={params.row.so_ct0_hd || ''}
          onValueChange={newValue => onCellValueChange(params.row.uuid, 'so_ct0_hd', newValue)}
        />
      )
    },
    {
      field: 'ngay_ct_hd',
      headerName: 'Ngày hóa đơn',
      width: 120,
      renderCell: (params: any) => (
        <CellField
          name='ngay_ct_hd'
          type='date'
          value={params.row.ngay_ct_hd || ''}
          onValueChange={newValue => onCellValueChange(params.row.uuid, 'ngay_ct_hd', newValue)}
        />
      )
    },
    {
      field: 'tknh2',
      headerName: 'Ngân hàng',
      width: 120,
      renderCell: (params: any) => (
        <SearchField<TaiKhoanNganHang>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN_NGAN_HANG}/`}
          searchColumns={taiKhoanNganHangSearchColumns}
          columnDisplay='account_code'
          dialogTitle='Danh mục tài khoản ngân hàng'
          value={params.row.tk_ngan_hang?.account_code || ''}
          onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_ngan_hang', row)}
        />
      )
    },
    {
      field: 'tk_no',
      headerName: 'Tài khoản nợ',
      width: 120,
      renderCell: (params: any) => (
        <SearchField<AccountModel>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
          searchColumns={accountSearchColumns}
          columnDisplay='code'
          dialogTitle='Danh mục tài khoản'
          value={params.row.tk_no_data?.code || ''}
          onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_no_data', row)}
        />
      )
    },
    {
      field: 'ma_nt_hd',
      headerName: 'Ngoại tệ',
      width: 100,
      renderCell: (params: any) => (
        <SearchField<NgoaiTe>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.NGOAI_TE}/`}
          searchColumns={ngoaiTeSearchColumns}
          columnDisplay='ma_nt'
          dialogTitle='Danh mục ngoại tệ'
          value={params.row.ma_nt_hd_data?.ma_nt || ''}
          onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_nt_hd_data', row)}
        />
      )
    },
    {
      field: 'ty_gia_hd',
      headerName: 'Tỷ giá hđ',
      width: 120,
      renderCell: (params: any) => (
        <CellField
          name='ty_gia_hd'
          type='number'
          value={params.row.ty_gia_hd || 0.0}
          onValueChange={newValue => onCellValueChange(params.row.uuid, 'ty_gia_hd', newValue)}
        />
      )
    },
    {
      field: 'tien_hd_nt',
      headerName: 'Tiền trên hóa đơn',
      width: 150,
      renderCell: (params: any) => (
        <CellField
          name='tien_hd_nt'
          type='number'
          value={params.row.tien_hd_nt || 0.0}
          onValueChange={newValue => onCellValueChange(params.row.uuid, 'tien_hd_nt', newValue)}
        />
      )
    },
    {
      field: 'da_pb_nt',
      headerName: 'Đã phân bổ',
      width: 120,
      renderCell: (params: any) => (
        <CellField
          name='da_pb_nt'
          type='number'
          value={params.row.da_pb_nt || 0.0}
          onValueChange={newValue => onCellValueChange(params.row.uuid, 'da_pb_nt', newValue)}
        />
      )
    },
    {
      field: 'cl_nt',
      headerName: 'Còn lại',
      width: 100,
      renderCell: (params: any) => (
        <CellField
          name='cl_nt'
          type='number'
          value={params.row.cl_nt || 0.0}
          onValueChange={newValue => onCellValueChange(params.row.uuid, 'cl_nt', newValue)}
        />
      )
    },
    {
      field: 'tien_nt',
      headerName: 'Tiền VND',
      width: 120,
      renderCell: (params: any) => (
        <CellField
          name='tien_nt'
          type='number'
          value={params.row.tien_nt || 0.0}
          onValueChange={newValue => onCellValueChange(params.row.uuid, 'tien_nt', newValue)}
        />
      )
    }
  ];
  const middle3Columns = [
    {
      field: 'du_cn',
      headerName: 'Dư công nợ',
      width: 120,
      renderCell: (params: any) => (
        <CellField
          name='du_cn'
          type='number'
          value={params.row.du_cn || 0.0}
          onValueChange={newValue => onCellValueChange(params.row.uuid, 'du_cn', newValue)}
        />
      )
    },
    {
      field: 'tk_no',
      headerName: 'Tài khoản nợ',
      width: 120,
      renderCell: (params: any) => (
        <SearchField<AccountModel>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
          searchColumns={accountSearchColumns}
          columnDisplay='code'
          dialogTitle='Danh mục tài khoản'
          value={params.row.tk_no_data?.code || ''}
          onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_no_data', row)}
        />
      )
    },
    {
      field: 'tien_nt',
      headerName: 'Tiền VND',
      width: 120,
      renderCell: (params: any) => (
        <CellField
          name='tien_nt'
          type='number'
          value={params.row.tien_nt || 0.0}
          onValueChange={newValue => onCellValueChange(params.row.uuid, 'tien_nt', newValue)}
        />
      )
    },
    {
      field: 'ma_loai_hd',
      headerName: 'Loại hoá đơn',
      width: 120,
      renderCell: (params: any) => (
        <CellField
          name='ma_loai_hd'
          type='select'
          options={[
            { value: '0', label: '0. Không có hoá đơn' },
            { value: '1', label: '1. Hoá đơn GTGT đã tách thuế' },
            { value: '2', label: '2. Hoá đơn GTGT không tách thuế' },
            { value: '3', label: '3. Hoá đơn bán hàng thông thường' }
          ]}
          value={params.row.ma_loai_hd || '0'}
          onValueChange={newValue => {
            setLoaiHD?.(newValue);
            onCellValueChange(params.row.uuid, 'ma_loai_hd', newValue);
          }}
        />
      )
    },
    ...(loaiHD !== '0'
      ? [
          {
            field: 'ma_thue',
            headerName: 'Mã thuế',
            width: 120,
            renderCell: (params: GridCellParams) => (
              <SearchField<Tax>
                type='text'
                searchEndpoint={`/${QUERY_KEYS.THUE}/`}
                searchColumns={thueSearchColumns}
                columnDisplay='ma_thue'
                dialogTitle='Danh mục thuế'
                value={params.row.ma_thue_data?.ma_thue || ''}
                onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_thue_data', row)}
              />
            )
          },
          // ten_thue - Tên thuế
          {
            field: 'ten_thue',
            headerName: 'Tên thuế',
            width: 150,
            renderCell: (params: GridCellParams) => (
              <CellField name='ten_thue' type='text' value={params.row.ma_thue_data?.ten_thue || ''} />
            )
          },
          {
            field: 'thue_suat',
            headerName: 'Thuế suất',
            width: 120,
            renderCell: (params: GridCellParams) => (
              <CellField name='thue_suat' type='number' value={params.row.ma_thue_data?.thue_suat || 0.0} />
            )
          },
          {
            field: 'tk_thue',
            headerName: 'Tk thuế',
            width: 120,
            renderCell: (params: GridCellParams) => (
              <SearchField<AccountModel>
                type='text'
                searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
                searchColumns={accountSearchColumns}
                columnDisplay='code'
                dialogTitle='Danh mục tài khoản'
                value={params.row.tk_thue_data?.code || ''}
                onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_thue_data', row)}
              />
            )
          },
          // ten_tk_thue - Tên tài khoản thuế
          {
            field: 'ten_tk_thue',
            headerName: 'Tên TK thuế',
            width: 150,
            renderCell: (params: GridCellParams) => (
              <CellField name='ten_tk_thue' type='text' value={params.row.tk_thue_data?.name || ''} />
            )
          },
          {
            field: 'so_ct0',
            headerName: 'Số hoá đơn',
            width: 120,
            renderCell: (params: GridCellParams) => (
              <CellField
                name='so_ct0'
                type='text'
                value={params.row.so_ct0 || ''}
                onValueChange={newValue => onCellValueChange(params.row.uuid, 'so_ct0', newValue)}
              />
            )
          },
          {
            field: 'so_ct2',
            headerName: 'Ký hiệu',
            width: 120,
            renderCell: (params: GridCellParams) => (
              <CellField
                name='so_ct2'
                type='text'
                value={params.row.so_ct2 || ''}
                onValueChange={newValue => onCellValueChange(params.row.uuid, 'so_ct2', newValue)}
              />
            )
          },
          {
            field: 'ngay_ct0',
            headerName: 'Ngày hoá đơn',
            width: 120,
            renderCell: (params: GridCellParams) => (
              <CellField
                name='ngay_ct0'
                type='date'
                value={params.row.ngay_ct0 || ''}
                onValueChange={newValue => onCellValueChange(params.row.uuid, 'ngay_ct0', newValue)}
              />
            )
          },
          {
            field: 'ma_mau_ct',
            headerName: 'Mẫu HĐ',
            width: 120,
            renderCell: (params: GridCellParams) => (
              <SearchField<any>
                type='text'
                searchEndpoint={`/${QUERY_KEYS.MAU_HOA_DON}/`}
                searchColumns={accountSearchColumns}
                columnDisplay='ma_mau_ct'
                dialogTitle='Danh mục mẫu chứng từ'
                value={params.row.ma_mau_ct_data?.ma_mau_ct || ''}
                onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_mau_ct_data', row)}
              />
            )
          },
          // ten_mau_ct - Tên mẫu chứng từ
          {
            field: 'ten_mau_ct',
            headerName: 'Tên mẫu HĐ',
            width: 150,
            renderCell: (params: GridCellParams) => (
              <CellField name='ten_mau_ct' type='text' value={params.row.ma_mau_ct_data?.ten_mau_ct || ''} />
            )
          },
          // ma_mau_bc - Mã mẫu báo cáo
          {
            field: 'ma_mau_bc',
            headerName: 'Mẫu báo cáo',
            width: 120,
            renderCell: (params: GridCellParams) => (
              <CellField
                name='ma_mau_bc'
                type='select'
                options={[
                  { value: '3', label: '3. Hóa đơn giá trị gia tăng' },
                  { value: '4', label: '4. Hàng hóa, dịch vụ mua vào không có hóa đơn' },
                  { value: '5', label: '5. Hóa đơn bán hàng thông thường' }
                ]}
                value={params.row.ma_mau_bc || ''}
                onValueChange={newValue => onCellValueChange(params.row.uuid, 'ma_mau_bc', newValue)}
              />
            )
          },
          // ma_tc_thue - Mã tính chất thuế
          {
            field: 'ma_tc_thue',
            headerName: 'Mã tính chất',
            width: 120,
            renderCell: (params: GridCellParams) => (
              <CellField
                name='ma_tc_thue'
                type='select'
                options={[
                  {
                    value: '1',
                    label:
                      '1. Hàng hoá, dịch vụ dùng riêng cho SXKD chịu thuế GTGT và sử dụng cho các hoạt động cung cấp hàng hoá, dịch vụ không kê khai, nộp thuế GTGT đủ điều kiện khấu trừ thuế'
                  },
                  {
                    value: '2',
                    label: '2. Hàng hoá, dịch vụ dùng chung cho SXKD chịu thuế GTGT và không chịu thuế GTGT'
                  },
                  { value: '3', label: '3. Hàng hóa, dịch vụ dùng cho dự án đầu tư đủ điều kiện khấu trừ thuế' },
                  { value: '4', label: '4. Hàng hóa, dịch vụ không đủ điều kiện khấu trừ' },
                  { value: '5', label: '5. Hàng hóa, dịch vụ không phải tổng hợp trên tờ khai 01/GTGT' }
                ]}
                value={params.row.ma_tc_thue || ''}
                onValueChange={newValue => onCellValueChange(params.row.uuid, 'ma_tc_thue', newValue)}
              />
            )
          },
          // ma_kh_invoice - Mã khách hàng cho hóa đơn
          {
            field: 'ma_kh_thue',
            headerName: 'Mã ncc',
            width: 120,
            renderCell: (params: GridCellParams) => (
              <SearchField<KhachHang>
                type='text'
                searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
                searchColumns={customerSearchColumns}
                columnDisplay='customer_code'
                dialogTitle='Danh mục đối tượng'
                value={params.row.ma_kh_thue_data?.customer_code || ''}
                onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_kh_thue_data', row)}
              />
            )
          },
          // ten_kh_thue - Tên khách hàng thuế
          {
            field: 'ten_kh_thue',
            headerName: 'Tên nhà cung cấp',
            width: 150,
            renderCell: (params: GridCellParams) => (
              <CellField
                name='ten_kh_thue'
                type='text'
                value={params.row.ma_kh_thue_data?.customer_name || params.row.ten_kh_thue || ''}
                onValueChange={newValue => onCellValueChange(params.row.uuid, 'ten_kh_thue', newValue)}
              />
            )
          },
          // dia_chi - Địa chỉ
          {
            field: 'dia_chi',
            headerName: 'Địa chỉ',
            width: 200,
            renderCell: (params: GridCellParams) => (
              <CellField
                name='dia_chi'
                type='text'
                value={params.row.dia_chi || params.row.ma_kh_thue_data?.address || ''}
                onValueChange={newValue => onCellValueChange(params.row.uuid, 'dia_chi', newValue)}
              />
            )
          },
          // ma_so_thue - Mã số thuế
          {
            field: 'ma_so_thue',
            headerName: 'Mã số thuế',
            width: 120,
            renderCell: (params: GridCellParams) => (
              <CellField
                name='ma_so_thue'
                type='text'
                value={params.row.ma_so_thue || params.row.ma_kh_thue_data?.tax_code || ''}
                onValueChange={newValue => onCellValueChange(params.row.uuid, 'ma_so_thue', newValue)}
              />
            )
          },
          // ten_vt_thue - Tên vật tư thuế
          {
            field: 'ten_vt_thue',
            headerName: 'Tên hàng hóa - dịch vụ',
            width: 150,
            renderCell: (params: GridCellParams) => (
              <CellField
                name='ten_vt_thue'
                type='text'
                value={params.row.ten_vt_thue || ''}
                onValueChange={newValue => onCellValueChange(params.row.uuid, 'ten_vt_thue', newValue)}
              />
            )
          },
          // thue_nt - Thuế ngoại tệ
          {
            field: 'thue_nt',
            headerName: 'Thuế NT',
            width: 120,
            renderCell: (params: GridCellParams) => (
              <CellField
                name='thue_nt'
                type='number'
                value={params.row.thue_nt || 0.0}
                onValueChange={newValue => onCellValueChange(params.row.uuid, 'thue_nt', newValue)}
              />
            )
          },
          // thue - Thuế VND
          {
            field: 'thue',
            headerName: 'Thuế VND',
            width: 120,
            renderCell: (params: GridCellParams) => (
              <CellField
                name='thue'
                type='number'
                value={params.row.thue || 0.0}
                onValueChange={newValue => onCellValueChange(params.row.uuid, 'thue', newValue)}
              />
            )
          },
          // ma_kh9 - Mã khách hàng 9
          {
            field: 'ma_kh9',
            headerName: 'Cục thuế',
            width: 120,
            renderCell: (params: GridCellParams) => (
              <SearchField<KhachHang>
                type='text'
                searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
                searchColumns={customerSearchColumns}
                columnDisplay='customer_code'
                dialogTitle='Danh mục đối tượng'
                value={params.row.ma_kh9_data?.customer_code || ''}
                onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_kh9_data', row)}
              />
            )
          },
          // ten_kh9 - Tên khách hàng 9
          {
            field: 'ten_kh9',
            headerName: 'Tên cục thuế',
            width: 150,
            renderCell: (params: GridCellParams) => (
              <CellField name='ten_kh9' type='text' value={params.row.ma_kh9_data?.customer_name || ''} />
            )
          }
        ]
      : [])
  ];
  const middle4Columns = [
    {
      field: 'tk_no',
      headerName: 'Tài khoản nợ',
      width: 120,
      renderCell: (params: any) => (
        <SearchField<AccountModel>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
          searchColumns={accountSearchColumns}
          columnDisplay='code'
          dialogTitle='Danh mục tài khoản'
          value={params.row.tk_no_data?.code || ''}
          onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_no_data', row)}
        />
      )
    },
    {
      field: 'tien_nt',
      headerName: 'Tiền VND',
      width: 120,
      renderCell: (params: any) => (
        <CellField
          name='tien_nt'
          type='number'
          value={params.row.tien_nt || 0.0}
          onValueChange={newValue => onCellValueChange(params.row.uuid, 'tien_nt', newValue)}
        />
      )
    }
  ];

  // const middle5Columns = [
  //   {
  //     field: 'tknh2',
  //     headerName: 'Ngân hàng',
  //     width: 120,
  //     renderCell: (params: any) => (
  //       <SearchField<TaiKhoanNganHang>
  //         type='text'
  //         searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN_NGAN_HANG}/`}
  //         searchColumns={taiKhoanNganHangSearchColumns}
  //         columnDisplay='account_code'
  //         dialogTitle='Danh mục tài khoản ngân hàng'
  //         value={params.row.tk_ngan_hang?.account_code || ''}
  //         onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_ngan_hang', row)}
  //       />
  //     )
  //   },
  //   {
  //     field: 'tk_no',
  //     headerName: 'Tài khoản nợ',
  //     width: 120,
  //     renderCell: (params: any) => (
  //       <SearchField<AccountModel>
  //         type='text'
  //         searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
  //         searchColumns={accountSearchColumns}
  //         columnDisplay='code'
  //         dialogTitle='Danh mục tài khoản'
  //         value={params.row.tk_no_data?.code || ''}
  //         onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_no_data', row)}
  //       />
  //     )
  //   },
  //   {
  //     field: 'tien_nt',
  //     headerName: 'Tiền VND',
  //     width: 120,
  //     renderCell: (params: any) => (
  //       <CellField
  //         name='tien_nt'
  //         type='number'
  //         value={params.row.tien_nt || 0.0}
  //         onValueChange={newValue => onCellValueChange(params.row.uuid, 'tien_nt', newValue)}
  //       />
  //     )
  //   },
  //   {
  //     field: 'ma_loai_hd',
  //     headerName: 'Loại hóa đơn',
  //     width: 150,
  //     renderCell: (params: any) => (
  //       <CellField
  //         name='ma_loai_hd'
  //         type='select'
  //         options={[
  //           { value: '0', label: '0. Không có hoá đơn' },
  //           { value: '1', label: '1. Hoá đơn GTGT đã tách thuế' },
  //           { value: '2', label: '2. Hoá đơn GTGT không tách thuế' },
  //           { value: '3', label: '3. Hoá đơn bán hàng thông thường' }
  //         ]}
  //         value={params.row.ma_loai_hd || '0'}
  //         onValueChange={newValue => {
  //           // setLoaiHD(newValue);
  //           onCellValueChange(params.row.uuid, 'ma_loai_hd', newValue);
  //         }}
  //       />
  //     )
  //   },
  //   {
  //     field: 'ma_thue',
  //     headerName: 'Thuế suất',
  //     width: 250,
  //     renderCell: (params: any) => {
  //       return (
  //         <CellField
  //           name='ma_thue'
  //           type='select'
  //           value={params.row.ma_thue}
  //           onValueChange={newValue => onCellValueChange(params.row.uuid, 'ma_thue', newValue)}
  //           options={[]}
  //         />
  //       );
  //     }
  //   }
  // ];
  const [_du_cn, ...hoaDonColumns] = middle3Columns;
  const middle5Columns = [
    {
      field: 'tknh2',
      headerName: 'Ngân hàng',
      width: 120,
      renderCell: (params: any) => (
        <SearchField<TaiKhoanNganHang>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN_NGAN_HANG}/`}
          searchColumns={taiKhoanNganHangSearchColumns}
          columnDisplay='account_code'
          dialogTitle='Danh mục tài khoản ngân hàng'
          value={params.row.tk_ngan_hang?.account_code || ''}
          onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_ngan_hang', row)}
        />
      )
    },
    ...hoaDonColumns
  ];

  if (status === '1') {
    return [...headColumns(onCellValueChange), ...middle1Columns, ...tailColumns(onCellValueChange)];
  } else if (status === '2') {
    return [...headColumns(onCellValueChange), ...middle2Columns, ...tailColumns(onCellValueChange)];
  } else if (status === '3') {
    return [...headColumns(onCellValueChange), ...middle3Columns, ...tailColumns(onCellValueChange)];
  } else if (status === '4') {
    return [...headColumns(onCellValueChange), ...middle4Columns, ...tailColumns(onCellValueChange)];
  } else if (status === '5') {
    return [...headColumns(onCellValueChange), ...middle5Columns, ...tailColumns(onCellValueChange)];
  } else {
    return [...headColumns(onCellValueChange), ...middle1Columns, ...tailColumns(onCellValueChange)];
  }
};
