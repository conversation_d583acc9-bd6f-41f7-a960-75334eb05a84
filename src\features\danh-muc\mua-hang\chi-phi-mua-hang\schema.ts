import { z } from 'zod';
import { ChiPhiMuaHangInput } from '@/types/schemas/chi-phi-mua-hang.type';

export const chiPhiMuaHangSchema = z.object({
  ma_cp: z.string().min(1, 'Mã chi phí không được để trống'),
  ten_cp: z.string().min(1, 'Tên chi phí không được để trống'),
  ten_cp2: z.string().optional().nullable(),
  loai_cp: z.string().optional().nullable(),
  loai_pb: z.string().min(1, 'Ti<PERSON><PERSON> thức phân bổ không được để trống'),
  status: z.string().min(1, 'Trạng thái không được để trống')
});

export type FormValues = z.infer<typeof chiPhiMuaHangSchema>;

export const initialValues: FormValues = {
  ma_cp: '',
  ten_cp: '',
  ten_cp2: '',
  loai_cp: '',
  loai_pb: '1', // Default to '1. Số lượng'
  status: '1'
};
