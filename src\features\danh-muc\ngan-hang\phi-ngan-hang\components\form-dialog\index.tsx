import React, { useState } from 'react';
import { Button } from '@mui/material';
import { BottomBar } from '@/components/custom/arito/form/bottom-bar';
import { AritoDialog, AritoForm } from '@/components/custom/arito';
import { searchSchema, BankFeeFormattedData } from '../../schemas';
import { AritoIcon } from '@/components/custom/arito/icon';
import { initialValues } from '../../schemas';
import ConfirmDialog from './ConfirmDialog';
import BankFeeForm from './BankFeeForm';
import { FormMode } from '@/types/form';

interface FormDialogProps {
  mode: FormMode;
  open: boolean;
  onClose: () => void;

  onAddButtonClick?: () => void;
  onEditButtonClick?: () => void;
  onDeleteButtonClick?: () => void;
  onCopyButtonClick?: () => void;
  onWatchButtonClick?: () => void;

  initialData: BankFeeFormattedData | Record<string, any>;
  onSubmit?: (data: BankFeeFormattedData) => void;
  showDuplicateDialog?: boolean;
  setShowDuplicateDialog: (value: boolean) => void;
}

function BankFeeDialog({
  mode,
  open,
  onClose,
  onAddButtonClick,
  onCopyButtonClick,
  onDeleteButtonClick,
  onEditButtonClick,
  initialData,
  onSubmit,
  showDuplicateDialog,
  setShowDuplicateDialog
}: FormDialogProps) {
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  const handleSubmit = (data: BankFeeFormattedData) => {
    if (onSubmit) {
      onSubmit(data);
    }
  };

  const handleCloseDialog = () => {
    setShowConfirmDialog(false);
    onClose();
  };

  return (
    <>
      <AritoDialog
        open={open}
        onClose={onClose}
        title={mode === 'add' ? 'Thêm mới phí ngân hàng' : mode === 'edit' ? 'Sửa phí ngân hàng' : 'Xem phí ngân hàng'}
        maxWidth='lg'
        disableBackdropClose={true}
        disableEscapeKeyDown={true}
        titleIcon={<AritoIcon icon={12} />}
      >
        <AritoForm
          mode={mode}
          hasAritoActionBar={false}
          schema={searchSchema}
          onSubmit={handleSubmit}
          initialData={mode === 'add' ? initialValues : initialData}
          className='w-full md:min-w-[500px] lg:min-w-[800px]'
          headerFields={<BankFeeForm mode={mode} />}
          classNameBottomBar='relative w-full flex justify-end gap-2'
          bottomBar={
            <>
              {mode === 'view' && (
                <BottomBar
                  mode={mode}
                  onAdd={onAddButtonClick}
                  onEdit={onEditButtonClick}
                  onDelete={onDeleteButtonClick}
                  onCopy={onCopyButtonClick}
                  onSubmit={() => {}}
                  onClose={onClose}
                />
              )}

              {mode !== 'view' && (
                <>
                  <Button
                    className='bg-[rgba(15,118,110,0.9)] text-white hover:bg-[rgba(15,118,110,1)]'
                    type='submit'
                    variant='contained'
                  >
                    <AritoIcon icon={884} marginX='4px' />
                    Đồng ý
                  </Button>

                  <Button onClick={() => setShowConfirmDialog(true)} variant='outlined'>
                    <AritoIcon icon={885} marginX='4px' />
                    Huỷ
                  </Button>
                </>
              )}
            </>
          }
        />
      </AritoDialog>

      {/* Confirm Dialog */}
      <ConfirmDialog
        onClose={handleCloseDialog}
        open={showConfirmDialog}
        onCloseConfirmDialog={() => setShowConfirmDialog(false)}
      />

      {/* Duplicate Code Dialog */}
      {showDuplicateDialog && (
        <div className='relative z-[1400]'>
          <AritoDialog
            open={showDuplicateDialog}
            onClose={() => setShowDuplicateDialog(false)}
            title='Mã phí ngân hàng đã tồn tại'
            titleIcon={<AritoIcon icon={1081} />}
            maxWidth='sm'
            disableBackdropClose={false}
          >
            <div className='p-4 md:p-6'>
              <div className='flex flex-col gap-4'>
                <p className='text-center text-base text-gray-700'>
                  Mã phí ngân hàng này đã tồn tại trong hệ thống.
                  <br />
                  Vui lòng chỉnh sửa mã phí ngân hàng để tiếp tục.
                </p>

                <div className='mt-4 flex justify-center gap-2'>
                  <Button
                    onClick={() => setShowDuplicateDialog(false)}
                    className='bg-[rgba(15,118,110,0.9)] text-white hover:bg-[rgba(15,118,110,1)]'
                    variant='contained'
                  >
                    <AritoIcon icon={884} marginX='4px' />
                    Đồng ý
                  </Button>
                </div>
              </div>
            </div>
          </AritoDialog>
        </div>
      )}
    </>
  );
}

export default BankFeeDialog;
