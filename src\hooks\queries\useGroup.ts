import { useEffect, useState } from 'react';
import { Group, GroupResponse, GroupType, GroupTypeMain } from '@/types/schemas';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/api';

/**
 * Generic interface for group data input
 */
export interface GroupFormattedData {
  ma_nhom: string;
  ten_phan_nhom: string;
  ten2?: string | null;
  trang_thai: string;
  loai_nhom: string;
}

/**
 * Return type for the useGroup hook
 */
export interface UseGroupReturn {
  groups: Group[];
  isLoading: boolean;
  refreshGroups: (groupType?: GroupType | null | any) => Promise<void>;
  fetchGroupsByType: (groupType: GroupType | null | any) => Promise<void>;
  addGroup: (data: GroupFormattedData) => Promise<Group | undefined | any>;
  updateGroup: (uuid: string, data: GroupFormattedData) => Promise<Group | undefined | any>;
  deleteGroup: (uuid: string) => Promise<boolean | undefined | any>;
}

/**
 * A generic hook for managing groups of different types
 * @param defaultGroupType The default group type to fetch (e.g., 'PHI1', 'NCC1', 'VT1', 'KH1')
 * @returns Functions and state for managing groups
 */
export const useGroup = (defaultGroupType: GroupType | any): UseGroupReturn => {
  const [groups, setGroups] = useState<Group[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const { entity } = useAuth();

  /**
   * Add a new group
   * @param data The group data to add
   * @returns The newly created group
   */
  const addGroup = async (data: GroupFormattedData) => {
    if (!entity?.slug) return;
    setIsLoading(true);
    try {
      // Always use the defaultGroupType for this instance of the hook
      const groupData = {
        ...data,
        loai_nhom: defaultGroupType
      };

      const response = await api.post(`entities/${entity?.slug}/erp/groups/`, {
        ma_nhom: groupData.ma_nhom,
        ten_phan_nhom: groupData.ten_phan_nhom,
        ten2: groupData.ten2 || null,
        trang_thai: groupData.trang_thai,
        loai_nhom: groupData.loai_nhom
      });
      const newData: Group = response.data;

      // Add the new item to the local state
      setGroups(prev => [...prev, newData]);

      return newData;
    } catch (error) {
      console.error('Error adding group:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Update an existing group
   * @param uuid The UUID of the group to update
   * @param data The updated group data
   * @returns The updated group
   */
  const updateGroup = async (uuid: string, data: GroupFormattedData) => {
    if (!entity?.slug) return;
    setIsLoading(true);
    try {
      // Always use the defaultGroupType for this instance of the hook
      const groupData = {
        ...data,
        loai_nhom: defaultGroupType
      };

      const response = await api.put(`entities/${entity?.slug}/erp/groups/${uuid}/`, {
        ma_nhom: groupData.ma_nhom,
        ten_phan_nhom: groupData.ten_phan_nhom,
        ten2: groupData.ten2 || null,
        trang_thai: groupData.trang_thai,
        loai_nhom: groupData.loai_nhom
      });
      const updatedData: Group = response.data;

      // Update the item in the local state
      setGroups(prev => prev.map(item => (item.uuid === uuid ? updatedData : item)));

      return updatedData;
    } catch (error) {
      console.error('Error updating group:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Delete a group
   * @param uuid The UUID of the group to delete
   * @returns True if the deletion was successful
   */
  const deleteGroup = async (uuid: string) => {
    if (!entity?.slug) return;
    setIsLoading(true);
    try {
      await api.delete(`entities/${entity?.slug}/erp/groups/${uuid}/`);

      // Remove the item from the local state
      setGroups(prev => prev.filter(item => item.uuid !== uuid));

      return true;
    } catch (error) {
      console.error('Error deleting group:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Fetch groups by type
   * @param groupType The group type to fetch (e.g., 'PHI1', 'NCC1', 'VT1', 'KH1')
   */
  const fetchGroups = async (groupType: GroupType | null = defaultGroupType) => {
    if (!entity?.slug) {
      console.log('No entity slug available');
      return;
    }

    setIsLoading(true);
    try {
      let url = `entities/${entity?.slug}/erp/groups/`;

      // Always use the defaultGroupType for this instance of the hook if no groupType is provided
      const typeToFetch = groupType || defaultGroupType;

      if (typeToFetch) {
        url += `?loai_nhom=${typeToFetch}`;
      }

      const response = await api.get<GroupResponse>(url);
      const mappedData: Group[] = response.data.results;
      setGroups(mappedData);
    } catch (error) {
      console.error('Error fetching groups:', error);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Fetch groups by type (alias for fetchGroups)
   * @param groupType The group type to fetch
   */
  const fetchGroupsByType = async (groupType: GroupType | null) => {
    await fetchGroups(groupType);
  };

  // Initial fetch when the component mounts
  useEffect(() => {
    if (entity?.slug && defaultGroupType) {
      fetchGroups(defaultGroupType);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [entity?.slug, defaultGroupType]);

  return {
    groups,
    isLoading,
    refreshGroups: fetchGroups,
    fetchGroupsByType,
    addGroup,
    updateGroup,
    deleteGroup
  };
};
