import { GridColDef } from '@mui/x-data-grid';
import {
  QUERY_KEYS,
  khachHangSearchColumns,
  boPhanSearchColumns,
  chiPhiSearchColumns,
  dotThanhToanSearchColumns,
  hopDongSearchColumns,
  kheUocSearchColumns,
  lenhSanXuatSearchColumns,
  phiSearchColumns,
  vatTuSearchColumns,
  vuViecSearchColumns,
  accountSearchColumns,
  nganHangSearchColumns
} from '@/constants';
import {
  AccountModel,
  BoPhan,
  ChiPhi,
  KhachHang,
  DotThanhToan,
  HopDong,
  KheUoc,
  Phi,
  VatTu,
  VuViec,
  NganHang
} from '@/types/schemas';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { SearchField } from '@/components/custom/arito';

export const getDetailTableColumns = (
  type: string,
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void
): GridColDef[] => [
  {
    field: 'dien_giai',
    headerName: '<PERSON><PERSON>n giải',
    width: 200,
    renderCell: params => (
      <CellField
        name='dien_giai'
        type='text'
        value={params.row.dien_giai}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'dien_giai', newValue)}
      />
    )
  },
  {
    field: 'ma_kh',
    headerName: 'Mã đối tượng',
    width: 120,
    renderCell: params => (
      <SearchField<KhachHang>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
        searchColumns={khachHangSearchColumns}
        columnDisplay='customer_code'
        dialogTitle='Danh mục đối tượng'
        value={params.row.ma_kh_data?.customer_code}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_kh_data', row)}
      />
    )
  },
  {
    field: 'ten_kh',
    headerName: 'Tên đối tượng',
    width: 200,
    renderCell: params => <CellField name='ten_kh' type='text' value={params.row.ma_kh_data?.customer_name} />
  },
  {
    field: 'du_cn',
    headerName: 'Dư công nợ',
    width: 120,
    renderCell: params => <CellField name='du_cn' type='number' value={params.row.du_cn} disabled={true} />
  },
  ...(type === '2'
    ? [
        {
          field: 'tk_no',
          headerName: 'Tài khoản nợ',
          width: 120,
          renderCell: (params: any) => (
            <SearchField<AccountModel>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
              searchColumns={accountSearchColumns}
              columnDisplay='code'
              dialogTitle='Danh mục tài khoản'
              value={params.row.tk_no_data?.code}
              onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_no_data', row)}
            />
          )
        }
      ]
    : type === '4'
      ? [
          {
            field: 'tknh2',
            headerName: 'Ngân hàng',
            width: 120,
            renderCell: (params: any) => (
              <SearchField<NganHang>
                type='text'
                searchEndpoint={`/${QUERY_KEYS.NGAN_HANG}/`}
                searchColumns={nganHangSearchColumns}
                columnDisplay='ma_ngan_hang'
                dialogTitle='Danh mục ngân hàng'
                value={params.row.tknh2_data?.ma_ngan_hang}
                onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tknh2_data', row)}
              />
            )
          }
        ]
      : []),
  ...(type === '1'
    ? [
        {
          field: 'id_hd',
          headerName: 'Hoá đơn',
          width: 100,
          renderCell: (params: any) => (
            <SearchField<any>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.HOA_DON}/`}
              searchColumns={[]}
              columnDisplay='id'
              dialogTitle='Hoá đơn'
              value={params.row.id_hd_data?.id || ''}
              onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'id_hd_data', row)}
            />
          )
        },
        {
          field: 'so_ct0_hd',
          headerName: 'Số hoá đơn',
          width: 120,
          renderCell: (params: any) => <CellField name='so_ct0_hd' type='text' value={params.row.id_hd_data?.so_ct0} />
        },
        {
          field: 'ngay_ct_hd',
          headerName: 'Ngày hoá đơn',
          width: 120,
          renderCell: (params: any) => (
            <CellField name='ngay_ct_hd' type='date' value={params.row.id_hd_data?.ngay_ct} />
          )
        },
        {
          field: 'tk_no',
          headerName: 'TK nợ',
          width: 120,
          renderCell: (params: any) => <CellField name='tk_no' type='text' value={params.row.tk_no} disabled={true} />
        },
        {
          field: 'ma_ngt_hd',
          headerName: 'Ngoại tệ',
          width: 120,
          renderCell: (params: any) => (
            <CellField name='ma_ngt_hd' type='text' value={params.row.ma_ngt_hd} disabled={true} />
          )
        },
        {
          field: 'ty_gia_hd',
          headerName: 'Tỷ giá hđ',
          width: 120,
          renderCell: (params: any) => (
            <CellField name='ty_gia_hd' type='number' value={params.row.ty_gia_hd} disabled={true} />
          )
        },
        {
          field: 'tien_hd_nt',
          headerName: 'Tiền trên hoá đơn',
          width: 140,
          renderCell: (params: any) => (
            <CellField name='tien_hd_nt' type='number' value={params.row.tien_hd_nt} disabled={true} />
          )
        },
        {
          field: 'da_pb_nt',
          headerName: 'Đã phân bổ',
          width: 120,
          renderCell: (params: any) => (
            <CellField name='da_pb_nt' type='number' value={params.row.da_pb_nt} disabled={true} />
          )
        },
        {
          field: 'cl_nt',
          headerName: 'Còn lại',
          width: 120,
          renderCell: (params: any) => (
            <CellField
              name='cl_nt'
              type='number'
              value={params.row.cl_nt || 0.0}
              onValueChange={newValue => onCellValueChange(params.row.uuid, 'cl_nt', newValue)}
              disabled={true}
            />
          )
        }
      ]
    : []),
  {
    field: 'tien_nt',
    headerName: 'Tiền VND',
    width: 120,
    renderCell: params => (
      <CellField
        name='tien_nt'
        type='number'
        value={params.row.tien_nt || 0.0}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'tien_nt', newValue)}
        disabled={true}
      />
    )
  },
  ...(type === '2'
    ? [
        {
          field: 'ma_loai_hd',
          headerName: 'Loại hoá đơn',
          width: 200,
          renderCell: (params: any) => (
            <CellField
              name='ma_loai_hd'
              type='select'
              value={params.row.ma_loai_hd}
              onValueChange={newValue => onCellValueChange(params.row.uuid, 'ma_loai_hd', newValue)}
              options={[
                { value: '0', label: '0. Không có hoá đơn' },
                { value: '1', label: '1. Hoá đơn GTGT đã tách thuế' },
                { value: '2', label: '2. Hoá đơn GTGT không tách thuế' },
                { value: '3', label: '3. Hoá đơn bán hàng thông thường' }
              ]}
            />
          )
        }
      ]
    : []),
  {
    field: 'so_ct_dn',
    headerName: 'Số đề nghị',
    width: 120,
    renderCell: params => <CellField name='so_ct_dn' type='text' value={params.row.so_ct_dn} disabled={true} />
  },
  {
    field: 'line_dn',
    headerName: 'Dòng ĐN',
    width: 100,
    renderCell: params => (
      <CellField
        name='line_dn'
        type='text'
        value={params.row.line_dn || ''}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'line_dn', newValue)}
        disabled={true}
      />
    )
  },
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 120,
    renderCell: params => (
      <SearchField<BoPhan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
        searchColumns={boPhanSearchColumns}
        columnDisplay='ma_bp'
        dialogTitle='Danh mục bộ phận'
        value={params.row.ma_bp_data?.ma_bp || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_bp_data', row)}
      />
    )
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 120,
    renderCell: params => (
      <SearchField<VuViec>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VU_VIEC}/`}
        searchColumns={vuViecSearchColumns}
        columnDisplay='ma_vu_viec'
        dialogTitle='Danh mục vụ việc'
        value={params.row.ma_vv_data?.ma_vu_viec || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_vv_data', row)}
      />
    )
  },
  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 120,
    renderCell: params => (
      <SearchField<HopDong>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.HOP_DONG}/`}
        searchColumns={hopDongSearchColumns}
        columnDisplay='ma_hd'
        dialogTitle='Danh mục hợp đồng'
        value={params.row.ma_hd_data?.ma_hd || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_hd_data', row)}
      />
    )
  },
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 140,
    renderCell: params => (
      <SearchField<DotThanhToan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}/`}
        searchColumns={dotThanhToanSearchColumns}
        columnDisplay='ma_dtt'
        dialogTitle='Danh mục đợt thanh toán'
        value={params.row.ma_dtt_data?.ma_dtt || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_dtt_data', row)}
      />
    )
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 120,
    renderCell: params => (
      <SearchField<KheUoc>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHE_UOC}/`}
        searchColumns={kheUocSearchColumns}
        columnDisplay='ma_ku'
        dialogTitle='Danh mục khế ước'
        value={params.row.ma_ku_data?.ma_ku || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_ku_data', row)}
      />
    )
  },
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 120,
    renderCell: params => (
      <SearchField<Phi>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.PHI}/`}
        searchColumns={phiSearchColumns}
        columnDisplay='ma_phi'
        dialogTitle='Danh mục phí'
        value={params.row.ma_phi_data?.ma_phi || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_phi_data', row)}
      />
    )
  },
  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 120,
    renderCell: params => (
      <SearchField<VatTu>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
        searchColumns={vatTuSearchColumns}
        columnDisplay='ma_vt'
        dialogTitle='Danh mục sản phẩm'
        value={params.row.ma_sp_data?.ma_vt || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_sp_data', row)}
      />
    )
  },
  {
    field: 'ma_lsx',
    headerName: 'Mã lệnh sản xuất',
    width: 140,
    renderCell: params => (
      <SearchField<any>
        type='text'
        searchEndpoint={'/'}
        searchColumns={lenhSanXuatSearchColumns}
        columnDisplay='ma_lsx'
        dialogTitle='Danh mục lệnh sản xuất'
        value={params.row.ma_lsx_data?.ma_lsx || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_lsx_data', row)}
      />
    )
  },
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 120,
    renderCell: params => (
      <SearchField<ChiPhi>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.CHI_PHI}/`}
        searchColumns={chiPhiSearchColumns}
        columnDisplay='ma_cp'
        dialogTitle='Danh mục chi phí'
        value={params.row.ma_cp0_data?.ma_cp || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_cp0_data', row)}
      />
    )
  }
];
