import { useFormContext } from 'react-hook-form';
import { useEffect } from 'react';
import { QUERY_KEYS, taiSanSearchColumns, lyDoTangGiamTSCDSearchColumns } from '@/constants';
import { FormField, SearchField } from '@/components/custom/arito/form';
import { TaiSanCoDinh, LyDoTangGiamTSCD } from '@/types/schemas';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';
import { useNgoaiTe } from '@/hooks';

interface BasicInfoProps {
  formMode: FormMode;
  searchFieldStates: {
    asset: TaiSanCoDinh | null;
    setAsset: (asset: TaiSanCoDinh | null) => void;
    reason: LyDoTangGiamTSCD | null;
    setReason: (reason: LyDoTangGiamTSCD | null) => void;
    assetType: any | null;
    setAssetType: (assetType: any | null) => void;
  };
}

function BasicInfo({ formMode, searchFieldStates }: BasicInfoProps) {
  const isViewMode = formMode === 'view';
  const { currencies } = useNgoaiTe();
  const { watch, setValue } = useFormContext();

  const nguyenGia = watch('nguyen_gia');
  const gtDaKh = watch('gt_da_kh');

  useEffect(() => {
    const nguyenGiaValue = parseFloat(nguyenGia) || 0;
    const gtDaKhValue = parseFloat(gtDaKh) || 0;
    const gtConLai = nguyenGiaValue - gtDaKhValue;
    const currentGtCl = watch('gt_cl');
    if (currentGtCl !== gtConLai) {
      setValue('gt_cl', gtConLai);
    }
  }, [nguyenGia, gtDaKh, setValue, watch]);

  return (
    <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
      <div className='min-w-[48vw] p-4 md:p-6'>
        <div className='flex flex-col gap-y-4 md:gap-y-6'>
          <div className='space-y-2'>
            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Mã tài sản</Label>
              <SearchField<TaiSanCoDinh>
                displayRelatedField='ten_ts'
                columnDisplay='ma_ts'
                searchEndpoint={`/${QUERY_KEYS.KHAI_BAO_THONG_TIN_TSCD}`}
                searchColumns={taiSanSearchColumns}
                value={searchFieldStates.asset?.ma_ts || ''}
                relatedFieldValue={searchFieldStates.asset?.ten_ts || ''}
                onRowSelection={searchFieldStates.setAsset}
                disabled={isViewMode}
              />
            </div>

            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Lý do</Label>
              <SearchField<LyDoTangGiamTSCD>
                displayRelatedField='ten_tg_ts'
                columnDisplay='ma_tg_ts'
                searchEndpoint={`/${QUERY_KEYS.LY_DO_TANG_GIAM_TSCD}`}
                searchColumns={lyDoTangGiamTSCDSearchColumns}
                value={searchFieldStates.reason?.ma_tg_ts || ''}
                relatedFieldValue={searchFieldStates.reason?.ten_tg_ts || ''}
                onRowSelection={searchFieldStates.setReason}
                disabled={isViewMode}
              />
            </div>

            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Kỳ/Năm</Label>
              <div className='flex items-center gap-2'>
                <FormField type='number' name='ky' defaultValue={4} disabled={isViewMode} />
                <FormField type='number' name='nam' defaultValue={2025} disabled={isViewMode} />
              </div>
            </div>

            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Ngày thay đổi</Label>
              <FormField type='date' name='ngay_ct' disabled={isViewMode} />
            </div>

            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Số chứng từ</Label>
              <FormField type='text' name='so_ct' disabled={isViewMode} />
            </div>

            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tỷ giá</Label>
              <div className='flex items-center gap-2'>
                <FormField
                  type='select'
                  name='ma_nt'
                  options={currencies.map(currency => ({ label: currency.ten_nt, value: currency.uuid }))}
                  disabled={isViewMode}
                />
                <FormField type='number' name='ty_gia' disabled={isViewMode} className='pb-1' />
              </div>
            </div>

            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Nguyên giá</Label>
              <FormField type='number' name='nguyen_gia' disabled={isViewMode} />
            </div>

            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Giá trị đã KH</Label>
              <FormField type='number' name='gt_da_kh' disabled={isViewMode} />
            </div>

            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Giá trị còn lại</Label>
              <FormField type='number' name='gt_cl' disabled={true} />
            </div>

            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Số kỳ kh còn lại</Label>
              <FormField type='number' name='so_ky_kh' disabled={isViewMode} />
            </div>

            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>GT khấu hao đc</Label>
              <FormField type='number' name='gt_kh_ky' disabled={isViewMode} />
            </div>

            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>GT khấu hao sau đc</Label>
              <FormField type='number' name='gt_kh_ky_nt' disabled={true} />
            </div>

            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Diễn giải</Label>
              <FormField type='text' name='dien_giai' className='w-full' disabled={isViewMode} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default BasicInfo;
