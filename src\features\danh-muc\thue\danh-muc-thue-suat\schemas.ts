import { z } from 'zod';

export const formSchema = z.object({
  ma_thue: z.string().min(1, '<PERSON><PERSON> thuế là bắt buộc'),
  ten_thue: z.string().min(1, '<PERSON>ê<PERSON> thuế là bắt buộc'),
  ten_thue2: z.string().optional().nullable(),
  thue_suat: z.coerce.number().min(0, 'Thuế suất không được âm').optional(),
  thue_suat_hddt: z.coerce.number().optional(),
  stt: z.coerce.number().min(0, 'Số thứ tự không được âm').optional(),
  loai_thue: z.coerce.number().optional(),
  status: z.coerce.number().optional()
});

export type FormValues = z.infer<typeof formSchema>;

export const initialValues: FormValues = {
  ma_thue: '',
  ten_thue: '',
  ten_thue2: '',
  thue_suat: 0,
  thue_suat_hddt: 0,
  stt: 0,
  loai_thue: 1,
  status: 1
};
