import { useFormContext } from 'react-hook-form';
import { useEffect, useState } from 'react';
import {
  QUERY_KEYS,
  MA_CHUNG_TU,
  accountSearchColumns,
  hanThanhToanSearchColumns,
  khachHangSearchColumns,
  nhanVienSearchColumns,
  quyenChungTuSearchColumns
} from '@/constants';
import { AccountModel, HanThanhToan, KhachHang, NhanVien, QuyenChungTu } from '@/types/schemas';
import { SearchField, FormField, AritoIcon } from '@/components/custom/arito';
import { FormFieldState, FormFieldActions } from '../../hooks';
import { useNgoaiTe, useQuyenChungTuByChungTu } from '@/hooks';
import { generateSoChungTuHienTai } from '@/lib/stringUtil';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface BasicInfoTabProps {
  formMode: FormMode;
  formState: {
    state: FormFieldState;
    actions: FormFieldActions;
  };
}

const tabsMini = [
  { id: 0, title: 'Chứng từ bán' },
  { id: 1, title: 'Hóa đơn' },
  { id: 2, title: 'Phiếu xuất' }
];

export function BasicInfoTab({ formMode, formState: { state, actions } }: BasicInfoTabProps) {
  const [selectedTabId, setSelectedTabId] = useState<number>(0);
  const { currencies } = useNgoaiTe();
  const { watch } = useFormContext();

  const ngayCt = watch('ngay_ct');

  const { quyenChungTus } = useQuyenChungTuByChungTu({
    ma_ct: MA_CHUNG_TU.BAN_HANG.HOA_DON_BAN_HANG,
    ngay_hl: ngayCt || new Date().toISOString().split('T')[0]
  });

  useEffect(() => {
    if (state.quyenChungTu) {
      actions.setSoChungTu(
        generateSoChungTuHienTai(state.quyenChungTu.i_so_ct_ht, state.quyenChungTu.so_ct_mau, new Date())
      );
    }
  }, [state.quyenChungTu, actions]);

  const pt_tao_yn = state.pt_tao_yn;
  const ck_yn = state.ck_yn;
  const loai_ck = state.loai_ck;

  const currencyOptions = currencies.map(currency => ({
    value: currency.uuid,
    label: currency.ma_nt
  }));

  return (
    <div className='flex flex-col gap-3 p-4'>
      {/* Checkboxes Section */}
      <div className='w-full'>
        <div className='flex items-center gap-6 overflow-x-auto'>
          <div className='flex items-center gap-2'>
            <div className='flex flex-shrink-0 items-center'>
              <Checkbox
                name='hdbh_yn'
                id='hdbh_yn'
                defaultChecked={true || state.hdbh_yn}
                onCheckedChange={actions.setHdbhYn}
                disabled={formMode === 'view'}
              />
              <Label className='ml-2'>Hóa đơn</Label>
            </div>
          </div>

          <div className='flex items-center gap-2'>
            <div className='flex flex-shrink-0 items-center'>
              <Checkbox
                name='px_yn'
                id='px_yn'
                disabled={formMode === 'view'}
                defaultChecked={true || state.px_yn}
                onCheckedChange={actions.setPxYn}
              />
              <Label className='ml-2'>Xuất kho</Label>
            </div>
          </div>

          <div className='flex items-center gap-2'>
            <div className='flex flex-shrink-0 items-center'>
              <Checkbox
                name='pt_tao_yn'
                id='pt_tao_yn'
                disabled={formMode === 'view'}
                onCheckedChange={actions.setPtTaoYn}
                checked={pt_tao_yn}
              />
              {!pt_tao_yn && <Label className='ml-2'>Phiếu thu</Label>}
            </div>
            {pt_tao_yn && (
              <div className='flex items-center gap-2'>
                <FormField
                  name='ma_httt'
                  type='select'
                  disabled={formMode === 'view'}
                  className='w-[150px] flex-shrink-0'
                  options={[
                    { value: 'TMB', label: 'Tiền mặt' },
                    { value: 'CKB', label: 'Chuyển khoản' },
                    { value: 'KB', label: 'Khác' }
                  ]}
                  defaultValue={state.ma_httt}
                  onValueChange={actions.setMaHttt}
                />
              </div>
            )}
          </div>

          <div className='flex items-center gap-2'>
            <div className='flex flex-shrink-0 items-center'>
              <Checkbox
                name='ck_yn'
                id='ck_yn'
                disabled={formMode === 'view'}
                checked={ck_yn}
                onCheckedChange={actions.setCkYn}
              />
              {!ck_yn && <Label className='ml-2'>Chiết khấu</Label>}
            </div>
            {ck_yn && (
              <div className='flex items-center gap-2'>
                <FormField
                  name='loai_ck'
                  type='select'
                  disabled={formMode === 'view'}
                  className='w-[250px] flex-shrink-0'
                  options={[
                    { value: '1', label: 'Chiết khấu tự nhập chi tiết' },
                    { value: '2', label: 'Chiết khấu % tổng đôn' },
                    { value: '3', label: 'Giảm tiền trên tổng hóa đơn' }
                  ]}
                  defaultValue={'2'}
                  onValueChange={actions.setLoaiCk}
                />
                {loai_ck !== '1' && (
                  <FormField
                    name='ck_tl_nt'
                    type='number'
                    disabled={formMode === 'view'}
                    className='w-[150px] flex-shrink-0 pb-1'
                    defaultValue={0}
                  />
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Customer Information Section */}
      <div className='grid grid-cols-12 gap-6'>
        {/* Left Column */}
        <div className='col-span-6 space-y-1'>
          <div className='flex items-center'>
            <Label className='w-[195px]'>Mã khách hàng</Label>
            <SearchField<KhachHang>
              type='text'
              name='ma_kh'
              value={state.khachHang?.customer_code || ''}
              searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
              searchColumns={khachHangSearchColumns}
              onRowSelection={actions.setKhachHang}
              columnDisplay='customer_code'
              dialogTitle='Danh mục khách hàng'
              className='w-[205px]'
              disabled={formMode === 'view'}
            />

            <div className='ml-4 flex items-center'>
              <Label className='w-20'>Mã số thuế</Label>
              <FormField
                name='ma_so_thue'
                type='text'
                disabled={formMode === 'view'}
                placeholder='Nhập và tra cứu'
                value={state.khachHang?.tax_code || ''}
              />
              <button className='ml-2 flex items-center justify-center border border-gray-200 px-3 py-1.5'>
                <AritoIcon icon={15} className='shrink-0' />
              </button>
              <button className='ml-2 flex items-center justify-center border border-gray-200 px-3 py-1.5'>
                <AritoIcon icon={888} />
              </button>
            </div>
          </div>

          <div className='flex items-center'>
            <Label className='w-[195px]'>Tên khách hàng/Ng.mua</Label>
            <div className='flex-1'>
              <FormField
                name='ten_kh_thue'
                type='text'
                disabled={formMode === 'view'}
                value={state.khachHang?.customer_name || ''}
                placeholder='Nhập tên khách hàng/đơn vị'
              />
            </div>
          </div>

          <div className='flex items-center'>
            <Label className='w-[195px]'>Địa chỉ/Email</Label>
            <div className='flex-1'>
              <FormField
                name='dia_chi'
                type='text'
                disabled={formMode === 'view'}
                labelClassName='w-48'
                value={state.khachHang?.address || ''}
                placeholder='Nhập địa chỉ khách hàng'
              />
            </div>
          </div>

          <div className='flex items-center'>
            <Label className='w-[195px]'>Mã nhân viên</Label>
            <SearchField<NhanVien>
              type='text'
              name='ma_nvbh'
              value={state.nhanVien?.ma_nhan_vien || state.khachHang?.sales_rep_data?.ma_nhan_vien || ''}
              searchEndpoint={`/${QUERY_KEYS.NHAN_VIEN}/`}
              columnDisplay='ma_nhan_vien'
              displayRelatedField='ho_ten_nhan_vien'
              searchColumns={nhanVienSearchColumns}
              dialogTitle='Danh sách nhân viên bán hàng'
              className='flex-1'
              onRowSelection={actions.setNhanVien}
              disabled={formMode === 'view'}
            />
          </div>

          <div className='flex items-center'>
            <Label className='w-[195px]'>Tài khoản nợ</Label>
            <SearchField<AccountModel>
              type='text'
              name='tk'
              value={state.taiKhoan?.code || state.khachHang?.account_data?.code || ''}
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
              columnDisplay='code'
              displayRelatedField='name'
              searchColumns={accountSearchColumns}
              dialogTitle='Danh sách tài khoản'
              className='flex-1'
              onRowSelection={actions.setTaiKhoan}
              disabled={formMode === 'view'}
            />
          </div>

          <div className='flex items-center'>
            <div className='flex-1'>
              <FormField
                label='Diễn giải'
                name='dien_giai'
                type='text'
                disabled={formMode === 'view'}
                labelClassName='w-48'
                value={state.khachHang?.description || ''}
              />
            </div>
          </div>
        </div>

        {/* Middle Column */}
        <div className='col-span-3 space-y-1'>
          <div className='ml-4 flex items-center'>
            <FormField
              name='du_cn_thu'
              label='Dư công nợ'
              labelClassName='w-32'
              inputClassName='w-6 text-red-600'
              className='w-[200px]'
              defaultValue={0}
              disabled={true}
            />
          </div>

          <div className='ml-4 flex items-center'>
            <FormField
              name='ong_ba'
              label='Người nhận'
              labelClassName='w-32'
              inputClassName='w-6 text-red-600'
              disabled={formMode === 'view'}
              className='w-[350px]'
              value={state.khachHang?.contact_person || ''}
              placeholder='Nhập tên người nhận'
            />
          </div>

          <div className='ml-4 flex items-center'>
            <FormField
              name='e_mail'
              label='Email'
              labelClassName='w-32'
              inputClassName='w-64'
              className='w-[350px]'
              disabled={formMode === 'view'}
              value={state.khachHang?.email || ''}
              placeholder='Nhập email'
            />
          </div>

          {!pt_tao_yn && (
            <div className='ml-4 flex items-center'>
              <div className='flex items-center'>
                <Label className='w-[132px]'>Hạn thanh toán</Label>
                <SearchField<HanThanhToan>
                  type='text'
                  name='ma_tt'
                  value={state.hanThanhToan?.ma_tt || state.khachHang?.payment_term_data?.ma_tt || ''}
                  searchEndpoint={`/${QUERY_KEYS.HAN_THANH_TOAN}/`}
                  columnDisplay='ma_tt'
                  displayRelatedField='ten_tt'
                  searchColumns={hanThanhToanSearchColumns}
                  dialogTitle='Danh mục hạn thanh toán'
                  className='w-[230px]'
                  onRowSelection={actions.setHanThanhToan}
                  placeholder='Hạn thanh toán'
                  disabled={formMode === 'view'}
                />
              </div>
            </div>
          )}
        </div>

        {/* Right Column */}
        <div className='col-span-3 flex'>
          {/* Tab Content */}
          <div className='flex-1 pr-2'>
            {/* Tab 0 */}
            {selectedTabId === 0 && (
              <div>
                <div className='flex items-center'>
                  <Label className='w-32'>Số chứng từ</Label>
                  <SearchField<QuyenChungTu>
                    type='text'
                    name='so_ct'
                    value={state.soChungTu || ''}
                    onValueChange={e => actions.setSoChungTu(e.target.value)}
                    searchEndpoint={`/${QUERY_KEYS.QUYEN_CHUNG_TU}`}
                    searchColumns={quyenChungTuSearchColumns}
                    columnDisplay='ma_nk'
                    dialogTitle='Danh mục quyển'
                    className='flex-1'
                    rows={quyenChungTus}
                    onRowSelection={actions.setQuyenChungTu}
                    disabled={formMode === 'view'}
                  />
                </div>
                <FormField
                  label='Ngày hạch toán'
                  name='ngay_ct'
                  type='date'
                  disabled={formMode === 'view'}
                  labelClassName='w-32 shrink-0'
                />
                <div className='flex'>
                  <div className='flex items-center'>
                    <Label className='w-32'>Ngoại tệ</Label>
                    <div className='w-[100px]'>
                      <FormField
                        name='ma_nt'
                        type='select'
                        disabled={formMode === 'view'}
                        labelClassName='w-32 shrink-0'
                        inputClassName='w-full'
                        options={currencyOptions}
                      />
                    </div>
                  </div>
                  <div className='flex-1 pl-2'>
                    <FormField
                      name='ty_gia'
                      type='text'
                      disabled={formMode === 'view'}
                      labelClassName='w-32 shrink-0'
                      inputClassName='w-full'
                    />
                  </div>
                </div>
                <FormField
                  name='status'
                  type='select'
                  disabled={formMode === 'view'}
                  label='Trạng thái'
                  labelClassName='w-32 shrink-0'
                  inputClassName='w-full'
                  options={[
                    { value: '0', label: 'Lập chứng từ' },
                    { value: '3', label: 'Chờ duyệt' },
                    { value: '5', label: 'Xuất hóa đơn' },
                    { value: '7', label: 'Bỏ duyệt đơn hàng' }
                  ]}
                  defaultValue={'5'}
                />
                <div className='mt-1 flex'>
                  <div className='mb-4 h-2 w-32 shrink-0' />
                  <FormField
                    label='Dữ liệu nhận được'
                    name='transfer_yn'
                    type='checkbox'
                    disabled={true}
                    labelClassName='w-32'
                    defaultValue={false}
                  />
                </div>
              </div>
            )}

            {/* Tab 1 */}
            {selectedTabId === 1 && (
              <div>
                <div className='flex items-center'>
                  <Label className='w-32'>Số hóa đơn</Label>
                  <FormField
                    name='so_ct_hddt0'
                    labelClassName='w-32 shrink-0'
                    inputClassName='w-[205px]'
                    className='w-[205px]'
                    disabled={formMode === 'view'}
                  />
                </div>
                <FormField
                  label='Ngày hóa đơn'
                  name='ngay_ct_hddt0'
                  type='date'
                  disabled={formMode === 'view'}
                  labelClassName='w-32 shrink-0'
                />
                <FormField
                  label='Ký hiệu'
                  name='so_ct2_hddt0'
                  type='text'
                  disabled={formMode === 'view'}
                  labelClassName='w-32 shrink-0'
                />
                <div className='flex'>
                  <div className='flex items-center'>
                    <Label className='w-[127px]'>Ngoại tệ</Label>
                    <div className='w-[100px]'>
                      <FormField
                        name='ma_nt'
                        type='select'
                        disabled={formMode === 'view'}
                        labelClassName='w-32 shrink-0'
                        inputClassName='w-full'
                        options={currencyOptions}
                      />
                    </div>
                  </div>
                  <div className='flex-1 pl-2'>
                    <FormField
                      name='ty_gia'
                      type='text'
                      disabled={formMode === 'view'}
                      labelClassName='w-32 shrink-0'
                      inputClassName='w-full'
                    />
                  </div>
                </div>
                <FormField
                  name='status'
                  type='select'
                  label='Trạng thái'
                  disabled={formMode === 'view'}
                  labelClassName='w-32 shrink-0'
                  inputClassName='w-full'
                  options={[
                    { value: '0', label: 'Lập chứng từ' },
                    { value: '3', label: 'Chờ duyệt' },
                    { value: '5', label: 'Xuất hóa đơn' },
                    { value: '7', label: 'Bỏ duyệt đơn hàng' }
                  ]}
                  defaultValue={'5'}
                />
                <div className='mt-1 flex'>
                  <div className='h-2 w-32 shrink-0' />
                  <FormField
                    label='Dữ liệu nhận được'
                    name='transfer_yn'
                    type='checkbox'
                    disabled={true}
                    labelClassName='w-32'
                    defaultValue={false}
                  />
                </div>
              </div>
            )}

            {/* Tab 2 */}
            {selectedTabId === 2 && (
              <div>
                <div className='flex items-center'>
                  <Label className='w-32'>Số chứng từ</Label>
                  <SearchField<QuyenChungTu>
                    type='text'
                    name='so_ct'
                    value={state.soChungTu || ''}
                    onValueChange={e => actions.setSoChungTu(e.target.value)}
                    searchEndpoint={`/${QUERY_KEYS.QUYEN_CHUNG_TU}`}
                    searchColumns={quyenChungTuSearchColumns}
                    columnDisplay='ma_nk'
                    dialogTitle='Danh mục quyển'
                    className='flex-1'
                    rows={quyenChungTus}
                    onRowSelection={actions.setQuyenChungTu}
                    disabled={formMode === 'view'}
                  />
                </div>
                <FormField
                  label='Ngày xuất kho'
                  name='ngay_px'
                  type='date'
                  disabled={formMode === 'view'}
                  labelClassName='w-32 shrink-0'
                />
                <div className='flex'>
                  <div className='flex items-center'>
                    <Label className='w-[127px]'>Ngoại tệ</Label>
                    <div className='w-[100px]'>
                      <FormField
                        name='ma_nt'
                        type='select'
                        disabled={formMode === 'view'}
                        labelClassName='w-32 shrink-0'
                        inputClassName='w-full'
                        options={currencyOptions}
                      />
                    </div>
                  </div>
                  <div className='flex-1 pl-2'>
                    <FormField
                      name='ty_gia'
                      type='text'
                      disabled={formMode === 'view'}
                      labelClassName='w-32 shrink-0'
                      inputClassName='w-full'
                    />
                  </div>
                </div>
                <FormField
                  name='status'
                  type='select'
                  label='Trạng thái'
                  disabled={formMode === 'view'}
                  labelClassName='w-32 shrink-0'
                  inputClassName='w-full'
                  options={[
                    { value: '0', label: 'Lập chứng từ' },
                    { value: '3', label: 'Chờ duyệt' },
                    { value: '5', label: 'Xuất hóa đơn' }
                  ]}
                  defaultValue={'5'}
                />
                <div className='mt-1 flex'>
                  <div className='h-2 w-32 shrink-0' />
                  <FormField
                    label='Dữ liệu nhận được'
                    name='transfer_yn'
                    type='checkbox'
                    disabled={true}
                    labelClassName='w-32'
                    defaultValue={false}
                  />
                </div>
              </div>
            )}
          </div>

          {/* Tab Buttons - moved to the right */}
          <div className='flex w-7 flex-col items-center justify-center gap-y-[4.2rem] pr-2'>
            {tabsMini.map(tab => (
              <button
                key={tab.id}
                onClick={e => {
                  e.preventDefault();
                  e.stopPropagation();
                  setSelectedTabId(tab.id);
                }}
                className={`h-[16px] w-[80px] rotate-90 rounded-sm text-center text-[10px] font-bold text-white ${
                  selectedTabId === tab.id ? 'bg-blue-600' : 'bg-gray-500 hover:bg-gray-600'
                }`}
              >
                {tab.title}
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
