import React from 'react';
import { QUERY_KEYS, boPhanSearchColumns, tinhThanhSearchColumns } from '@/constants';
import { SearchField, FormField } from '@/components/custom/arito';
import { <PERSON><PERSON><PERSON>, TinhThanh } from '@/types/schemas';
import { FormMode } from '@/types/form';

interface BasicInfoTabProps {
  formMode: FormMode;
  department: BoPhan | null;
  location: TinhThanh | null;
  setDepartment: (department: BoPhan) => void;
  setLocation: (location: TinhThanh) => void;
}

export default function BasicInfoTab({
  formMode,
  department,
  location,
  setDepartment,
  setLocation
}: BasicInfoTabProps) {
  return (
    <div className='p-4'>
      <div className='flex flex-col'>
        <div className='flex items-center'>
          <div className='flex w-full items-center'>
            <label className='w-40 min-w-40 text-sm'>Mã nhân viên</label>
            <FormField className='w-60' name='ma_nhan_vien' type='text' disabled={formMode === 'view'} />
            <div className='flex'>
              <FormField
                className='w-36'
                name='nhan_vien_ban_hang'
                type='checkbox'
                label='Bán hàng'
                defaultValue={true}
                disabled={formMode === 'view'}
              />
              <FormField
                className='w-36'
                name='nhan_vien_mua_hang'
                type='checkbox'
                label='Mua hàng'
                defaultValue={true}
                disabled={formMode === 'view'}
              />
              <FormField
                className='w-40'
                name='cong_no_tam_ung'
                type='checkbox'
                label='Công nợ/tạm ứng'
                defaultValue={true}
                disabled={formMode === 'view'}
              />
            </div>
          </div>
        </div>

        <div className='flex items-center'>
          <label className='w-40 min-w-40 text-sm'>Họ và Tên</label>
          <FormField className='w-60' name='ho_ten_nhan_vien' type='text' disabled={formMode === 'view'} />
        </div>

        <div className='flex items-center'>
          <label className='w-40 min-w-40 text-sm'>Chức vụ</label>
          <FormField className='w-60' name='chuc_vu' type='text' disabled={formMode === 'view'} />
        </div>

        <div className='flex items-center'>
          <label className='w-40 min-w-40 text-sm'>Bộ phận</label>
          <div className='w-[370px]'>
            <SearchField<BoPhan>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
              columnDisplay='ma_bp'
              displayRelatedField='ten_bp'
              relatedFieldValue={department?.ten_bp || ''}
              searchColumns={boPhanSearchColumns}
              value={department?.ma_bp || ''}
              onRowSelection={row => setDepartment(row as BoPhan)}
              disabled={formMode === 'view'}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <label className='w-40 min-w-40 text-sm'>Giới tính</label>
          <FormField
            className='w-60'
            name='gioi_tinh'
            type='select'
            options={[
              { value: '1', label: 'Nam' },
              { value: '2', label: 'Nữ' },
              { value: '3', label: 'Khác' }
            ]}
            disabled={formMode === 'view'}
          />
        </div>

        <div className='flex items-center'>
          <label className='w-40 min-w-40 text-sm'>Ngày sinh</label>
          <div className='flex flex-1 gap-4'>
            <FormField className='w-60' name='ngay_sinh' type='date' disabled={formMode === 'view'} />
            <div className='flex items-center'>
              <label className='text-sm'>Nơi sinh</label>
              <FormField className='mx-2 w-60' name='noi_sinh' type='text' disabled={formMode === 'view'} />
            </div>
          </div>
        </div>

        <div className='flex items-center'>
          <label className='w-40 min-w-40 text-sm'>Địa chỉ thường trú</label>
          <div className='w-full'>
            <FormField name='dia_chi' type='text' disabled={formMode === 'view'} />
          </div>
        </div>

        <div className='flex items-center'>
          <label className='w-40 min-w-40 text-sm'>Điện thoại</label>
          <FormField className='w-60' name='dien_thoai' type='text' disabled={formMode === 'view'} />
        </div>

        <div className='flex items-center'>
          <label className='w-40 min-w-40 text-sm'>Email</label>
          <FormField className='w-60' name='email' type='text' disabled={formMode === 'view'} />
        </div>

        <div className='flex items-center'>
          <label className='w-40 min-w-40 text-sm'>Số CMND</label>
          <div className='flex flex-1 gap-4'>
            <FormField className='w-60' name='so_cmnd' type='text' disabled={formMode === 'view'} />
            <div className='flex items-center gap-2'>
              <label className='text-sm'>Ngày cấp</label>
              <FormField className='w-60' name='ngay_hieu_luc_cmnd' type='date' disabled={formMode === 'view'} />
            </div>
          </div>
        </div>

        <div className='flex items-center'>
          <label className='w-40 min-w-40 text-sm'>Nơi cấp</label>
          <div className='w-[250px]'>
            <SearchField<TinhThanh>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.TINH_THANH}/`}
              columnDisplay='ma_tinh'
              displayRelatedField='ten_tinh'
              searchColumns={tinhThanhSearchColumns}
              relatedFieldValue={location?.ten_tinh || ''}
              value={location?.ma_tinh || ''}
              onRowSelection={row => setLocation(row as TinhThanh)}
              disabled={formMode === 'view'}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
