import { useFormContext } from 'react-hook-form';
import React from 'react';
import { DetailItemInputTableActionBar } from './DetailItemInputTableActionBar';
import { InputTable } from '@/components/custom/arito/custom-input-table';
import { useChiTieuNganSach } from '@/hooks/queries/useChiTieuNganSach';
import { useDetailItemRows } from './hooks/useDetailItemRows';
import { getDetailItemColumns } from './cols';
import { FormMode } from '@/types/form';

interface DetailItemTabProps {
  value: any[];
  onChange?: (newValue: any[]) => void;
  formMode: FormMode;
}

export const DetailItemTab: React.FC<DetailItemTabProps> = ({ value, onChange, formMode }) => {
  const {
    rows,
    selectedRowUuid,
    handleRowClick,
    handleAddRow,
    handleDeleteRow,
    handleCopyRow,
    handlePasteRow,
    handleMoveRow,
    handleCellValueChange
  } = useDetailItemRows(value, onChange);

  // Get budget criteria data from the hook
  const { chiTieuNganSachs } = useChiTieuNganSach();

  // Get form context to access the has_budget checkbox value
  const { watch } = useFormContext();
  const hasBudget = watch('has_budget') || false;

  return (
    <div className='h-[500px] w-full'>
      <InputTable
        rows={rows}
        onRowClick={handleRowClick}
        selectedRowId={selectedRowUuid || undefined}
        columns={getDetailItemColumns(handleCellValueChange, chiTieuNganSachs, hasBudget)}
        getRowId={row => row?.uuid || row?.id || ''}
        actionButtons={
          <DetailItemInputTableActionBar
            mode={formMode}
            handleAddRow={handleAddRow}
            handleDeleteRow={handleDeleteRow}
            handleCopyRow={handleCopyRow}
            handlePasteRow={handlePasteRow}
            handleMoveRow={handleMoveRow}
          />
        }
      />
    </div>
  );
};
