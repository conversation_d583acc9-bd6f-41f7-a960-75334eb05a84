'use client';

import { AritoDataTables, LoadingOverlay } from '@/components/custom/arito';
import { ActionBar, FormDialog, ConfirmDialog } from './components';
import { useFormState, useRows, useNhaCungCap } from '@/hooks';
import { getTableColumns } from './cols-definition';
import { NhaCungCapInput } from '@/types/schemas';

export default function DanhMucYeuToPage() {
  const { nhaCungCaps, isLoading, addNhaCungCap, updateNhaCungCap, deleteNhaCungCap, refreshNhaCungCaps } =
    useNhaCungCap();
  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRows();
  const {
    showForm,
    showDelete,
    formMode,

    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick
  } = useFormState();

  const handleSubmit = async (data: NhaCungCapInput) => {
    console.log('🚀 Form handleSubmit - Received data:', data);
    console.log('🚀 Form mode:', formMode);
    console.log('🚀 Selected object:', selectedObj);

    try {
      if (formMode === 'add') {
        console.log('➕ Adding new vendor/supplier...');
        await addNhaCungCap(data);
        console.log('✅ Successfully added vendor/supplier');
      } else if (formMode === 'edit' && selectedObj) {
        console.log('✏️ Updating vendor/supplier with UUID:', selectedObj.uuid);
        await updateNhaCungCap(selectedObj.uuid, data);
        console.log('✅ Successfully updated vendor/supplier');
      }

      handleCloseForm();
      clearSelection();
      await refreshNhaCungCaps();
      console.log('🔄 Form closed and data refreshed');
    } catch (error: any) {
      console.error('❌ Error submitting form:', error);
      console.error('❌ Error details:', {
        message: error?.message,
        response: error?.response?.data,
        status: error?.response?.status
      });
    }
  };

  const tables = [
    {
      name: '',
      rows: nhaCungCaps,
      columns: getTableColumns(handleViewClick)
    }
  ];

  return (
    <div className='flex h-screen flex-col lg:overflow-hidden'>
      {showForm && (
        <FormDialog
          open={showForm}
          onSubmit={handleSubmit}
          formMode={formMode}
          initialData={
            selectedObj && (formMode === 'edit' || formMode === 'view' || (formMode === 'add' && isCopyMode))
              ? selectedObj
              : formMode === 'add' && !isCopyMode
                ? undefined
                : undefined
          }
          onClose={handleCloseForm}
          onAdd={() => {
            handleCloseForm();
            clearSelection();
            setTimeout(() => {
              handleAddClick();
            }, 100);
          }}
          onEdit={() => {
            if (selectedObj) {
              handleCloseForm();
              handleEditClick();
            }
          }}
          onDelete={() => {
            if (selectedObj) {
              handleCloseForm();
              handleDeleteClick();
            }
          }}
          onCopy={() => {
            if (selectedObj) {
              handleCloseForm();
              handleCopyClick();
            }
          }}
        />
      )}

      {showDelete && (
        <ConfirmDialog
          open={showDelete}
          onClose={handleCloseDelete}
          selectedObj={selectedObj}
          onDelete={deleteNhaCungCap}
          clearSelection={clearSelection}
          title='Xoá dữ liệu'
          content='Bạn có chắc chắn xoá không?'
        />
      )}

      {!showForm && (
        <>
          <ActionBar
            onAddClick={handleAddClick}
            onEditClick={() => selectedObj && handleEditClick()}
            onCopyClick={() => selectedObj && handleCopyClick()}
            onDeleteClick={() => selectedObj && handleDeleteClick()}
            onViewClick={() => selectedObj && handleViewClick()}
            onRefreshClick={refreshNhaCungCaps}
            onFixedColumnsClick={() => {}}
            onExportClick={() => {}}
            isViewDisabled={!selectedObj}
          />
          {isLoading && <LoadingOverlay />}
          {!isLoading && (
            <div className='w-full overflow-hidden'>
              <AritoDataTables
                tables={tables}
                selectedRowId={selectedRowIndex || undefined}
                onRowClick={handleRowClick}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
}
