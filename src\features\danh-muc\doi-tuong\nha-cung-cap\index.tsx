'use client';

import { AritoDataTables, LoadingOverlay } from '@/components/custom/arito';
import { ActionBar, FormDialog, ConfirmDialog } from './components';
import { useFormState, useRows, useNhaCungCap } from '@/hooks';
import { getTableColumns } from './cols-definition';
import { NhaCungCapInput } from '@/types/schemas';

export default function DanhMucYeuToPage() {
  const { nhaCungCaps, isLoading, addNhaCungCap, updateNhaCungCap, deleteNhaCungCap, refreshNhaCungCaps } =
    useNhaCungCap();
  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRows();
  const {
    showForm,
    showDelete,
    formMode,

    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick
  } = useFormState();

  const handleSubmit = async (data: NhaCungCapInput) => {
    console.log('🚀 Index handleSubmit called:', {
      formMode,
      selectedObj: selectedObj?.uuid,
      dataReceived: !!data,
      timestamp: new Date().toISOString()
    });

    try {
      if (formMode === 'add') {
        console.log('➕ Adding new vendor...');
        await addNhaCungCap(data);
        console.log('✅ Add successful');
      } else if (formMode === 'edit' && selectedObj) {
        console.log('✏️ Updating vendor:', selectedObj.uuid);
        await updateNhaCungCap(selectedObj.uuid, data);
        console.log('✅ Update successful');
      } else {
        console.warn('⚠️ Invalid form mode or missing selectedObj:', { formMode, selectedObj });
      }

      console.log('🔄 Closing form and refreshing...');
      handleCloseForm();
      clearSelection();
      await refreshNhaCungCaps();
      console.log('✅ Form handling complete');
    } catch (error: any) {
      console.error('❌ Form submission error:', {
        error: error.message,
        status: error.response?.status,
        data: error.response?.data,
        timestamp: new Date().toISOString()
      });
    }
  };

  const tables = [
    {
      name: '',
      rows: nhaCungCaps,
      columns: getTableColumns(handleViewClick)
    }
  ];

  return (
    <div className='flex h-screen flex-col lg:overflow-hidden'>
      {showForm && (
        <FormDialog
          open={showForm}
          onSubmit={handleSubmit}
          formMode={formMode}
          initialData={
            selectedObj && (formMode === 'edit' || formMode === 'view' || (formMode === 'add' && isCopyMode))
              ? selectedObj
              : formMode === 'add' && !isCopyMode
                ? undefined
                : undefined
          }
          onClose={handleCloseForm}
          onAdd={() => {
            handleCloseForm();
            clearSelection();
            setTimeout(() => {
              handleAddClick();
            }, 100);
          }}
          onEdit={() => {
            if (selectedObj) {
              handleCloseForm();
              handleEditClick();
            }
          }}
          onDelete={() => {
            if (selectedObj) {
              handleCloseForm();
              handleDeleteClick();
            }
          }}
          onCopy={() => {
            if (selectedObj) {
              handleCloseForm();
              handleCopyClick();
            }
          }}
        />
      )}

      {showDelete && (
        <ConfirmDialog
          open={showDelete}
          onClose={handleCloseDelete}
          selectedObj={selectedObj}
          onDelete={deleteNhaCungCap}
          clearSelection={clearSelection}
          title='Xoá dữ liệu'
          content='Bạn có chắc chắn xoá không?'
        />
      )}

      {!showForm && (
        <>
          <ActionBar
            onAddClick={handleAddClick}
            onEditClick={() => selectedObj && handleEditClick()}
            onCopyClick={() => selectedObj && handleCopyClick()}
            onDeleteClick={() => selectedObj && handleDeleteClick()}
            onViewClick={() => selectedObj && handleViewClick()}
            onRefreshClick={refreshNhaCungCaps}
            onFixedColumnsClick={() => {}}
            onExportClick={() => {}}
            isViewDisabled={!selectedObj}
          />
          {isLoading && <LoadingOverlay />}
          {!isLoading && (
            <div className='w-full overflow-hidden'>
              <AritoDataTables
                tables={tables}
                selectedRowId={selectedRowIndex || undefined}
                onRowClick={handleRowClick}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
}
