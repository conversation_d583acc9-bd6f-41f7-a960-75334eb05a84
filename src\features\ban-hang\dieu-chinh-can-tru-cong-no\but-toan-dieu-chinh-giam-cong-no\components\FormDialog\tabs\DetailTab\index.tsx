import { GridEventListener } from '@mui/x-data-grid';
import { getDetailTableColumns } from '../../../../cols-definition';
import { ChiTietHoaDonBanHangDichVu } from '@/types/schemas';
import InputTableActionBar from './InputTableActionBar';
import { InputTable } from '@/components/custom/arito';
import { FormMode } from '@/types/form';

interface DetailTabProps {
  rows: ChiTietHoaDonBanHangDichVu[];
  selectedRowUuid: string | null;
  handleRowClick: GridEventListener<'rowClick'>;
  handleAddRow: () => void;
  handleDeleteRow: () => void;
  handleCopyRow: () => void;
  handlePasteRow: () => void;
  handleMoveRow: (direction: 'up' | 'down') => void;
  handleCellValueChange: (rowUuid: string, field: string, newValue: any) => void;
  formMode: FormMode;
}

export default function DetailTab({
  rows,
  selectedRowUuid,
  handleRowClick,
  handleAddRow,
  handleDeleteRow,
  handleCopyRow,
  handlePasteRow,
  handleMoveRow,
  handleCellValueChange,
  formMode
}: DetailTabProps) {
  return (
    <div className='h-screen overflow-hidden'>
      <InputTable<ChiTietHoaDonBanHangDichVu>
        rows={rows}
        onRowClick={handleRowClick}
        selectedRowId={selectedRowUuid || undefined}
        columns={getDetailTableColumns(handleCellValueChange)}
        getRowId={row => row?.uuid || ''}
        mode={formMode}
        actionButtons={
          <InputTableActionBar
            mode={formMode}
            handleAddRow={handleAddRow}
            handleDeleteRow={handleDeleteRow}
            handleCopyRow={handleCopyRow}
            handlePasteRow={handlePasteRow}
            handleMoveRow={handleMoveRow}
            onExport={() => console.log('Export clicked')}
            onPin={() => console.log('Pin clicked')}
          />
        }
        className='h-full'
      />
    </div>
  );
}
