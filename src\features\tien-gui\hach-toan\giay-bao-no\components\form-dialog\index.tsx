'use client';

import { useState, useEffect, useMemo } from 'react';
import { getFormTitle, getFormActionButtons, calculateTotals, transformFormData } from '../../utils';
import { AritoHeaderTabs, AritoForm, LoadingOverlay } from '@/components/custom/arito';
import { useInputTableRows, useLoading, useSearchFieldStates } from '../../hooks';
import { PhiNganHangTab } from './PhiNganHangTab';
import { ConfirmDialog } from '../../components';
import { initialFormValues } from '../../schema';
import { BasicInfoTab } from './BasicInfoTab';
import { ThanhToanTab } from './ThanhToanTab';
import UyNhiemChiTab from './UyNhiemChiTab';
import { HistoryTab } from './HistoryTab';
import { FormMode } from '@/types/form';
import { BottomBar } from './BottomBar';
import { DetailTab } from './DetailTab';
import { TyGiaTab } from './TyGiaTab';
import { OtherTab } from './OtherTab';
import { ThueTab } from './ThueTab';

interface FormDialogProps {
  formMode: FormMode;
  open: boolean;
  initialData?: any;
  onSubmit?: (data: any) => void;
  onClose: () => void;
  onAdd?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onCopy?: () => void;
}

const FormDialog = ({
  open,
  formMode,
  initialData,
  onClose,
  onSubmit,
  onAdd,
  onEdit,
  onDelete,
  onCopy
}: FormDialogProps) => {
  const [isConfirm, setIsConfirm] = useState<boolean>(false);
  const [isFormDirty, setIsFormDirty] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>('info');
  const { loading } = useLoading({
    isOpen: open,
    external: false,
    duration: 500
  });
  const {
    rows: detailRows,
    selectedRowUuid: detailSelectedRowUuid,
    handleRowClick: detailHandleRowClick,
    handleAddRow: detailHandleAddRow,
    handleDeleteRow: detailHandleDeleteRow,
    handleCopyRow: detailHandleCopyRow,
    handlePasteRow: detailHandlePasteRow,
    handleMoveRow: detailHandleMoveRow,
    handleCellValueChange: detailHandleCellValueChange
  } = useInputTableRows(initialData?.chi_tiet || []);
  const {
    rows: thueRows,
    selectedRowUuid: thueSelectedRowUuid,
    handleRowClick: thueHandleRowClick,
    handleAddRow: thueHandleAddRow,
    handleDeleteRow: thueHandleDeleteRow,
    handleCopyRow: thueHandleCopyRow,
    handlePasteRow: thueHandlePasteRow,
    handleMoveRow: thueHandleMoveRow,
    handleCellValueChange: thueHandleCellValueChange
  } = useInputTableRows(initialData?.thue || []);
  const {
    rows: phiNganHangRows,
    selectedRowUuid: phiNganHangSelectedRowUuid,
    handleRowClick: phiNganHangHandleRowClick,
    handleAddRow: phiNganHangHandleAddRow,
    handleDeleteRow: phiNganHangHandleDeleteRow,
    handleCopyRow: phiNganHangHandleCopyRow,
    handlePasteRow: phiNganHangHandlePasteRow,
    handleMoveRow: phiNganHangHandleMoveRow,
    handleCellValueChange: phiNganHangHandleCellValueChange
  } = useInputTableRows(initialData?.phiNganHang || []);

  const { state, actions } = useSearchFieldStates(initialData);

  useEffect(() => {
    if (!open) {
      setIsFormDirty(false);
      setActiveTab('info');
    }
  }, [open]);

  const detail = useMemo(() => {
    return initialData?.chi_tiet || [];
  }, [initialData]);

  const { totalAmount, totalQuantity, totalBankFees, totalTax, totalPayment } = useMemo(() => {
    return calculateTotals(detail);
  }, [detail]);

  const handleSubmit = (data: any) => {
    const formData = transformFormData(data, state, detailRows, thueRows, phiNganHangRows);

    console.log('formData: ', formData);

    onSubmit?.(formData);
    setIsFormDirty(false);
    onClose();
  };

  const handleClose = () => {
    if (isFormDirty && formMode !== 'view') {
      setIsConfirm(true);
    } else {
      onClose();
    }
  };

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  const title = getFormTitle(formMode, activeTab);
  const actionButtons = getFormActionButtons(formMode, activeTab, {
    onAdd,
    onEdit,
    onDelete,
    onCopy,
    handleClose
  });

  return (
    <>
      <div className='relative overflow-auto'>
        {loading && (
          <div className='absolute inset-0 z-50 flex items-center justify-center bg-white bg-opacity-80'>
            <LoadingOverlay />
          </div>
        )}
        <AritoForm
          mode={formMode}
          initialData={initialData || initialFormValues}
          title={title}
          actionButtons={actionButtons}
          subTitle='Hóa đơn bán hàng'
          onSubmit={handleSubmit}
          onClose={handleClose}
          headerFields={
            <div className='max-h-[calc(100vh-150px)] overflow-y-auto' onChange={() => setIsFormDirty(true)}>
              <AritoHeaderTabs
                tabs={[
                  {
                    id: 'info',
                    label: 'Thông tin',
                    component: <BasicInfoTab formMode={formMode} formState={{ state, actions }} />
                  },
                  ...(formMode === 'view'
                    ? [
                        {
                          id: 'history',
                          label: 'Lịch sử',
                          component: <HistoryTab />
                        }
                      ]
                    : [])
                ]}
                onTabChange={handleTabChange}
                defaultTabIndex={activeTab === 'info' ? 0 : activeTab === 'history' ? 1 : 0}
              />
            </div>
          }
          tabs={
            activeTab === 'info' && [
              {
                id: 'details',
                label: 'Chi tiết',
                component: (
                  <DetailTab
                    formMode={formMode}
                    rows={detailRows}
                    selectedRowUuid={detailSelectedRowUuid}
                    onRowClick={detailHandleRowClick}
                    onAddRow={detailHandleAddRow}
                    onDeleteRow={detailHandleDeleteRow}
                    onCopyRow={detailHandleCopyRow}
                    onPasteRow={detailHandlePasteRow}
                    onMoveRow={detailHandleMoveRow}
                    onCellValueChange={detailHandleCellValueChange}
                    status={state.loaiChungTu}
                    loaiHD={state.loai_hd}
                    setNhomLoaiHD={actions.setNhomLoaiHD}
                  />
                )
              },
              {
                id: 'thue',
                label: 'Thuế',
                component: (
                  <ThueTab
                    formMode={formMode}
                    rows={thueRows}
                    selectedRowUuid={thueSelectedRowUuid}
                    onRowClick={thueHandleRowClick}
                    onAddRow={thueHandleAddRow}
                    onDeleteRow={thueHandleDeleteRow}
                    onCopyRow={thueHandleCopyRow}
                    onPasteRow={thueHandlePasteRow}
                    onMoveRow={thueHandleMoveRow}
                    onCellValueChange={thueHandleCellValueChange}
                  />
                )
              },
              {
                id: 'thong_tin_thanh_toan',
                label: 'Thông tin thanh toán',
                component: <ThanhToanTab formMode={formMode} hanTT={state.hanTT} setHanTT={actions.setHanTT} />
              },
              {
                id: 'ty_gia',
                label: 'Tỷ giá',
                component: <TyGiaTab formMode={formMode} />
              },
              {
                id: 'phi_nganHang',
                label: 'Phí ngân hàng',
                component: (
                  <PhiNganHangTab
                    formMode={formMode}
                    rows={phiNganHangRows}
                    selectedRowUuid={phiNganHangSelectedRowUuid}
                    onRowClick={phiNganHangHandleRowClick}
                    onAddRow={phiNganHangHandleAddRow}
                    onDeleteRow={phiNganHangHandleDeleteRow}
                    onCopyRow={phiNganHangHandleCopyRow}
                    onPasteRow={phiNganHangHandlePasteRow}
                    onMoveRow={phiNganHangHandleMoveRow}
                    onCellValueChange={phiNganHangHandleCellValueChange}
                  />
                )
              },
              {
                id: 'uy_nhiem_chi',
                label: 'Ủy nhiệm chi',
                component: <UyNhiemChiTab formMode={formMode} state={state} actions={actions} />
              },
              {
                id: 'khac',
                label: 'Khác',
                component: <OtherTab formMode={formMode} />
              }
            ]
          }
          classNameBottomBar='relative w-full flex justify-end gap-2'
          bottomBar={
            activeTab === 'info' && (
              <BottomBar
                totalQuantity={totalQuantity}
                totalPayment={totalPayment}
                totalAmount={totalAmount}
                totalBankFees={totalBankFees}
                totalTax={totalTax}
              />
            )
          }
        />
      </div>

      {isConfirm && (
        <ConfirmDialog
          open={isConfirm}
          onClose={() => setIsConfirm(false)}
          onConfirm={() => {
            setIsConfirm(false);
            setIsFormDirty(false);
            onClose();
          }}
          title='Cảnh báo'
          content='Bạn muốn kết thúc?'
        />
      )}
    </>
  );
};

export default FormDialog;
