import { FormField, SearchField } from '@/components/custom/arito';
import { khoHangSearchColumns, QUERY_KEYS } from '@/constants';
import type { KhoHang } from '@/types/schemas';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface BasicFormProps {
  mode: FormMode;
  kho: KhoHang | null;
  setKho: (kho: KhoHang) => void;
}

function BasicForm({ mode, kho, setKho }: BasicFormProps) {
  return (
    <div className='space-y-3 p-6'>
      <div className='flex flex-col sm:flex-row sm:items-center'>
        <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Mã địa chỉ</Label>
        <FormField type='text' name='ma_dcnh' className='w-80' disabled={mode === 'view' || mode === 'edit'} />
      </div>

      <div className='flex flex-col sm:flex-row sm:items-center'>
        <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tên địa chỉ</Label>
        <FormField type='text' name='ten_dcnh' className='w-80' disabled={mode === 'view'} />
      </div>

      <div className='flex flex-col sm:flex-row sm:items-center'>
        <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tên khác</Label>
        <FormField type='text' name='ten_dcnh2' className='w-80' disabled={mode === 'view'} />
      </div>

      <div className='flex flex-col sm:flex-row sm:items-center'>
        <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Mã kho</Label>
        <SearchField<KhoHang>
          type='text'
          dialogTitle='Danh mục kho'
          searchEndpoint={`/${QUERY_KEYS.KHO_HANG}`}
          searchColumns={khoHangSearchColumns}
          value={kho?.ma_kho || ''}
          onRowSelection={setKho}
          displayRelatedField='ten_kho'
          relatedFieldValue={kho?.ten_kho || ''}
          columnDisplay='ma_kho'
          disabled={mode === 'view'}
        />
      </div>

      <div className='flex flex-col sm:flex-row sm:items-center'>
        <Label htmlFor='status' className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>
          Trạng thái
        </Label>
        <FormField
          id='status'
          type='select'
          name='status'
          className='w-80'
          options={[
            { label: '1. Còn sử dụng', value: '1' },
            { label: '0. Không sử dụng', value: '0' }
          ]}
          disabled={mode === 'view'}
        />
      </div>
    </div>
  );
}

export default BasicForm;
