import { IconButton, Tooltip } from '@mui/material';
import { buttonConfigs, buttonSx } from '@/components/custom/arito/custom-input-table/components/action-button-set';
import {} from '@/components/custom/arito/custom-input-table/types';
import { AritoIcon, ButtonType } from '@/components/custom/arito';
import { FormMode } from '@/types/form';

interface ButtonConfig {
  type: ButtonType;
  onClick: () => void;
  disabled?: boolean;
  title?: string;
  icon?: number;
}

interface ActionBarProps {
  formMode: FormMode;
  handleAddRow?: () => void;
  handleDeleteRow?: () => void;
  handleCopyRow?: () => void;
  handlePasteRow?: () => void;
  handleMoveRow?: (direction: 'up' | 'down') => void;
  handleExport?: () => void;
  handlePin?: () => void;
  handleViewInventory?: () => void;
  handleViewReceipt?: () => void;
}

const renderActionButton = (button: ButtonConfig): React.ReactNode => {
  let icon: number;
  let title: string;

  if (button.icon !== undefined && button.title) {
    icon = button.icon;
    title = button.title;
  } else {
    const config = buttonConfigs[button.type] || { icon: 0, title: '' };
    icon = config.icon;
    title = config.title;
  }

  const isDisabled = button.disabled || false;

  return (
    <Tooltip title={title} arrow>
      <span>
        <IconButton size='small' onClick={button.onClick} disabled={isDisabled} sx={buttonSx}>
          <AritoIcon icon={icon} />
        </IconButton>
      </span>
    </Tooltip>
  );
};

export default function ActionBar({
  formMode,
  handleAddRow,
  handleDeleteRow,
  handleCopyRow,
  handlePasteRow,
  handleMoveRow,
  handleExport,
  handlePin,
  handleViewInventory,
  handleViewReceipt
}: ActionBarProps) {
  const buttonActions = {
    add: handleAddRow,
    delete: handleDeleteRow,
    copy: handleCopyRow,
    paste: handlePasteRow,
    moveUp: handleMoveRow ? () => handleMoveRow('up') : undefined,
    moveDown: handleMoveRow ? () => handleMoveRow('down') : undefined,
    export: handleExport,
    pin: handlePin,
    viewInventory: handleViewInventory,
    viewReceipt: handleViewReceipt
  } as Record<ButtonType, () => void | undefined>;

  const buttonTypes: ButtonType[] = (
    [
      'add',
      'delete',
      'copy',
      'paste',
      'moveUp',
      'moveDown',
      'export',
      'pin',
      'viewInventory',
      'viewReceipt'
    ] as ButtonType[]
  ).filter(type => buttonActions[type] !== undefined);

  const buttons: ButtonConfig[] = buttonTypes.map(type => ({
    type,
    onClick: buttonActions[type],
    disabled: formMode === 'view'
  }));

  return (
    <div className='flex h-9 flex-none items-center justify-between border-b border-gray-200 bg-white px-1 py-0'>
      <div className='flex h-full flex-1 items-center justify-between'>
        <div className='flex items-center'>
          {buttons.map((button, index) => (
            <span key={button.type + index}>{renderActionButton(button)}</span>
          ))}
        </div>
      </div>
    </div>
  );
}
