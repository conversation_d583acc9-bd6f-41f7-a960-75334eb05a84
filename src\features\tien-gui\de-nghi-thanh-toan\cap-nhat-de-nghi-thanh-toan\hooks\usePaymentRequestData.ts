import { useState, useCallback, useEffect } from 'react';
import { CapNhatDeNghiThanhToan, CapNhatDeNghiThanhToanInput } from '@/types/schemas';
import { useCapNhatDeNghiThanhToan } from '@/hooks/queries';

/**
 * Hook for managing payment request data with real API integration
 * @returns Data and handlers
 */
export const usePaymentRequestData = (handleCloseForm?: () => void) => {
  const [rows, setRows] = useState<CapNhatDeNghiThanhToan[]>([]);
  const [selectedDetails, setSelectedDetails] = useState<any[]>([]);
  const [error, setError] = useState<Error | null>(null);
  const [isDataMapped, setIsDataMapped] = useState<boolean>(false);

  // Use the real API hook
  const {
    capNhatDeNghiThanhToans,
    isLoading: apiLoading,
    addCapNhatDeNghiThanhToan,
    updateCapNhatDeNghiThanhToan,
    deleteCapNhatDeNghiThanhToan,
    refreshCapNhatDeNghiThanhToans,
    getCapNhatDeNghiThanhToanById
  } = useCapNhatDeNghiThanhToan();

  // Map API data to UI format when data changes
  useEffect(() => {
    // Reset data mapping flag when API starts loading
    if (apiLoading) {
      setIsDataMapped(false);
      setRows([]); // Clear existing rows while loading
      return;
    }

    // Only process data when API is not loading and data is defined
    if (!apiLoading && capNhatDeNghiThanhToans !== undefined) {
      if (capNhatDeNghiThanhToans.length > 0) {
        setRows(capNhatDeNghiThanhToans);
      } else {
        setRows([]);
      }
      // Mark data as mapped after setting rows
      setIsDataMapped(true);
    }
  }, [capNhatDeNghiThanhToans, apiLoading]);

  const createHandleRowClick = useCallback(
    (setSelectedObj: (obj: any) => void, setInputDetails: (details: any[]) => void) => {
      return (row: CapNhatDeNghiThanhToan) => {
        // Simply select the object without calling API
        setSelectedObj(row);

        // Clear input details when selecting a new row
        setInputDetails([]);
        setSelectedDetails([]);
      };
    },
    []
  );

  const handleFormSubmit = useCallback(
    async (data: any) => {
      try {
        setError(null);
        const chiTietData = (data.inputDetails || []).map((row: any, index: number) => ({
          line: index + 1,
          // Budget criteria (only if has_budget is true)
          ns_kd: row.ns_kd || '',
          // Basic fields from the input table
          tien: parseFloat(row.tien || '0'),
          dien_giai: row.dien_giai || '',
          ma_loai_hd: row.ma_loai_hd || '',
          // Entity references - use the UUID from the _data objects if available
          ma_kh: row.ma_kh_data?.uuid || row.ma_kh || '',
          ma_bp: row.ma_bp_data?.uuid || row.ma_bp || '',
          ma_vv: row.ma_vv_data?.uuid || row.ma_vv || '',
          ma_hd: row.ma_hd_data?.uuid || row.ma_hd || '',
          ma_dtt: row.ma_dtt_data?.uuid || row.ma_dtt || '',
          ma_ku: row.ma_ku_data?.uuid || row.ma_ku || '',
          ma_phi: row.ma_phi_data?.uuid || row.ma_phi || '',
          ma_sp: row.ma_sp_data?.uuid || row.ma_sp || '',
          ma_cp0: row.ma_cp0_data?.uuid || row.ma_cp0 || ''
        }));

        // Map form fields to API fields - now using direct API field names
        const mappedData = {
          // Basic info fields - direct mapping since we use API field names
          ma_ngv: data.ma_ngv,
          ns_yn: data.has_budget ? 'Y' : 'N',
          ong_ba: data.ong_ba,
          dia_chi: data.dia_chi,
          dien_giai: data.dien_giai,
          ngay_ct: data.ngay_ct,
          ngay_lct: data.ngay_ct, // Same as ngay_ct
          ty_gia: data.ty_gia || 1,
          status: data.status || '0',

          // OtherTab fields - direct mapping
          so_ct_goc: data.so_ct_goc,
          dien_giai_ct_goc: data.dien_giai_ct_goc,
          email_tq: data.email_tq,
          i_so_ct: data.i_so_ct,

          // Additional fields - now using UUID for foreign currency
          ...(data.ma_nt && { ma_nt: data.ma_nt }), // Only include if a currency is selected

          // Financial fields
          t_tien_nt: data.t_tien_nt || 0,
          t_tien: data.t_tien || 0,
          t_thue_nt: data.t_thue_nt || 0,
          t_thue: data.t_thue || 0,

          // Input table details
          chi_tiet: chiTietData
        };

        if (data.formMode === 'add') {
          const input: CapNhatDeNghiThanhToanInput = mappedData;
          await addCapNhatDeNghiThanhToan(input);
        } else if (data.formMode === 'edit' && data.uuid) {
          const input: Partial<CapNhatDeNghiThanhToanInput> = mappedData;
          await updateCapNhatDeNghiThanhToan(data.uuid, input);
        } else {
          console.warn('⚠️ No valid form mode or missing UUID for edit mode:', {
            formMode: data.formMode,
            uuid: data.uuid
          });
        }

        // Refresh data after successful operation
        await refreshCapNhatDeNghiThanhToans();

        // Close the form after successful submission
        if (handleCloseForm) {
          handleCloseForm();
        }
      } catch (error) {
        setError(error instanceof Error ? error : new Error('Failed to submit form'));
        throw error;
      }
    },
    [addCapNhatDeNghiThanhToan, updateCapNhatDeNghiThanhToan, refreshCapNhatDeNghiThanhToans, handleCloseForm]
  );

  const handleDelete = useCallback(
    async (uuid: string) => {
      try {
        setError(null);
        await deleteCapNhatDeNghiThanhToan(uuid);
        await refreshCapNhatDeNghiThanhToans();
      } catch (error) {
        setError(error instanceof Error ? error : new Error('Failed to delete payment request'));
        throw error;
      }
    },
    [deleteCapNhatDeNghiThanhToan, refreshCapNhatDeNghiThanhToans]
  );

  const handleRefresh = useCallback(async () => {
    try {
      setError(null);
      await refreshCapNhatDeNghiThanhToans();
    } catch (error) {
      setError(error instanceof Error ? error : new Error('Failed to refresh data'));
    }
  }, [refreshCapNhatDeNghiThanhToans]);

  // Calculate the overall loading state: loading if API is loading OR data hasn't been mapped yet
  const isLoading = apiLoading || !isDataMapped;

  return {
    rows,
    selectedDetails,
    isLoading,
    error,
    setRows,
    createHandleRowClick,
    handleFormSubmit,
    handleDelete,
    handleRefresh
  };
};
