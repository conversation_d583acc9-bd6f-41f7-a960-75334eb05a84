import { useState } from 'react';
import { FormMode } from '@/types/form';

/**
 * Hook for managing form state in the payment request update feature
 * @returns Form state and handlers
 */
export const useFormState = () => {
  const [showForm, setShowForm] = useState<boolean>(false);
  const [formMode, setFormMode] = useState<FormMode>('view');
  const [selectedObj, setSelectedObj] = useState<any | null>(null);
  const [currentObj, setCurrentObj] = useState<any | null>(null);
  const [inputDetails, setInputDetails] = useState<any[]>([]);
  const [showDelete, setShowDelete] = useState<boolean>(false);

  const handleCloseForm = () => {
    setShowForm(false);
  };

  const handleOpenEditForm = (obj: any) => {
    setFormMode('edit');
    setCurrentObj(obj);
    setInputDetails(obj.details || []);
    setShowForm(true);
  };

  const handleOpenViewForm = (obj: any) => {
    setFormMode('view');
    setCurrentObj(obj);
    setInputDetails(obj.details || []);
    setShowForm(true);
  };

  const handleOpenAddForm = () => {
    setFormMode('add');
    setCurrentObj(null);
    setInputDetails([]);
    setShowForm(true);
  };

  const handleOpenCopyForm = (obj: any) => {
    setFormMode('add');
    // Copy the object data but remove uuid to create new record
    const { uuid, id, ...objDataWithoutId } = obj;
    setCurrentObj({
      ...objDataWithoutId,
      // Add copy suffix to identifiable fields using API field names
      doc_number: obj.doc_number ? `Copy_${obj.doc_number}` : '',
      dien_giai: obj.dien_giai ? `Copy - ${obj.dien_giai}` : obj.description ? `Copy - ${obj.description}` : '',
      // Map UI fields to API fields for copy
      ma_ngv: obj.supplier_code || obj.ma_ngv,
      ong_ba: obj.supplier_name || obj.ong_ba,
      // Preserve foreign currency if it exists
      ma_nt: obj.ma_nt,
      ty_gia: obj.ty_gia || 1,
      // Clear status to default for new record
      status: '0'
    });
    setInputDetails(obj.details || []);
    setShowForm(true);
  };

  const handleOpenDeleteDialog = () => {
    setShowDelete(true);
  };

  const handleCloseDeleteDialog = () => {
    setShowDelete(false);
  };

  const clearSelection = () => {
    setSelectedObj(null);
    setCurrentObj(null);
    setInputDetails([]);
  };

  return {
    // State
    showForm,
    formMode,
    selectedObj,
    currentObj,
    inputDetails,
    showDelete,

    // Setters
    setShowForm,
    setFormMode,
    setSelectedObj,
    setCurrentObj,
    setInputDetails,
    setShowDelete,

    // Handlers
    handleCloseForm,
    handleOpenEditForm,
    handleOpenViewForm,
    handleOpenAddForm,
    handleOpenCopyForm,
    handleOpenDeleteDialog,
    handleCloseDeleteDialog,
    clearSelection
  };
};
