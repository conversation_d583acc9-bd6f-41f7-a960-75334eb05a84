import React, { useState } from 'react';
import { QuyDoiDonViTinhChung, QuyDoiDonViTinhChungInput } from '@/types/schemas';
import { AritoIcon, AritoForm, BottomBar } from '@/components/custom/arito';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { searchSchema, SearchFormValues } from '../../schemas';
import { DonViTinh } from '@/types/schemas';
import { FormMode } from '@/types/form';
import BasicInfo from './BasicInfo';

interface UnitDialogProps {
  open: boolean;
  mode: FormMode;
  initialData?: QuyDoiDonViTinhChung;
  onClose: () => void;
  onSubmit?: (data: QuyDoiDonViTinhChungInput) => void;
  selectedObj?: QuyDoiDonViTinhChung | null;
  onAddButtonClick?: () => void;
  onEditButtonClick?: () => void;
  onDeleteButtonClick?: () => void;
  onCopyButtonClick?: () => void;
  errors?: {
    dvt?: string;
    dvt0?: string;
    he_so?: string;
  };
}

function UnitDialog({
  open,
  mode,
  initialData,
  onClose,
  onSubmit,
  onAddButtonClick,
  onEditButtonClick,
  onDeleteButtonClick,
  onCopyButtonClick,
  errors = {}
}: UnitDialogProps) {
  const [dvt, setDvt] = useState<DonViTinh | null>(initialData?.dvt_data || null);
  const [dvt0, setDvt0] = useState<DonViTinh | null>(initialData?.dvt0_data || null);

  const handleSubmit = async (data: SearchFormValues) => {
    // Khởi tạo object lỗi
    const validationErrors: {
      dvt?: string;
      dvt0?: string;
      he_so?: string;
    } = {};

    let hasError = false;

    // Validate required fields
    if (!dvt?.uuid) {
      validationErrors.dvt = 'Vui lòng chọn đơn vị tính';
      hasError = true;
    }

    if (!dvt0?.uuid) {
      validationErrors.dvt0 = 'Vui lòng chọn đơn vị tính quy đổi';
      hasError = true;
    }

    let hesoValue = 0;
    if (data.he_so) {
      const hesoString = String(data.he_so).replace(',', '.');
      hesoValue = parseFloat(hesoString);
      if (isNaN(hesoValue)) {
        hesoValue = 0;
      }
    }

    // Ensure he_so is greater than 0
    if (hesoValue <= 0) {
      validationErrors.he_so = 'Hệ số phải lớn hơn 0';
      hasError = true;
    }

    // Nếu có lỗi, cập nhật state errors và dừng xử lý
    if (hasError) {
      // Cập nhật errors thông qua props từ component cha
      if (errors && Object.keys(validationErrors).length > 0) {
        // Truyền lỗi lên component cha
        onClose();
        onSubmit &&
          onSubmit({
            dvt: dvt?.uuid || '',
            dvt0: dvt0?.uuid || '',
            he_so: data.he_so?.toString() || '0',
            status: data.trang_thai || '1',
            _errors: validationErrors // Thêm trường _errors để truyền lỗi
          });
        return;
      }
    }

    const submissionData: QuyDoiDonViTinhChungInput = {
      dvt: dvt?.uuid || '',
      dvt0: dvt0?.uuid || '',
      he_so: hesoValue.toString(), // Convert to string to match API expectations
      status: data.trang_thai || '1'
    };

    if (onSubmit) {
      onSubmit(submissionData);
    }
  };

  const title = mode === 'add' ? 'Mới' : mode === 'edit' ? 'Sửa' : 'Xem';

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title={title}
      maxWidth='lg'
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode={mode}
        hasAritoActionBar={false}
        schema={searchSchema}
        onSubmit={handleSubmit}
        className='w-full md:min-w-[500px] lg:min-w-[600px]'
        headerFields={
          <BasicInfo formMode={mode} dvt={dvt} dvt0={dvt0} setDvt={setDvt} setDvt0={setDvt0} errors={errors} />
        }
        classNameBottomBar='relative w-full flex justify-end gap-2'
        bottomBar={
          <BottomBar
            mode={mode}
            onClose={onClose}
            onAdd={onAddButtonClick}
            onEdit={onEditButtonClick}
            onDelete={onDeleteButtonClick}
            onCopy={onCopyButtonClick}
          />
        }
      />
    </AritoDialog>
  );
}

export default UnitDialog;
